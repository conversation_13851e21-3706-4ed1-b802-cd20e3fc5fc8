#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from PySide6.QtWidgets import (QSplashScreen, QLabel, QProgressBar, 
                               QVBoxLayout, QWidget, QApplication)
from PySide6.QtCore import Qt, QTimer, Signal, QThread
from PySide6.QtGui import QPixmap, QPainter, QFont, QColor, QLinearGradient, QBrush
import time

class LoadingWorker(QThread):
    """加载工作线程 - 只负责进度显示，不创建UI"""
    progress_updated = Signal(int, str)  # 进度值, 状态文本
    create_main_window = Signal()  # 请求创建主窗口的信号
    finished = Signal()

    def __init__(self):
        super().__init__()

    def run(self):
        """执行加载步骤"""
        try:
            # 步骤1: 初始化基础组件
            self.progress_updated.emit(10, "初始化视频处理器...")
            time.sleep(0.3)

            self.progress_updated.emit(20, "加载音频分析模块...")
            time.sleep(0.3)

            self.progress_updated.emit(30, "初始化增强功能...")
            time.sleep(0.3)

            self.progress_updated.emit(40, "加载智能模板处理器...")
            time.sleep(0.3)

            self.progress_updated.emit(50, "初始化OCR处理器...")
            time.sleep(0.3)

            # 步骤2: 请求创建主窗口（在主线程中）
            self.progress_updated.emit(60, "创建主界面组件...")
            self.create_main_window.emit()
            time.sleep(0.5)

            self.progress_updated.emit(70, "初始化时间轴...")
            time.sleep(0.3)

            self.progress_updated.emit(80, "添加默认轨道...")
            time.sleep(0.3)

            self.progress_updated.emit(90, "连接信号和槽...")
            time.sleep(0.3)

            self.progress_updated.emit(100, "启动完成！")
            time.sleep(0.5)

        except Exception as e:
            self.progress_updated.emit(100, f"启动失败: {str(e)}")

        self.finished.emit()

class SplashScreen(QSplashScreen):
    """启动加载界面"""
    
    def __init__(self, main_window_creator=None):
        # 创建一个自定义的启动画面
        pixmap = self.create_splash_pixmap()
        super().__init__(pixmap)

        # 设置窗口属性
        self.setWindowFlags(Qt.WindowType.SplashScreen | Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)

        # 创建UI组件
        self.setup_ui()

        # 保存主窗口创建函数
        self.main_window_creator = main_window_creator

        # 创建加载工作线程
        self.worker = LoadingWorker()
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.create_main_window.connect(self.create_main_window_in_main_thread)
        self.worker.finished.connect(self.loading_finished)

        # 完成信号
        self.loading_complete = False
        self.main_window = None
    
    def create_splash_pixmap(self):
        """创建启动画面的背景图片"""
        width, height = 600, 400
        pixmap = QPixmap(width, height)
        pixmap.fill(Qt.GlobalColor.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 创建渐变背景
        gradient = QLinearGradient(0, 0, 0, height)
        gradient.setColorAt(0, QColor(45, 45, 45))
        gradient.setColorAt(1, QColor(25, 25, 25))
        
        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRoundedRect(0, 0, width, height, 15, 15)
        
        # 绘制标题
        painter.setPen(QColor(255, 255, 255))
        title_font = QFont("Arial", 28, QFont.Weight.Bold)
        painter.setFont(title_font)
        painter.drawText(50, 80, "VIDEOCUT")
        
        # 绘制副标题
        subtitle_font = QFont("Arial", 14)
        painter.setFont(subtitle_font)
        painter.setPen(QColor(200, 200, 200))
        painter.drawText(50, 110, "专业视频编辑器")
        
        # 绘制版本信息
        version_font = QFont("Arial", 10)
        painter.setFont(version_font)
        painter.setPen(QColor(150, 150, 150))
        painter.drawText(50, 130, "版本 2.0.0")
        
        # 绘制装饰线
        painter.setPen(QColor(0, 200, 150, 180))
        painter.drawLine(50, 150, 550, 150)
        
        painter.end()
        return pixmap
    
    def setup_ui(self):
        """设置UI组件"""
        # 创建主widget
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(50, 200, 50, 50)
        
        # 状态标签
        self.status_label = QLabel("正在启动...")
        self.status_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                background: transparent;
            }
        """)
        layout.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #555;
                border-radius: 8px;
                background-color: #333;
                text-align: center;
                color: white;
                font-weight: bold;
                height: 25px;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #00C896, stop:1 #00A080);
                border-radius: 6px;
                margin: 1px;
            }
        """)
        layout.addWidget(self.progress_bar)
        
        # 版权信息
        copyright_label = QLabel("© 2024 VideoEdit Pro. All rights reserved.")
        copyright_label.setStyleSheet("""
            QLabel {
                color: #888;
                font-size: 10px;
                background: transparent;
                margin-top: 20px;
            }
        """)
        layout.addWidget(copyright_label)
        
        # 设置布局
        widget.setStyleSheet("background: transparent;")
        self.setLayout(layout)
    
    def start_loading(self):
        """开始加载过程"""
        self.show()
        self.worker.start()
    
    def update_progress(self, value, message):
        """更新进度"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)

        # 强制刷新显示
        QApplication.processEvents()

    def create_main_window_in_main_thread(self):
        """在主线程中创建主窗口"""
        if self.main_window_creator:
            try:
                self.main_window = self.main_window_creator()
                print("✅ 主窗口创建成功")
            except Exception as e:
                print(f"❌ 主窗口创建失败: {e}")
                self.main_window = None

    def loading_finished(self):
        """加载完成"""
        self.loading_complete = True

        # 延迟一点时间让用户看到100%
        QTimer.singleShot(500, self.close)

    def is_loading_complete(self):
        """检查是否加载完成"""
        return self.loading_complete

    def get_main_window(self):
        """获取创建的主窗口"""
        return self.main_window

class LoadingManager:
    """加载管理器"""
    
    def __init__(self):
        self.splash = None
    
    def show_splash(self, main_window_creator=None):
        """显示启动画面"""
        self.splash = SplashScreen(main_window_creator)
        self.splash.start_loading()
        return self.splash
    
    def hide_splash(self):
        """隐藏启动画面"""
        if self.splash:
            self.splash.close()
            self.splash = None
