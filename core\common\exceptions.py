#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义异常类
"""

from typing import Optional, Any


class VideoEditorError(Exception):
    """视频编辑器基础异常"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, details: Optional[Any] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details
    
    def __str__(self):
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message


class VideoProcessingError(VideoEditorError):
    """视频处理异常"""
    pass


class AudioProcessingError(VideoEditorError):
    """音频处理异常"""
    pass


class TemplateError(VideoEditorError):
    """模板相关异常"""
    pass


class ConfigError(VideoEditorError):
    """配置相关异常"""
    pass


class FileNotFoundError(VideoEditorError):
    """文件未找到异常"""
    pass


class InvalidFormatError(VideoEditorError):
    """无效格式异常"""
    pass


class ExportError(VideoEditorError):
    """导出异常"""
    pass


class TimelineError(VideoEditorError):
    """时间轴异常"""
    pass


class OCRError(VideoEditorError):
    """OCR识别异常"""
    pass


class BeatDetectionError(AudioProcessingError):
    """节拍检测异常"""
    pass


class FFmpegError(VideoProcessingError):
    """FFmpeg相关异常"""
    pass


def handle_exception(func):
    """异常处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except VideoEditorError:
            # 重新抛出自定义异常
            raise
        except Exception as e:
            # 将其他异常包装为自定义异常
            from .logger import error
            error(f"Unexpected error in {func.__name__}: {str(e)}")
            raise VideoEditorError(f"Unexpected error: {str(e)}", "UNEXPECTED_ERROR", e)
    return wrapper
