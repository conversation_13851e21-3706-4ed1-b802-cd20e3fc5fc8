#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量视频处理器
专为理发店视频自动化剪辑设计
"""

import os
import json
import shutil
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from datetime import datetime

from ..templates.template_manager import HairSalonTemplate, HairSalonTemplateManager
from ..media_processing.video_processor import VideoProcessor
from ..common.logger import get_logger
from ..common.exceptions import VideoProcessingError, handle_exception
from ..ai_features.smart_template_processor import SmartTemplateProcessor, AutomationSettings

class BatchProcessingJob:
    """批量处理任务"""
    
    def __init__(self, job_id: str, template_id: str, video_batches: List[Dict]):
        self.job_id = job_id
        self.template_id = template_id
        self.video_batches = video_batches  # 每个批次包含多个视频文件
        self.status = "pending"  # pending, running, completed, failed
        self.progress = 0.0
        self.start_time = None
        self.end_time = None
        self.results = []
        self.errors = []
        self.current_batch = 0
        self.total_batches = len(video_batches)

        # 智能处理相关
        self.smart_processing = False
        self.automation_settings = None
        self.smart_analysis_results = []  # 智能分析结果

class HairSalonBatchProcessor:
    """理发店批量视频处理器"""
    
    def __init__(self, config, template_manager: HairSalonTemplateManager,
                 video_processor: VideoProcessor):
        self.config = config
        self.template_manager = template_manager
        self.video_processor = video_processor

        # 初始化智能模板处理器
        self.smart_processor = SmartTemplateProcessor(config, template_manager, video_processor)

        # 初始化日志器
        self.logger = get_logger('batch_processor')

        # 批量处理设置
        self.max_workers = config.get('batch.max_workers', 2)  # 并发处理数
        self.output_dir = Path(config.get('paths.batch_output_dir',
                                         str(Path.home() / "Videos" / "HairSalon_Output")))
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 任务管理
        self.jobs: Dict[str, BatchProcessingJob] = {}
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
        self._job_lock = threading.Lock()

        # 进度回调
        self.progress_callback = None
        self.completion_callback = None

        # 智能处理设置
        self.enable_smart_processing = True
        self.automation_settings = AutomationSettings()
    
    def create_batch_job(self, template_id: str, video_batches: List[Dict]) -> str:
        """创建批量处理任务
        
        Args:
            template_id: 模版ID
            video_batches: 视频批次列表，每个批次格式：
                {
                    'batch_name': '客户名称_日期',
                    'output_name': '输出文件名',
                    'videos': {
                        'before': '/path/to/before.mp4',
                        'process': '/path/to/process.mp4',
                        'after': '/path/to/after.mp4'
                    },
                    'custom_settings': {
                        'watermark_text': '客户专属文字',
                        'music_volume': 0.7
                    }
                }
        
        Returns:
            任务ID
        """
        job_id = f"job_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.jobs)}"
        
        job = BatchProcessingJob(job_id, template_id, video_batches)
        
        with self._job_lock:
            self.jobs[job_id] = job
        
        print(f"✅ 创建批量处理任务: {job_id}")
        print(f"   模版: {template_id}")
        print(f"   批次数量: {len(video_batches)}")
        
        return job_id
    
    def start_batch_processing(self, job_id: str) -> bool:
        """开始批量处理"""
        with self._job_lock:
            if job_id not in self.jobs:
                print(f"❌ 任务不存在: {job_id}")
                return False
            
            job = self.jobs[job_id]
            if job.status != "pending":
                print(f"❌ 任务状态不正确: {job.status}")
                return False
            
            job.status = "running"
            job.start_time = datetime.now()
        
        # 提交到线程池
        future = self.executor.submit(self._process_batch_job, job_id)
        
        print(f"🚀 开始批量处理: {job_id}")
        return True
    
    def _process_batch_job(self, job_id: str):
        """处理批量任务"""
        job = self.jobs[job_id]
        
        try:
            # 加载模版
            template = self.template_manager.load_template(job.template_id)
            if not template:
                raise Exception(f"无法加载模版: {job.template_id}")
            
            # 处理每个批次
            for batch_index, batch_data in enumerate(job.video_batches):
                job.current_batch = batch_index + 1
                
                try:
                    result = self._process_single_batch(template, batch_data, batch_index)
                    job.results.append(result)
                    
                    if result['success']:
                        print(f"✅ 批次 {batch_index + 1}/{job.total_batches} 完成: {batch_data['batch_name']}")
                    else:
                        print(f"❌ 批次 {batch_index + 1}/{job.total_batches} 失败: {result.get('error', '未知错误')}")
                        job.errors.append(f"批次 {batch_index + 1}: {result.get('error', '未知错误')}")
                
                except Exception as e:
                    error_msg = f"批次 {batch_index + 1} 处理异常: {str(e)}"
                    job.errors.append(error_msg)
                    job.results.append({
                        'success': False,
                        'batch_name': batch_data.get('batch_name', f'批次{batch_index + 1}'),
                        'error': str(e)
                    })
                    print(f"❌ {error_msg}")
                
                # 更新进度
                job.progress = (batch_index + 1) / job.total_batches * 100
                
                # 调用进度回调
                if self.progress_callback:
                    self.progress_callback(job_id, job.progress, job.current_batch, job.total_batches)
            
            # 任务完成
            job.status = "completed"
            job.end_time = datetime.now()
            
            success_count = sum(1 for r in job.results if r['success'])
            print(f"🎉 批量处理完成: {job_id}")
            print(f"   成功: {success_count}/{job.total_batches}")
            print(f"   失败: {len(job.errors)}")
            
            # 调用完成回调
            if self.completion_callback:
                self.completion_callback(job_id, job.status, success_count, len(job.errors))
        
        except Exception as e:
            job.status = "failed"
            job.end_time = datetime.now()
            error_msg = f"批量处理任务失败: {str(e)}"
            job.errors.append(error_msg)
            print(f"❌ {error_msg}")
            
            if self.completion_callback:
                self.completion_callback(job_id, job.status, 0, len(job.errors))
    
    def _process_single_batch(self, template: HairSalonTemplate, 
                            batch_data: Dict, batch_index: int) -> Dict[str, Any]:
        """处理单个批次"""
        batch_name = batch_data.get('batch_name', f'批次{batch_index + 1}')
        output_name = batch_data.get('output_name', f'{batch_name}_output.mp4')
        videos = batch_data.get('videos', {})
        custom_settings = batch_data.get('custom_settings', {})
        
        print(f"🎬 处理批次: {batch_name}")
        
        try:
            # 创建临时工作目录
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                # 1. 应用模版到视频文件
                template_result = self.template_manager.apply_template_to_videos(
                    template, videos
                )
                
                if not template_result['success']:
                    return {
                        'success': False,
                        'batch_name': batch_name,
                        'error': f"模版应用失败: {', '.join(template_result['errors'])}"
                    }
                
                # 2. 处理视频片段
                processed_segments = []
                for segment_info in template_result['segments']:
                    if segment_info['applied'] and segment_info['video_path']:
                        # 处理单个片段
                        segment_result = self._process_video_segment(
                            segment_info, template, temp_path, custom_settings
                        )
                        if segment_result:
                            processed_segments.append(segment_result)
                
                if not processed_segments:
                    return {
                        'success': False,
                        'batch_name': batch_name,
                        'error': "没有有效的视频片段"
                    }
                
                # 3. 合并视频片段
                merged_video_path = temp_path / "merged_video.mp4"
                segment_paths = [seg['output_path'] for seg in processed_segments]
                
                success = self.video_processor.merge_videos(segment_paths, str(merged_video_path))
                if not success:
                    return {
                        'success': False,
                        'batch_name': batch_name,
                        'error': "视频合并失败"
                    }
                
                # 4. 添加背景音乐
                if template_result['music']:
                    video_with_music = temp_path / "video_with_music.mp4"
                    success = self._add_background_music(
                        str(merged_video_path), template_result['music'][0], 
                        str(video_with_music), custom_settings
                    )
                    if success:
                        merged_video_path = video_with_music
                
                # 5. 添加水印/署名
                if template_result['watermarks']:
                    video_with_watermark = temp_path / "video_with_watermark.mp4"
                    success = self._add_watermarks(
                        str(merged_video_path), template_result['watermarks'],
                        str(video_with_watermark), custom_settings
                    )
                    if success:
                        merged_video_path = video_with_watermark
                
                # 6. 应用滤镜和效果
                if template_result['filters']:
                    final_video = temp_path / "final_video.mp4"
                    success = self._apply_filters(
                        str(merged_video_path), template_result['filters'],
                        str(final_video)
                    )
                    if success:
                        merged_video_path = final_video
                
                # 7. 输出最终视频
                output_path = self.output_dir / output_name
                shutil.copy2(merged_video_path, output_path)
                
                return {
                    'success': True,
                    'batch_name': batch_name,
                    'output_path': str(output_path),
                    'segments_processed': len(processed_segments),
                    'file_size': output_path.stat().st_size
                }
        
        except Exception as e:
            return {
                'success': False,
                'batch_name': batch_name,
                'error': str(e)
            }
    
    def _process_video_segment(self, segment_info: Dict, template: HairSalonTemplate,
                             temp_path: Path, custom_settings: Dict) -> Optional[Dict]:
        """处理单个视频片段"""
        try:
            input_path = segment_info['video_path']
            segment_type = segment_info['type']
            duration = segment_info['duration']
            
            # 获取视频信息
            video_info = self.video_processor.get_video_info(input_path)
            
            # 计算裁剪时间
            if video_info.duration > duration:
                # 视频太长，从中间部分截取
                start_time = (video_info.duration - duration) / 2
                end_time = start_time + duration
            else:
                # 视频太短或刚好，使用全部
                start_time = 0
                end_time = video_info.duration
            
            # 输出路径
            output_path = temp_path / f"segment_{segment_type}.mp4"
            
            # 裁剪视频
            success = self.video_processor.cut_video(
                input_path, str(output_path), start_time, end_time
            )
            
            if success:
                return {
                    'type': segment_type,
                    'output_path': str(output_path),
                    'duration': end_time - start_time
                }
            else:
                print(f"❌ 片段处理失败: {segment_type}")
                return None
        
        except Exception as e:
            print(f"❌ 片段处理异常 {segment_info['type']}: {e}")
            return None
    
    def _add_background_music(self, video_path: str, music_config: Dict,
                            output_path: str, custom_settings: Dict) -> bool:
        """添加背景音乐"""
        try:
            music_path = music_config.get('audio_path', '')
            if not music_path or not os.path.exists(music_path):
                print("⚠️ 背景音乐文件不存在，跳过")
                return False
            
            # 获取自定义音量设置
            volume = custom_settings.get('music_volume', music_config.get('volume', 0.8))
            
            # 混合音频
            audio_tracks = [{
                'path': music_path,
                'volume': volume,
                'start_time': music_config.get('start_time', 0.0)
            }]
            
            return self.video_processor.mix_multiple_audio_tracks(
                video_path, audio_tracks, output_path
            )
        
        except Exception as e:
            print(f"❌ 添加背景音乐失败: {e}")
            return False
    
    def _add_watermarks(self, video_path: str, watermarks_config: List[Dict],
                       output_path: str, custom_settings: Dict) -> bool:
        """添加水印"""
        try:
            # 目前只处理第一个水印（文字水印）
            watermark = watermarks_config[0]
            
            if watermark['content_type'] != 'text':
                print("⚠️ 暂不支持图片水印，跳过")
                return False
            
            # 获取自定义水印文字
            watermark_text = custom_settings.get('watermark_text', watermark['content'])
            
            # 创建临时文字图片作为水印
            # 这里简化处理，实际应该生成文字图片
            print(f"📝 添加水印: {watermark_text}")
            
            # 暂时复制原视频（实际应该添加水印）
            shutil.copy2(video_path, output_path)
            return True
        
        except Exception as e:
            print(f"❌ 添加水印失败: {e}")
            return False
    
    def _apply_filters(self, video_path: str, filters_config: List[Dict],
                      output_path: str) -> bool:
        """应用滤镜"""
        try:
            # 暂时简化处理，复制原视频
            # 实际应该根据滤镜配置应用各种效果
            print("🎨 应用滤镜效果")
            shutil.copy2(video_path, output_path)
            return True
        
        except Exception as e:
            print(f"❌ 应用滤镜失败: {e}")
            return False
    
    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        with self._job_lock:
            if job_id not in self.jobs:
                return None
            
            job = self.jobs[job_id]
            return {
                'job_id': job.job_id,
                'template_id': job.template_id,
                'status': job.status,
                'progress': job.progress,
                'current_batch': job.current_batch,
                'total_batches': job.total_batches,
                'start_time': job.start_time.isoformat() if job.start_time else None,
                'end_time': job.end_time.isoformat() if job.end_time else None,
                'success_count': sum(1 for r in job.results if r['success']),
                'error_count': len(job.errors),
                'results': job.results,
                'errors': job.errors
            }
    
    def cancel_job(self, job_id: str) -> bool:
        """取消任务"""
        with self._job_lock:
            if job_id not in self.jobs:
                return False
            
            job = self.jobs[job_id]
            if job.status == "running":
                job.status = "cancelled"
                print(f"⏹️ 任务已取消: {job_id}")
                return True
            
            return False
    
    def cleanup_completed_jobs(self, keep_days: int = 7):
        """清理已完成的任务"""
        with self._job_lock:
            current_time = datetime.now()
            jobs_to_remove = []
            
            for job_id, job in self.jobs.items():
                if job.status in ["completed", "failed", "cancelled"] and job.end_time:
                    days_passed = (current_time - job.end_time).days
                    if days_passed > keep_days:
                        jobs_to_remove.append(job_id)
            
            for job_id in jobs_to_remove:
                del self.jobs[job_id]
                print(f"🗑️ 清理任务: {job_id}")
    
    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def set_completion_callback(self, callback):
        """设置完成回调函数"""
        self.completion_callback = callback
    
    def stop_all_jobs(self):
        """停止所有任务"""
        try:
            # 取消所有进行中的任务
            with self._job_lock:
                for job_id, job in self.jobs.items():
                    if job.status in ["pending", "running"]:
                        job.status = "cancelled"
                        job.errors.append("程序关闭")
            
            # 关闭线程池
            if hasattr(self, 'executor'):
                self.executor.shutdown(wait=False)
                
            print("🛑 已停止所有批量处理任务")
        except Exception as e:
            print(f"停止批量处理任务时出错: {e}")

    @handle_exception
    def create_smart_batch_job(self, template_id: str, video_batches: List[Dict],
                              automation_settings: Optional[AutomationSettings] = None) -> str:
        """创建智能批量处理任务"""
        try:
            self.logger.info(f"Creating smart batch job with template: {template_id}")

            # 使用智能处理设置
            if automation_settings:
                self.automation_settings = automation_settings

            # 创建基础任务
            job_id = self.create_batch_job(template_id, video_batches)

            # 标记为智能处理任务
            with self._job_lock:
                if job_id in self.jobs:
                    self.jobs[job_id].smart_processing = True
                    self.jobs[job_id].automation_settings = self.automation_settings

            self.logger.info(f"Smart batch job created: {job_id}")
            return job_id

        except Exception as e:
            self.logger.error(f"Failed to create smart batch job: {str(e)}")
            raise VideoProcessingError(f"Smart batch job creation failed: {str(e)}")

    @handle_exception
    def process_batch_with_intelligence(self, job_id: str) -> bool:
        """使用智能增强功能处理批量任务"""
        try:
            with self._job_lock:
                if job_id not in self.jobs:
                    raise VideoProcessingError(f"Job not found: {job_id}")

                job = self.jobs[job_id]
                if job.status != "pending":
                    raise VideoProcessingError(f"Invalid job status: {job.status}")

                job.status = "running"
                job.start_time = datetime.now()

            self.logger.info(f"Starting intelligent batch processing: {job_id}")

            # 加载模板
            template = self.template_manager.load_template(job.template_id)
            if not template:
                raise VideoProcessingError(f"Template not found: {job.template_id}")

            # 处理每个批次
            for batch_index, batch_data in enumerate(job.video_batches):
                job.current_batch = batch_index + 1

                try:
                    # 使用智能处理
                    result = self._process_single_batch_smart(
                        template, batch_data, batch_index, job.automation_settings
                    )

                    job.results.append(result)

                    if result['success']:
                        self.logger.info(f"Smart batch {batch_index + 1}/{job.total_batches} completed: {batch_data['batch_name']}")
                    else:
                        self.logger.error(f"Smart batch {batch_index + 1}/{job.total_batches} failed: {result.get('error', 'Unknown error')}")
                        job.errors.append(f"Batch {batch_index + 1}: {result.get('error', 'Unknown error')}")

                except Exception as e:
                    error_msg = f"Smart batch {batch_index + 1} processing exception: {str(e)}"
                    job.errors.append(error_msg)
                    job.results.append({
                        'success': False,
                        'batch_name': batch_data.get('batch_name', f'Batch{batch_index + 1}'),
                        'error': str(e)
                    })
                    self.logger.error(error_msg)

                # 更新进度
                job.progress = (batch_index + 1) / job.total_batches * 100
                if self.progress_callback:
                    self.progress_callback(job_id, job.progress)

            # 完成处理
            job.status = "completed"
            job.end_time = datetime.now()

            success_count = sum(1 for result in job.results if result['success'])
            self.logger.info(f"Smart batch processing completed: {success_count}/{len(job.results)} successful")

            if self.completion_callback:
                self.completion_callback(job_id, job.status)

            return True

        except Exception as e:
            # 标记任务失败
            with self._job_lock:
                if job_id in self.jobs:
                    self.jobs[job_id].status = "failed"
                    self.jobs[job_id].errors.append(str(e))

            self.logger.error(f"Smart batch processing failed: {str(e)}")
            return False

    def _process_single_batch_smart(self, template: HairSalonTemplate,
                                   batch_data: Dict, batch_index: int,
                                   automation_settings: AutomationSettings) -> Dict[str, Any]:
        """使用智能功能处理单个批次"""
        batch_name = batch_data.get('batch_name', f'Batch{batch_index + 1}')
        videos = batch_data.get('videos', {})
        custom_settings = batch_data.get('custom_settings', {})

        try:
            self.logger.info(f"Processing smart batch: {batch_name}")

            # 验证视频文件
            for segment_type, video_path in videos.items():
                if not os.path.exists(video_path):
                    return {
                        'success': False,
                        'batch_name': batch_name,
                        'error': f"Video file not found: {video_path}"
                    }

            # 生成输出路径
            output_filename = batch_data.get('output_name', f"{batch_name}_智能剪辑.mp4")
            output_path = self.output_dir / output_filename

            # 使用智能模板处理器
            success = self.smart_processor.apply_smart_template(
                template, videos, str(output_path), automation_settings
            )

            if success:
                return {
                    'success': True,
                    'batch_name': batch_name,
                    'output_path': str(output_path),
                    'processing_type': 'smart',
                    'automation_features': {
                        'auto_sync': automation_settings.enable_auto_sync,
                        'smart_trimming': automation_settings.enable_smart_trimming,
                        'text_extraction': automation_settings.enable_text_extraction,
                        'audio_analysis': automation_settings.enable_audio_analysis
                    }
                }
            else:
                return {
                    'success': False,
                    'batch_name': batch_name,
                    'error': "Smart template processing failed"
                }

        except Exception as e:
            self.logger.error(f"Smart batch processing failed for {batch_name}: {str(e)}")
            return {
                'success': False,
                'batch_name': batch_name,
                'error': str(e)
            }

    def set_automation_settings(self, settings: AutomationSettings):
        """设置自动化处理参数"""
        self.automation_settings = settings
        self.logger.info("Automation settings updated")

    def enable_smart_features(self, enable: bool = True):
        """启用/禁用智能功能"""
        self.enable_smart_processing = enable
        self.logger.info(f"Smart processing {'enabled' if enable else 'disabled'}")

    def get_smart_processing_report(self, job_id: str) -> Optional[Dict[str, Any]]:
        """获取智能处理报告"""
        try:
            with self._job_lock:
                if job_id not in self.jobs:
                    return None

                job = self.jobs[job_id]

                # 统计智能处理结果
                smart_results = [r for r in job.results if r.get('processing_type') == 'smart']

                if not smart_results:
                    return None

                report = {
                    'job_id': job_id,
                    'template_id': job.template_id,
                    'total_batches': len(job.results),
                    'smart_processed': len(smart_results),
                    'success_rate': sum(1 for r in smart_results if r['success']) / len(smart_results),
                    'automation_features_used': {},
                    'processing_time': (job.end_time - job.start_time).total_seconds() if job.end_time and job.start_time else 0,
                    'errors': job.errors
                }

                # 统计使用的自动化功能
                for result in smart_results:
                    if result.get('automation_features'):
                        for feature, enabled in result['automation_features'].items():
                            if feature not in report['automation_features_used']:
                                report['automation_features_used'][feature] = 0
                            if enabled:
                                report['automation_features_used'][feature] += 1

                return report

        except Exception as e:
            self.logger.error(f"Failed to generate smart processing report: {str(e)}")
            return None