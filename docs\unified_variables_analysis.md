# 统一变量管理分析

## 🎯 **已识别的频繁获取变量**

基于对代码的深入分析，我发现了大量可以统一管理的频繁访问变量：

## 📊 **1. UI界面常量**

### **颜色系统**
```python
# 当前分散在各个文件中的颜色定义
HEADER_BG = "#333333"           # dialog_styles.py
CONTENT_BG = "#4B4D52"          # dialog_styles.py  
TEXT_COLOR = "#B6ECFF"          # dialog_styles.py
ACCENT_COLOR = "#00C896"        # 多个文件
TRACK_BG = "#333333"            # main_window_simplified.py
TIMELINE_BG = "#505C70"         # main_window_simplified.py
```

**统一后**：
```python
# 通过 UnifiedConstantsManager 统一管理
colors = constants.get_theme_colors()
primary_bg = constants.get_color('primary_bg')
```

### **尺寸配置**
```python
# 当前硬编码的尺寸
track_height = 64               # 多个文件
track_spacing = 12              # 多个文件
media_library_width = 528       # main_window_simplified.py
video_preview_width = 1024      # main_window_simplified.py
timeline_ruler_height = 48      # multi_track_timeline.py
```

**统一后**：
```python
# 通过常量管理器获取
track_height = constants.get_dimension('track_height')
panel_sizes = constants.get_panel_sizes()  # (528, 1024, 368)
```

## 🎮 **2. 时间轴配置**

### **缩放和播放参数**
```python
# 当前分散的配置
_zoom_levels = [1, 2, 5, 10, 25, 50, 100, 200, 400, 800]  # multi_track_timeline.py
_playback_speeds = [0.25, 0.5, 0.75, 1.0, 1.25, 1.5, 2.0, 4.0]  # 同上
_pixels_per_second = 100        # timeline.py, multi_track_timeline.py
_snap_threshold = 10            # 多个文件
```

**统一后**：
```python
# 通过常量管理器统一获取
zoom_levels = constants.get_zoom_levels()
playback_speeds = constants.get_playback_speeds()
current_pps = state_manager.get_current_zoom_pixels_per_second()
```

### **缩略图配置**
```python
# 当前硬编码的缩略图参数
min_thumb_width = 80            # timeline.py
max_thumb_width = 160           # timeline.py
thumbnail_width = 120           # README.md
```

**统一后**：
```python
# 通过配置获取
thumb_config = constants.timeline_config
min_width = thumb_config.min_thumbnail_width
```

## 🎵 **3. 媒体处理配置**

### **格式支持**
```python
# 当前分散的格式定义
video_formats = ['.mp4', '.avi', '.mov', '.mkv']  # 多个文件
audio_formats = ['.mp3', '.wav', '.aac', '.m4a']  # 多个文件
default_fps = 30                # config.py, swanksalon_engine.py
sample_rate = 44100             # 多个文件
```

**统一后**：
```python
# 统一格式管理
video_formats = constants.get_supported_formats('video')
media_params = constants.get_default_media_params()
```

### **质量预设**
```python
# 当前硬编码的质量配置
preview_quality_smooth = 0.5    # 需要统一
preview_quality_high = 0.75     # 需要统一
cache_size = "1GB"              # config.py
```

**统一后**：
```python
# 质量预设管理
scale = constants.get_quality_scale(QualityPreset.SMOOTH)
cache_config = constants.get_cache_config()
```

## 🖥️ **4. 窗口和视口状态**

### **窗口状态**
```python
# 当前分散的窗口状态
window_width = 1920             # main_window_simplified.py
window_height = 1080            # main_window_simplified.py
is_maximized = False            # window_controller.py
has_notch = False               # main_window_simplified.py
```

**统一后**：
```python
# 通过状态管理器统一
window_state = state_manager.get_window_state()
is_maximized = window_state.is_maximized
```

### **视口状态**
```python
# 当前分散的视口状态
timeline_scroll_x = 0           # 多个文件
timeline_zoom_level = 6         # multi_track_timeline.py
preview_zoom = 1.0              # 需要统一
```

**统一后**：
```python
# 统一视口管理
viewport = state_manager.get_viewport_state()
scroll_x = viewport.timeline_scroll_x
```

## 🎯 **5. 选择和交互状态**

### **选择状态**
```python
# 当前分散的选择状态
selected_tracks = []            # 多个文件
selected_media_items = []       # 多个文件
selection_start = -1.0          # timeline.py
```

**统一后**：
```python
# 统一选择管理
selection = state_manager.get_selection_state()
selected_tracks = selection.selected_tracks
```

### **拖拽状态**
```python
# 当前分散的拖拽状态
is_dragging = False             # 多个文件
drag_type = ""                  # 需要统一
drag_position = (0, 0)          # 需要统一
```

**统一后**：
```python
# 统一拖拽管理
is_dragging = state_manager.is_dragging()
drag_state = state_manager.drag_state
```

## ⚡ **6. 性能和缓存**

### **性能配置**
```python
# 当前分散的性能配置
max_threads = 4                 # config.py
frame_cache_size = 50           # 需要统一
thumbnail_cache_size = 100      # timeline.py
hardware_acceleration = True    # 多个文件
```

**统一后**：
```python
# 统一性能管理
thread_config = constants.get_thread_config()
cache_config = constants.get_cache_config()
```

### **实时性能指标**
```python
# 当前缺失的性能监控
current_fps = 0.0               # 需要添加
memory_usage = 0.0              # 需要添加
cpu_usage = 0.0                 # 需要添加
```

**统一后**：
```python
# 实时性能监控
metrics = state_manager.get_performance_metrics()
current_fps = metrics.fps
```

## 🔧 **7. 应用配置**

### **路径配置**
```python
# 当前分散的路径配置
temp_dir = "./temp"             # config.py
last_import_dir = str(Path.home())  # config.py
ffmpeg_path = "ffmpeg"          # config.py
```

### **主题配置**
```python
# 当前硬编码的主题
theme = "dark"                  # config.py
language = "zh-CN"              # config.py
```

## 📈 **统一管理的优势**

### **1. 性能提升**
- **缓存机制**：频繁访问的计算值被缓存
- **减少重复计算**：时间轴宽度、轨道高度等动态计算
- **内存优化**：避免重复存储相同配置

### **2. 一致性保证**
- **单一数据源**：所有组件使用相同的配置值
- **同步更新**：配置变化自动同步到所有使用者
- **类型安全**：统一的数据类型和验证

### **3. 维护便利**
- **集中管理**：所有常量在一个地方定义
- **易于修改**：修改一处，全局生效
- **配置持久化**：支持保存和加载用户配置

### **4. 扩展性**
- **主题系统**：轻松切换深色/浅色主题
- **多语言支持**：统一的本地化管理
- **插件系统**：第三方插件可以访问统一配置

## 🚀 **使用示例**

### **替换前（分散管理）**
```python
# 在不同文件中重复定义
class TimelineWidget:
    def __init__(self):
        self.track_height = 64
        self.track_spacing = 12
        self.pixels_per_second = 100

class TrackWidget:
    def __init__(self):
        self.track_height = 64  # 重复定义
        self.background_color = "#333333"
```

### **替换后（统一管理）**
```python
# 统一获取配置
class TimelineWidget:
    def __init__(self):
        self.track_height = constants.get_dimension('track_height')
        self.track_spacing = constants.get_dimension('track_spacing')
        self.pixels_per_second = state_manager.get_current_zoom_pixels_per_second()

class TrackWidget:
    def __init__(self):
        self.track_height = constants.get_dimension('track_height')
        self.background_color = constants.get_color('track_bg')
```

## 📋 **实施计划**

1. **第一阶段**：UI常量统一（颜色、尺寸）
2. **第二阶段**：时间轴配置统一（缩放、播放）
3. **第三阶段**：状态管理统一（窗口、视口、选择）
4. **第四阶段**：性能监控和缓存优化
5. **第五阶段**：配置持久化和主题系统

这个统一管理系统将大大提升代码的可维护性和性能！
