﻿#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from pathlib import Path
from typing import Dict, Any
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QSlider, QScrollArea
)
from PySide6.QtCore import Qt, Signal, QObject
from .timeline_components import TimelineRuler
from .track_components import TrackLabel
from .track_widget import TrackWidget
# 🔧 新设计：不再需要统一播放头管理器
# from gui.components.unified_playhead import UnifiedPlayheadManager


class TimelineGlobalParams(QObject):
    """时间轴全局参数管理器 - 统一管理所有时间轴相关参数"""

    # 参数变化信号
    position_changed = Signal(float)  # 当前播放位置变化
    duration_changed = Signal(float)  # 总时长变化
    zoom_changed = Signal(float)  # 缩放级别变化
    playback_speed_changed = Signal(float)  # 播放速度变化
    track_count_changed = Signal(int)  # 轨道数量变化

    def __init__(self):
        super().__init__()

        # 时间轴核心参数
        self._current_position = 0.0  # 当前播放位置（秒）
        self._total_duration = 30.0   # 总时长（秒）
        self._pixels_per_second = 100  # 像素比例（每秒像素数）

        # 缩放相关参数
        self._zoom_levels = [1, 2, 5, 10, 25, 50, 100, 200, 400, 800]
        self._current_zoom_index = 6  # 默认100像素/秒

        # 播放相关参数
        self._playback_speeds = [0.25, 0.5, 0.75, 1.0, 1.25, 1.5, 2.0, 4.0]
        self._current_speed_index = 3  # 默认1.0x
        self._current_playback_speed = 1.0
        self._is_playing = False

        # 轨道相关参数
        self._track_count = 0
        self._track_height = 64
        self._track_spacing = 12
        self._track_label_width = 128
        self._track_content_width = 1792
        self._ruler_height = 48

        # 磁性吸附参数
        self._snap_enabled = True
        self._snap_threshold = 10  # 像素
        self._grid_snap_interval = 0.5  # 秒

        # 更新状态标志
        self._updating_position = False

        print("✅ 时间轴全局参数管理器初始化完成")

    # 当前播放位置相关方法
    @property
    def current_position(self) -> float:
        return self._current_position

    def set_current_position(self, position: float, emit_signal: bool = True):
        """设置当前播放位置"""
        if self._updating_position:
            return

        old_position = self._current_position
        self._current_position = max(0.0, min(position, self._total_duration))

        if emit_signal and abs(old_position - self._current_position) > 0.001:
            self.position_changed.emit(self._current_position)
            print(f"🎯 全局位置更新: {old_position:.3f}s -> {self._current_position:.3f}s")

    # 总时长相关方法
    @property
    def total_duration(self) -> float:
        return self._total_duration

    def set_total_duration(self, duration: float, emit_signal: bool = True):
        """设置总时长"""
        old_duration = self._total_duration
        self._total_duration = max(30.0, duration)  # 最小30秒

        if emit_signal and abs(old_duration - self._total_duration) > 0.001:
            self.duration_changed.emit(self._total_duration)
            print(f"⏱️ 全局时长更新: {old_duration:.1f}s -> {self._total_duration:.1f}s")

    # 像素比例相关方法
    @property
    def pixels_per_second(self) -> float:
        return self._pixels_per_second

    def set_pixels_per_second(self, pixels_per_second: float, emit_signal: bool = True):
        """设置像素比例"""
        old_pixels = self._pixels_per_second
        self._pixels_per_second = max(1.0, pixels_per_second)

        if emit_signal and abs(old_pixels - self._pixels_per_second) > 0.001:
            self.zoom_changed.emit(self._pixels_per_second)
            print(f"🔍 全局缩放更新: {old_pixels:.1f}px/s -> {self._pixels_per_second:.1f}px/s")

    # 缩放级别相关方法
    @property
    def zoom_levels(self) -> list:
        return self._zoom_levels.copy()

    @property
    def current_zoom_index(self) -> int:
        return self._current_zoom_index

    def set_zoom_index(self, index: int, emit_signal: bool = True):
        """设置缩放级别索引"""
        if 0 <= index < len(self._zoom_levels):
            self._current_zoom_index = index
            new_pixels = self._zoom_levels[index]
            self.set_pixels_per_second(new_pixels, emit_signal)

    def zoom_in(self):
        """放大"""
        if self._current_zoom_index < len(self._zoom_levels) - 1:
            self.set_zoom_index(self._current_zoom_index + 1)

    def zoom_out(self):
        """缩小"""
        if self._current_zoom_index > 0:
            self.set_zoom_index(self._current_zoom_index - 1)

    # 播放速度相关方法
    @property
    def playback_speeds(self) -> list:
        return self._playback_speeds.copy()

    @property
    def current_playback_speed(self) -> float:
        return self._current_playback_speed

    def set_playback_speed_index(self, index: int, emit_signal: bool = True):
        """设置播放速度索引"""
        if 0 <= index < len(self._playback_speeds):
            self._current_speed_index = index
            old_speed = self._current_playback_speed
            self._current_playback_speed = self._playback_speeds[index]

            if emit_signal and abs(old_speed - self._current_playback_speed) > 0.001:
                self.playback_speed_changed.emit(self._current_playback_speed)
                print(f"🚀 全局播放速度更新: {old_speed:.2f}x -> {self._current_playback_speed:.2f}x")

    def increase_playback_speed(self):
        """增加播放速度"""
        if self._current_speed_index < len(self._playback_speeds) - 1:
            self.set_playback_speed_index(self._current_speed_index + 1)

    def decrease_playback_speed(self):
        """减少播放速度"""
        if self._current_speed_index > 0:
            self.set_playback_speed_index(self._current_speed_index - 1)

    # 播放状态相关方法
    @property
    def is_playing(self) -> bool:
        return self._is_playing

    def set_playing_state(self, playing: bool):
        """设置播放状态"""
        self._is_playing = playing
        print(f"▶️ 全局播放状态: {'播放中' if playing else '已暂停'}")

    # 轨道相关方法
    @property
    def track_count(self) -> int:
        return self._track_count

    def set_track_count(self, count: int, emit_signal: bool = True):
        """设置轨道数量"""
        old_count = self._track_count
        self._track_count = max(0, count)

        if emit_signal and old_count != self._track_count:
            self.track_count_changed.emit(self._track_count)
            print(f"🎵 全局轨道数量更新: {old_count} -> {self._track_count}")

    @property
    def track_height(self) -> int:
        return self._track_height

    @property
    def track_spacing(self) -> int:
        return self._track_spacing

    @property
    def track_label_width(self) -> int:
        return self._track_label_width

    @property
    def track_content_width(self) -> int:
        return self._track_content_width

    @property
    def ruler_height(self) -> int:
        return self._ruler_height

    # 磁性吸附相关方法
    @property
    def snap_enabled(self) -> bool:
        return self._snap_enabled

    def set_snap_enabled(self, enabled: bool):
        """设置磁性吸附开关"""
        self._snap_enabled = enabled
        print(f"🧲 磁性吸附: {'启用' if enabled else '禁用'}")

    @property
    def snap_threshold(self) -> int:
        return self._snap_threshold

    @property
    def grid_snap_interval(self) -> float:
        return self._grid_snap_interval

    # 更新状态控制
    def set_updating_position(self, updating: bool):
        """设置位置更新状态标志"""
        self._updating_position = updating

    # 计算相关方法
    def time_to_pixels(self, time_seconds: float) -> int:
        """时间转换为像素位置"""
        return int(time_seconds * self._pixels_per_second)

    def pixels_to_time(self, pixels: int) -> float:
        """像素位置转换为时间"""
        return pixels / self._pixels_per_second

    def get_timeline_width(self) -> int:
        """获取时间轴总宽度（像素）"""
        return int(self._total_duration * self._pixels_per_second)

    def get_total_track_height(self) -> int:
        """获取所有轨道的总高度"""
        if self._track_count == 0:
            return 0
        return self._track_count * self._track_height + (self._track_count - 1) * self._track_spacing

    # 磁性吸附计算
    def apply_snap(self, time_position: float) -> float:
        """应用磁性吸附"""
        if not self._snap_enabled:
            return time_position

        # 网格吸附
        grid_time = round(time_position / self._grid_snap_interval) * self._grid_snap_interval
        if abs(time_position - grid_time) <= (self._snap_threshold / self._pixels_per_second):
            return grid_time

        return time_position

class TimelineScrollContent(QWidget):
    """时间轴滚动内容区域"""

    position_changed = Signal(float)

    def __init__(self, global_params: TimelineGlobalParams):
        super().__init__()
        self.global_params = global_params
        self.setMinimumSize(3000, 400)
        self.setStyleSheet("background-color: #505C70;")

        # 连接全局参数变化信号
        self.global_params.zoom_changed.connect(self.on_zoom_changed)
        self.global_params.duration_changed.connect(self.on_duration_changed)

        # 🔧 新设计：在滚动内容中创建播放头组件（移动位置方式）
        from .timeline_components import ScrollContentPlayhead
        self.scroll_playhead = ScrollContentPlayhead(self.global_params, self)

        # 初始化尺寸
        self.update_size()

    def update_size(self):
        """根据全局参数更新尺寸"""
        width = self.global_params.get_timeline_width()
        self.setMinimumWidth(max(3000, width))

        # 更新播放头组件几何
        if hasattr(self, 'scroll_playhead'):
            self.scroll_playhead.setGeometry(self.rect())
            self.scroll_playhead.raise_()  # 确保在最上层

    def on_zoom_changed(self, pixels_per_second: float):
        """响应缩放变化"""
        self.update_size()
        self.update()

    def on_duration_changed(self, duration: float):
        """响应时长变化"""
        self.update_size()

    def set_drag_position(self, x_pos: int):
        """设置拖动预览位置"""
        if hasattr(self, 'scroll_playhead_overlay') and self.scroll_playhead_overlay:
            self.scroll_playhead_overlay.set_drag_position(x_pos)

    def clear_drag_position(self):
        """清除拖动预览位置"""
        if hasattr(self, 'scroll_playhead_overlay') and self.scroll_playhead_overlay:
            self.scroll_playhead_overlay.clear_drag_position()
        self.update()

    def set_position(self, position: float):
        """设置播放位置"""
        self.global_params.set_current_position(position)
        self.position_changed.emit(position)

    def resizeEvent(self, event):
        """处理窗口大小变化事件"""
        super().resizeEvent(event)

        # 🔧 新设计：更新滚动播放头组件几何
        if hasattr(self, 'scroll_playhead'):
            self.scroll_playhead.setGeometry(self.rect())
            self.scroll_playhead.raise_()
            self.scroll_playhead.show()

class MultiTrackTimeline(QWidget):
    """多轨道时间轴"""

    position_changed = Signal(float)
    selection_changed = Signal(float, float)
    media_dropped = Signal(int, dict)
    media_clicked = Signal(str, float, str)  # file_path, start_time, media_type
    playback_started = Signal()
    playback_stopped = Signal()

    def __init__(self, video_processor=None, global_params=None):
        super().__init__()
        self.video_processor = video_processor

        # 创建或使用全局参数管理器
        if global_params is None:
            self.global_params = TimelineGlobalParams()
            self._owns_global_params = True
        else:
            self.global_params = global_params
            self._owns_global_params = False

        # 存储轨道数据和组件
        self.tracks = []  # 存储轨道数据
        self.track_widgets = []  # 存储轨道组件
        self.timeline_ruler = None  # 时间标尺
        self.scroll_content = None  # 滚动内容区域

        # 拖动状态
        self.dragging_playhead = False

        # 滚动区域引用，用于自动滚动
        self.scroll_area = None

        # 🔧 修复：初始化位置更新标志
        self._updating_position = False

        # 🔧 添加标志：是否已经进行过首次自动缩放
        self.has_auto_scaled = False

        # 🔧 新设计：不再需要统一播放头管理器
        # self.unified_playhead = None

        # 连接全局参数变化信号
        self.global_params.position_changed.connect(self.on_global_position_changed)
        self.global_params.zoom_changed.connect(self.on_global_zoom_changed)
        self.global_params.duration_changed.connect(self.on_global_duration_changed)
        self.global_params.track_count_changed.connect(self.on_global_track_count_changed)

        self.init_ui()

        print("✅ MultiTrackTimeline 初始化完成，使用全局参数管理器")

    def on_global_position_changed(self, position: float):
        """响应全局位置变化"""
        if not self.global_params._updating_position:
            self.set_position(position)

    def on_global_zoom_changed(self, pixels_per_second: float):
        """响应全局缩放变化"""
        self.apply_zoom_level()

    def on_global_duration_changed(self, duration: float):
        """响应全局时长变化"""
        self.update_timeline_duration()

    def on_global_track_count_changed(self, count: int):
        """响应全局轨道数量变化"""
        # 🔧 新设计：不再需要更新统一播放头轨道参数
        # self.update_unified_playhead_tracks()
        pass

    def update_timeline_duration(self):
        """更新时间轴时长显示"""
        if self.timeline_ruler:
            self.timeline_ruler.set_total_duration(self.global_params.total_duration)

        if self.scroll_content:
            self.scroll_content.update_size()

    # 🔧 新设计：不再需要统一播放头管理器
    # def update_unified_playhead_tracks(self):
    #     """更新统一播放头的轨道参数"""
    #     if self.unified_playhead:
    #         self.unified_playhead.set_track_params(
    #             track_count=self.global_params.track_count,
    #             track_height=self.global_params.track_height,
    #             track_spacing=self.global_params.track_spacing
    #         )

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # 时间轴控制栏
        control_bar = self.create_control_bar()
        layout.addWidget(control_bar)

        # 主要时间轴区域
        main_area = self.create_main_area()
        layout.addWidget(main_area)

        # 初始化默认轨道
        self.add_track("video", "视频1")
        self.add_track("audio", "音频1")

        # 🔧 新设计：不再创建顶层播放头，使用滚动内容中的播放头
        # self.create_top_level_playhead()

    def create_top_level_playhead(self):
        """创建顶层播放头组件"""
        from .timeline_components import TopLevelPlayhead

        # 创建顶层播放头覆盖层
        self.top_level_playhead = TopLevelPlayhead(
            self.global_params,
            timeline_start_x=self.global_params.track_label_width,
            parent=self
        )

        # 设置覆盖层几何 - 覆盖整个时间轴组件
        self.top_level_playhead.setGeometry(self.rect())

        # 🔧 修复：确保滚动区域引用正确设置
        self.top_level_playhead.set_scroll_area(self.scroll_area)

        # 确保播放头在最上层
        self.top_level_playhead.raise_()
        self.top_level_playhead.show()

        print("✅ 顶层播放头已创建，滚动区域已连接")

    # 🔧 新设计：不再需要统一播放头管理器
    # def create_unified_playhead(self):
    #     """创建统一播放头管理器"""
    #     # 创建统一播放头管理器，覆盖整个时间轴区域
    #     self.unified_playhead = UnifiedPlayheadManager(self)
    #
    #     # 设置时间轴参数
    #     self.unified_playhead.set_timeline_params(
    #         start_x=self.global_params.track_label_width,
    #         width=self.global_params.track_content_width,
    #         duration=self.global_params.total_duration,
    #         pixels_per_second=self.global_params.pixels_per_second
    #     )
    #
    #     # 设置控制栏高度
    #     self.unified_playhead.set_control_bar_height(self.global_params.ruler_height)
    #
    #     # 设置轨道参数
    #     track_count = len(self.tracks)
    #     self.global_params.set_track_count(track_count, emit_signal=False)
    #     self.unified_playhead.set_track_params(
    #         track_count=track_count,
    #         track_height=self.global_params.track_height,
    #         track_spacing=self.global_params.track_spacing
    #     )
    #
    #     # 设置初始位置
    #     self.unified_playhead.set_position(self.global_params.current_position)
    #
    #     print(f"✅ 统一播放头管理器已创建，轨道数量: {track_count}")

    def resizeEvent(self, event):
        """处理窗口大小变化事件"""
        super().resizeEvent(event)

        # 🔧 新设计：不再使用顶层播放头
        # if hasattr(self, 'top_level_playhead'):
        #     self.top_level_playhead.setGeometry(self.rect())
        #     self.top_level_playhead.raise_()
        #     self.top_level_playhead.show()

    def mousePressEvent(self, event):
        """鼠标按下事件 - 支持在整个时间轴区域拖动播放头"""
        if event.button() == Qt.MouseButton.LeftButton:
            # 检查是否在时间轴区域内（控制栏下方）
            if event.position().y() >= self.global_params.ruler_height:
                x_pos = event.position().x()

                # 检查是否在时间轴有效区域内
                track_start = self.global_params.track_label_width
                track_end = track_start + self.global_params.track_content_width
                if track_start <= x_pos <= track_end:
                    # 计算相对于时间轴内容区域的X位置
                    timeline_x = x_pos - track_start
                    time_pos = max(0, min(self.global_params.pixels_to_time(timeline_x), self.global_params.total_duration))

                    # 应用磁性吸附
                    snapped_time = self.global_params.apply_snap(time_pos)

                    # 设置播放头位置
                    self.set_position(snapped_time)
                    self.dragging_playhead = True

                    print(f"🎯 开始拖动播放头: {time_pos:.2f}s -> {snapped_time:.2f}s (磁性吸附) at x={x_pos}, y={event.position().y()}")
                    return

        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 拖动播放头"""
        if hasattr(self, 'dragging_playhead') and self.dragging_playhead:
            x_pos = event.position().x()

            # 检查是否在时间轴有效区域内
            track_start = self.global_params.track_label_width
            track_end = track_start + self.global_params.track_content_width
            if track_start <= x_pos <= track_end:
                # 计算相对于时间轴内容区域的X位置
                timeline_x = x_pos - track_start
                time_pos = max(0, min(self.global_params.pixels_to_time(timeline_x), self.global_params.total_duration))

                # 应用磁性吸附
                snapped_time = self.global_params.apply_snap(time_pos)

                # 设置播放头位置
                self.set_position(snapped_time)

                print(f"🎯 拖动播放头: {time_pos:.2f}s -> {snapped_time:.2f}s (磁性吸附) at x={x_pos}, y={event.position().y()}")
            return

        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            if hasattr(self, 'dragging_playhead'):
                self.dragging_playhead = False
                print("🎯 结束拖动播放头")

        super().mouseReleaseEvent(event)

    def create_control_bar(self):
        """创建时间轴控制栏"""
        control_bar = QWidget()
        control_bar.setFixedHeight(48)  # 固定高度48px，宽度自适应
        control_bar.setStyleSheet("""
            QWidget {
                background-color: #333333;
            }
        """)

        control_layout = QHBoxLayout(control_bar)
        control_layout.setContentsMargins(24, 0, 24, 0)
        control_layout.setSpacing(0)  # 手动控制所有间距

        # 轨道标签
        track_label = QLabel("轨道:")
        track_label.setStyleSheet("""
            QLabel {
                color: #F2ECFF;
                font-size: 14px;
                padding: 0px;
                margin: 0px;
            }
        """)
        control_layout.addWidget(track_label)
        control_layout.addSpacing(24)  # 24px间距

        # 视频+按钮
        add_video_btn = QPushButton("视频+")
        add_video_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #F2ECFF;
                border: none;
                padding: 0px;
                margin: 0px;
                font-size: 14px;
            }
            QPushButton:hover {
                color: #FFFFFF;
            }
        """)
        add_video_btn.setToolTip("添加视频轨道")
        add_video_btn.clicked.connect(lambda: self.add_track("video"))
        control_layout.addWidget(add_video_btn)
        control_layout.addSpacing(24)  # 24px间距

        # 音频+按钮
        add_audio_btn = QPushButton("音频+")
        add_audio_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #F2ECFF;
                border: none;
                padding: 0px;
                margin: 0px;
                font-size: 14px;
            }
            QPushButton:hover {
                color: #FFFFFF;
            }
        """)
        add_audio_btn.setToolTip("添加音频轨道")
        add_audio_btn.clicked.connect(lambda: self.add_track("audio"))
        control_layout.addWidget(add_audio_btn)
        control_layout.addSpacing(24)  # 24px间距

        # 时间轴标签
        zoom_label = QLabel("时间轴:")
        zoom_label.setStyleSheet("""
            QLabel {
                color: #F2ECFF;
                font-size: 14px;
                padding: 0px;
                margin: 0px;
            }
        """)
        control_layout.addWidget(zoom_label)
        control_layout.addSpacing(24)  # 24px间距

        # 缩小按钮
        self.zoom_out_btn = QPushButton("-")
        self.zoom_out_btn.setFixedSize(12, 12)
        self.zoom_out_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #F2ECFF;
                border: none;
                padding: 0px;
                margin: 0px;
                font-size: 12px;
            }
            QPushButton:hover {
                color: #FFFFFF;
            }
        """)
        self.zoom_out_btn.clicked.connect(self.zoom_out)
        control_layout.addWidget(self.zoom_out_btn)
        control_layout.addSpacing(12)  # 12px间距

        # 缩放滑块
        self.zoom_slider = QSlider(Qt.Orientation.Horizontal)
        self.zoom_slider.setRange(1, 100)
        self.zoom_slider.setValue(20)
        self.zoom_slider.setFixedWidth(80)  # 更小的滑块
        self.zoom_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #555555;
                height: 3px;
                background: #555555;
                border-radius: 1px;
            }
            QSlider::handle:horizontal {
                background: #F2ECFF;
                border: 1px solid #F2ECFF;
                width: 12px;
                height: 12px;
                border-radius: 6px;  /* 标准圆形 */
                margin: -5px 0;
            }
            QSlider::handle:horizontal:hover {
                background: #FFFFFF;
                border: 1px solid #FFFFFF;
            }
        """)
        self.zoom_slider.valueChanged.connect(self.apply_zoom)
        control_layout.addWidget(self.zoom_slider)
        control_layout.addSpacing(12)  # 12px间距

        # 放大按钮
        self.zoom_in_btn = QPushButton("+")
        self.zoom_in_btn.setFixedSize(12, 12)
        self.zoom_in_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #F2ECFF;
                border: none;
                padding: 0px;
                margin: 0px;
                font-size: 12px;
            }
            QPushButton:hover {
                color: #FFFFFF;
            }
        """)
        self.zoom_in_btn.clicked.connect(self.zoom_in)
        control_layout.addWidget(self.zoom_in_btn)
        control_layout.addSpacing(24)  # 24px间距

        # 缩放显示
        self.zoom_display = QLabel("100px/s")
        self.zoom_display.setStyleSheet("""
            QLabel {
                color: #F2ECFF;
                font-size: 14px;
                padding: 0px;
                margin: 0px;
            }
        """)
        control_layout.addWidget(self.zoom_display)

        # 工具按钮 - 文字样式
        tools_section = QHBoxLayout()
        tools_section.setSpacing(24)

        # 适应按钮 - 文字样式
        self.fit_btn = QPushButton("适应")
        self.fit_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #F2ECFF;
                border: none;
                padding: 0px;
                margin: 0px;
                font-size: 14px;
            }
            QPushButton:hover {
                color: #FFFFFF;
            }
        """)
        self.fit_btn.setToolTip("适应时间轴到窗口")
        self.fit_btn.clicked.connect(self.zoom_to_fit)
        tools_section.addWidget(self.fit_btn)

        # 磁性吸附 - 文字样式
        self.snap_btn = QPushButton("磁性")
        self.snap_btn.setCheckable(True)
        self.snap_btn.setChecked(True)
        self.snap_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #F2ECFF;
                border: none;
                padding: 0px;
                margin: 0px;
                font-size: 14px;
            }
            QPushButton:hover {
                color: #FFFFFF;
            }
            QPushButton:checked {
                color: #00C896;
                font-weight: bold;
            }
        """)
        self.snap_btn.setToolTip("启用磁性吸附")
        self.snap_btn.toggled.connect(self.on_snap_toggled)
        tools_section.addWidget(self.snap_btn)

        control_layout.addLayout(tools_section)

        # 弹性空间
        control_layout.addStretch()

        return control_bar

    def create_main_area(self):
        """创建主要时间轴区域"""
        main_area = QWidget()
        main_area.setStyleSheet("""
            QWidget {
                background-color: #2B2B2B;
            }
        """)

        main_layout = QHBoxLayout(main_area)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 左侧轨道标签区域 - 使用全局参数的宽度，背景色#4B4D52
        self.track_labels_widget = QWidget()
        self.track_labels_widget.setFixedWidth(self.global_params.track_label_width)
        self.track_labels_widget.setStyleSheet("""
            QWidget {
                background-color: #4B4D52;
                border: none;
            }
        """)

        self.labels_layout = QVBoxLayout(self.track_labels_widget)
        self.labels_layout.setContentsMargins(5, 0, 5, 5)  # 顶部边距0，与右侧对齐
        self.labels_layout.setSpacing(self.global_params.track_spacing)  # 使用全局轨道间距
        self.labels_layout.setAlignment(Qt.AlignmentFlag.AlignTop)  # 从顶部开始排列，不自适应分配空间

        # 时间标尺占位符 - 使用全局参数的高度，不显示内容
        ruler_placeholder = QWidget()
        ruler_placeholder.setFixedHeight(self.global_params.ruler_height)
        ruler_placeholder.setStyleSheet("""
            QWidget {
                background-color: transparent;
                border: none;
            }
        """)
        self.labels_layout.addWidget(ruler_placeholder)

        main_layout.addWidget(self.track_labels_widget)

        # 右侧时间轴和轨道滚动区域 - 宽度自适应，背景色#4A4857
        scroll_area = QScrollArea()
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOn)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setWidgetResizable(True)

        # 🔧 修复：保存滚动区域引用，用于自动滚动
        self.scroll_area = scroll_area
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: #4A4857;
                border: none;
            }
            QScrollBar:horizontal {
                background-color: #3C3C3C;
                height: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:horizontal {
                background-color: #666;
                border-radius: 6px;
                min-width: 20px;
            }
            QScrollBar::handle:horizontal:hover {
                background-color: #888;
            }
            QScrollBar:vertical {
                background-color: #3C3C3C;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #666;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #888;
            }
        """)

        # 创建滚动内容区域 - 使用全局参数
        self.scroll_content = TimelineScrollContent(self.global_params)
        # 连接滚动内容区域的位置改变信号
        self.scroll_content.position_changed.connect(self.on_timeline_position_changed)

        # 🔧 删除重复播放头覆盖层：使用主窗口的统一播放头管理器
        self.scroll_playhead_overlay = None  # 不再创建重复的播放头覆盖层


        scroll_layout = QVBoxLayout(self.scroll_content)
        scroll_layout.setContentsMargins(0, 0, 0, 0)
        scroll_layout.setSpacing(0)

        # 专业的时间标尺 - 使用全局参数
        self.timeline_ruler = TimelineRuler(self.global_params)
        # 连接时间标尺的位置改变信号
        self.timeline_ruler.position_changed.connect(self.on_timeline_position_changed)
        scroll_layout.addWidget(self.timeline_ruler)

        # 轨道容器 - 使用全局参数
        self.tracks_container = QWidget()
        self.tracks_layout = QVBoxLayout(self.tracks_container)
        self.tracks_layout.setContentsMargins(0, self.global_params.track_spacing, 0, 0)  # 顶部间距，与时间轴间隔
        self.tracks_layout.setSpacing(self.global_params.track_spacing)  # 使用全局轨道间距
        self.tracks_layout.setAlignment(Qt.AlignmentFlag.AlignTop)  # 从顶部开始排列，不自适应分配空间

        scroll_layout.addWidget(self.tracks_container)
        scroll_layout.addStretch()

        # 设置滚动区域的内容
        scroll_area.setWidget(self.scroll_content)
        main_layout.addWidget(scroll_area)

        return main_area

    def add_track(self, track_type: str, name: str = None):
        """添加轨道"""
        if name is None:
            track_count = len([t for t in self.tracks if t['type'] == track_type])
            name = f"{track_type.title()}{track_count + 1}"

        # 创建轨道数据
        track_data = {
            'type': track_type,
            'name': name,
            'media_files': [],
            'locked': False,
            'muted': False,
            'solo': False
        }

        self.tracks.append(track_data)
        track_index = len(self.tracks) - 1

        # 创建轨道标签
        track_label = TrackLabel(name, track_type, track_index, self)
        track_label.delete_requested.connect(self.remove_track)
        self.labels_layout.addWidget(track_label)

        # 创建轨道组件
        track_widget = TrackWidget(track_type)
        track_widget.media_clicked.connect(self.on_media_clicked)
        track_widget.set_zoom(self.pixels_per_second)
        self.tracks_layout.addWidget(track_widget)
        self.track_widgets.append(track_widget)

        # 🔧 关键修复：添加轨道后确保滚动播放头保持在最上层
        if hasattr(self, 'scroll_content') and hasattr(self.scroll_content, 'scroll_playhead'):
            self.scroll_content.scroll_playhead.raise_()
            self.scroll_content.scroll_playhead.show()

        print(f"添加轨道: {name} ({track_type})")
        return track_index

    def remove_track(self, track_index: int):
        """删除轨道"""
        if 0 <= track_index < len(self.tracks):
            track = self.tracks[track_index]
            print(f"删除轨道: {track['name']}")

            # 删除轨道数据
            del self.tracks[track_index]

            # 删除轨道组件
            if track_index < len(self.track_widgets):
                widget = self.track_widgets[track_index]

                # 断开信号连接，防止删除后仍然响应事件
                try:
                    widget.media_clicked.disconnect()
                    widget.media_dropped.disconnect()
                except:
                    pass  # 如果信号已经断开，忽略错误

                widget.deleteLater()
                del self.track_widgets[track_index]

            # 删除轨道标签
            label_item = self.labels_layout.itemAt(track_index + 1)  # +1 因为第一个是占位符
            if label_item:
                label_widget = label_item.widget()
                if label_widget:
                    label_widget.deleteLater()
                self.labels_layout.removeItem(label_item)



    def on_media_clicked(self, file_path: str, start_time: float, track_type: str = None):
        """处理媒体块点击 - 移动时间轴到点击位置并发射媒体点击信号"""
        print(f"🎯 媒体块被点击: {file_path} 时间: {start_time:.2f}s, 轨道类型: {track_type}")

        # 直接移动时间轴到指定位置，不加载视频
        self.position_changed.emit(start_time)

        # 发射媒体点击信号给主窗口
        if track_type:
            self.media_clicked.emit(file_path, start_time, track_type)

    def on_timeline_position_changed(self, position: float):
        """处理时间轴位置变化 - 防止循环调用"""
        # 🔧 修复：防止循环调用，使用全局参数管理器的更新状态标志
        if self.global_params._updating_position:
            return

        print(f"🎯 MultiTrackTimeline.on_timeline_position_changed: {position:.2f}s")

        # 🔧 修复：只有在非更新状态下才处理位置变化，避免循环调用
        if not self._updating_position:
            # 调用set_position方法来确保所有组件都得到更新
            self.set_position(position)

            # 发射位置变化信号给主窗口
            self.position_changed.emit(position)

    def zoom_in(self):
        """放大"""
        self.global_params.zoom_in()

    def zoom_out(self):
        """缩小"""
        self.global_params.zoom_out()

    def apply_zoom(self, value: int):
        """应用缩放"""
        # 将滑块值映射到缩放级别
        zoom_levels = self.global_params.zoom_levels
        zoom_index = int((value / 100.0) * (len(zoom_levels) - 1))
        zoom_index = max(0, min(zoom_index, len(zoom_levels) - 1))

        if zoom_index != self.global_params.current_zoom_index:
            self.global_params.set_zoom_index(zoom_index)

    def apply_zoom_level(self):
        """应用缩放级别 - 响应全局参数变化"""
        # 更新显示
        self.zoom_display.setText(f"{self.global_params.pixels_per_second}px/s")

        # 🔧 新设计：不再需要更新统一播放头参数
        # if self.unified_playhead:
        #     self.unified_playhead.set_timeline_params(
        #         start_x=self.global_params.track_label_width,
        #         width=self.global_params.track_content_width,
        #         duration=self.global_params.total_duration,
        #         pixels_per_second=self.global_params.pixels_per_second
        #     )

        # 更新所有轨道组件
        for track_widget in self.track_widgets:
            track_widget.set_zoom(self.global_params.pixels_per_second)

        # 更新轨道宽度以匹配时间轴
        new_width = self.global_params.get_timeline_width()
        for track_widget in self.track_widgets:
            track_widget.setMinimumWidth(new_width)

        # 重新计算所有媒体块的位置和大小
        for track_index in range(len(self.tracks)):
            print(f"🔄 更新轨道 {track_index} 显示...")
            self.update_track_display(track_index)

        print(f"✅ 缩放已应用: {self.global_params.pixels_per_second}px/s, 轨道宽度: {new_width}px, 更新了 {len(self.tracks)} 个轨道")

    def zoom_to_fit(self):
        """缩放到适应窗口"""
        if self.scroll_content and self.global_params.total_duration > 0:
            available_width = self.scroll_content.parent().width() - 50  # 留一些边距
            required_pixels_per_second = available_width / self.global_params.total_duration

            # 找到最接近的缩放级别
            zoom_levels = self.global_params.zoom_levels
            best_index = 0
            best_diff = abs(zoom_levels[0] - required_pixels_per_second)

            for i, level in enumerate(zoom_levels):
                diff = abs(level - required_pixels_per_second)
                if diff < best_diff:
                    best_diff = diff
                    best_index = i

            self.global_params.set_zoom_index(best_index)

            # 更新滑块
            slider_value = int((best_index / (len(zoom_levels) - 1)) * 100)
            self.zoom_slider.setValue(slider_value)

    def set_position(self, position: float):
        """设置播放位置 - 防止循环调用，支持自动滚动"""
        # 防止循环调用
        if self.global_params._updating_position:
            return

        self.global_params.set_updating_position(True)
        try:
            # 使用全局参数管理器设置位置
            self.global_params.set_current_position(position)

            # 自动滚动确保播放头可见
            self.ensure_playhead_visible(position)

            # 🔧 新设计：播放头在滚动内容中，通过全局参数自动更新
            # if self.unified_playhead:
            #     self.unified_playhead.set_position(position)

            # 保留原有的时间轴标尺更新（如果需要）
            if self.timeline_ruler:
                self.timeline_ruler.set_position(position)
        finally:
            self.global_params.set_updating_position(False)

    def ensure_playhead_visible(self, position: float):
        """确保播放头在可见区域内 - 自动滚动"""
        if not self.scroll_area:
            return

        # 计算播放头的X位置
        playhead_x = self.global_params.time_to_pixels(position)

        # 获取滚动区域的可见范围
        scroll_bar = self.scroll_area.horizontalScrollBar()
        if not scroll_bar:
            return

        current_scroll = scroll_bar.value()
        viewport_width = self.scroll_area.viewport().width()

        # 计算可见区域的左右边界
        visible_left = current_scroll
        visible_right = current_scroll + viewport_width

        # 设置边界缓冲区，确保播放头不会贴边
        buffer = 100  # 100像素缓冲区

        # 检查是否需要滚动
        need_scroll = False
        new_scroll_value = current_scroll

        if playhead_x < visible_left + buffer:
            # 播放头在左边界附近，向左滚动
            new_scroll_value = max(0, playhead_x - buffer)
            need_scroll = True
        elif playhead_x > visible_right - buffer:
            # 播放头在右边界附近，向右滚动
            new_scroll_value = playhead_x - viewport_width + buffer
            need_scroll = True

        if need_scroll:
            # 平滑滚动到新位置
            scroll_bar.setValue(int(new_scroll_value))
            print(f"🔄 自动滚动: 播放头位置{playhead_x}px, 滚动到{new_scroll_value}")

            # 🔧 新设计：播放头在滚动内容中，不需要额外更新
            # if self.unified_playhead:
            #     # 更新播放头覆盖层几何，确保滚动后正确显示
            #     self.unified_playhead.update_geometry(self.rect())
            #     # 强制重绘播放头
            #     self.unified_playhead.playhead_overlay.update()

    def set_drag_position(self, x_pos: int):
        """设置拖动预览位置"""
        if hasattr(self, 'scroll_playhead_overlay') and self.scroll_playhead_overlay:
            self.scroll_playhead_overlay.set_drag_position(x_pos)

    def clear_drag_position(self):
        """清除拖动预览位置"""
        if hasattr(self, 'scroll_playhead_overlay') and self.scroll_playhead_overlay:
            self.scroll_playhead_overlay.clear_drag_position()

    def handle_track_drop(self, track_widget, file_path: str, drop_x: int):
        """处理拖放到轨道的文件"""
        print(f"🔍 handle_track_drop 被调用: {file_path}")
        try:
            # 找到对应的轨道索引
            track_index = None
            for i, widget in enumerate(self.track_widgets):
                if widget == track_widget:
                    track_index = i
                    break

            if track_index is None:
                print("❌ 找不到对应的轨道")
                return

            # 🔧 新增：检查是否是模板拖放
            if file_path.startswith("template:"):
                template_id = file_path.replace("template:", "")
                print(f"🎬 检测到模板拖放: {template_id}")

                # 获取主窗口实例并加载模板
                main_window = self.parent()
                while main_window and not hasattr(main_window, 'load_template'):
                    main_window = main_window.parent()

                if main_window and hasattr(main_window, 'load_template'):
                    main_window.load_template(template_id)
                    return
                else:
                    print("❌ 找不到主窗口实例")
                    return

            # 检查文件是否存在
            if not os.path.exists(file_path):
                print(f"❌ 文件不存在: {file_path}")
                return

            # 计算时间位置并应用磁性吸附
            time_position = self.global_params.pixels_to_time(drop_x)

            # 应用磁性吸附
            time_position = self.global_params.apply_snap(time_position)

            # 获取媒体时长
            duration = self.get_media_duration(file_path)
            if duration <= 0:
                print(f"❌ 无法获取媒体时长: {file_path}")
                return

            # 检查是否是占位符替换
            track = self.tracks[track_index]
            placeholder_to_replace = None

            # 查找最接近的占位符
            min_distance = float('inf')
            for media_item in track['media_files']:
                if media_item.get('is_placeholder', False):
                    # 检查拖拽位置是否在占位符范围内或附近
                    placeholder_start = media_item['start_time']
                    placeholder_end = placeholder_start + media_item['duration']

                    # 情况1：拖拽到占位符内部
                    if placeholder_start <= time_position <= placeholder_end:
                        placeholder_to_replace = media_item
                        print(f"🎯 拖拽到占位符内部: {media_item['name']}")
                        break

                    # 情况2：拖拽到占位符附近（5秒容差）
                    distance_to_start = abs(time_position - placeholder_start)
                    distance_to_end = abs(time_position - placeholder_end)
                    min_distance_to_placeholder = min(distance_to_start, distance_to_end)

                    if min_distance_to_placeholder < min_distance and min_distance_to_placeholder < 5.0:
                        min_distance = min_distance_to_placeholder
                        placeholder_to_replace = media_item

            if placeholder_to_replace:
                print(f"🎯 找到要替换的占位符: {placeholder_to_replace['name']} (位置: {placeholder_to_replace['start_time']:.1f}s)")
            else:
                print(f"ℹ️ 未找到占位符，将作为新媒体项添加到位置: {time_position:.1f}s")

            # 检查是否为视频文件且包含音频轨道
            # 🔧 修改：去掉自动音频提取逻辑
            # 不再自动从视频中提取音频到音频轨道
            audio_extracted = False

            # 创建视频媒体项（如果提取了音频，则标记为无音频）
            media_item = {
                'file_path': file_path,
                'start_time': time_position,
                'duration': duration,
                'name': os.path.basename(file_path),
                'id': f"{track_index}_{len(track['media_files'])}",
                'has_audio_extracted': audio_extracted  # 标记音频是否已被提取
            }

            if placeholder_to_replace:
                # 替换占位符
                media_item['start_time'] = placeholder_to_replace['start_time']
                template_duration = placeholder_to_replace['duration']
                media_item['template_segment'] = placeholder_to_replace.get('template_segment', False)
                media_item['segment_type'] = placeholder_to_replace.get('segment_type', '')

                # 智能截取：如果视频比模板片段长，自动截取精彩片段
                if duration > template_duration:
                    print(f"🎬 视频长度({duration:.1f}s) > 模板长度({template_duration:.1f}s)，开始智能截取...")
                    media_item['duration'] = template_duration
                    media_item['trim_start'] = 0
                    media_item['trim_end'] = duration - template_duration
                else:
                    # 视频不长于模板，保持原长度
                    media_item['duration'] = template_duration

                # 移除占位符
                track['media_files'].remove(placeholder_to_replace)
                print(f"✅ 替换占位符: {placeholder_to_replace['name']} -> {os.path.basename(file_path)}")

                # 添加替换后的媒体项
                track['media_files'].append(media_item)
                print(f"✅ 占位符替换完成，新媒体项位置: {media_item['start_time']:.1f}s, 时长: {media_item['duration']:.1f}s")
            else:
                # 没有找到占位符，需要检查重叠并智能插入
                final_position = self.find_non_overlapping_position(track, time_position, duration)
                media_item['start_time'] = final_position

                track['media_files'].append(media_item)
                print(f"✅ 添加新媒体项: {os.path.basename(file_path)} at {final_position:.2f}s")

            # 按时间排序
            track['media_files'].sort(key=lambda x: x['start_time'])

            # 更新总时长
            self.update_total_duration()

            # 🔧 新功能：只在第一次拖放时自动调整时间轴缩放
            if not self.has_auto_scaled:
                self.auto_adjust_zoom_for_media(duration)
                self.has_auto_scaled = True
                print(f"🔍 首次自动缩放完成，后续拖放将不再自动调整缩放")

            # 更新轨道显示
            self.update_track_display(track_index)

            # 发出媒体拖放信号
            self.media_dropped.emit(track_index, media_item)

            print(f"✅ 已添加媒体到轨道 {track_index}: {os.path.basename(file_path)} at {time_position:.2f}s")

        except Exception as e:
            print(f"❌ 处理轨道拖放失败: {e}")
            import traceback
            traceback.print_exc()

    def auto_adjust_zoom_for_media(self, media_duration: float):
        """自动调整时间轴缩放，确保媒体素材块占用不超过半个可视区域宽度"""
        try:
            if not self.scroll_area:
                return

            # 获取可视区域宽度
            viewport_width = self.scroll_area.viewport().width()
            if viewport_width <= 0:
                return

            # 计算半个可视区域的宽度（目标宽度）
            target_width = viewport_width * 0.5  # 50%的可视区域宽度

            # 计算需要的像素比例：目标宽度 / 媒体时长
            required_pixels_per_second = target_width / media_duration

            # 找到最接近的缩放级别
            zoom_levels = self.global_params.zoom_levels
            best_index = 0
            best_diff = abs(zoom_levels[0] - required_pixels_per_second)

            for i, level in enumerate(zoom_levels):
                diff = abs(level - required_pixels_per_second)
                if diff < best_diff:
                    best_diff = diff
                    best_index = i

            # 应用缩放级别
            current_zoom_index = self.global_params.current_zoom_index
            if best_index != current_zoom_index:
                self.global_params.set_zoom_index(best_index)

                # 更新滑块
                slider_value = int((best_index / (len(zoom_levels) - 1)) * 100)
                if hasattr(self, 'zoom_slider'):
                    self.zoom_slider.setValue(slider_value)

                print(f"🔍 自动调整缩放: 媒体时长{media_duration:.1f}s, 目标宽度{target_width:.0f}px, "
                      f"缩放级别{current_zoom_index}->{best_index} ({zoom_levels[best_index]}px/s)")

        except Exception as e:
            print(f"❌ 自动调整缩放失败: {e}")

    def find_non_overlapping_position(self, track: dict, desired_position: float, duration: float) -> float:
        """找到不重叠的插入位置"""
        try:
            media_files = track['media_files']

            # 如果轨道为空，直接返回期望位置
            if not media_files:
                return max(0, desired_position)

            # 按开始时间排序现有媒体项
            sorted_media = sorted(media_files, key=lambda x: x['start_time'])

            # 检查期望位置是否与现有素材重叠
            desired_end = desired_position + duration

            # 查找插入点
            insert_before_index = None
            for i, media in enumerate(sorted_media):
                media_start = media['start_time']
                media_end = media_start + media['duration']

                # 检查是否重叠
                if not (desired_end <= media_start or desired_position >= media_end):
                    # 有重叠，需要找到合适的插入位置
                    print(f"🔍 检测到重叠: 期望位置{desired_position:.1f}s-{desired_end:.1f}s 与现有素材{media_start:.1f}s-{media_end:.1f}s重叠")

                    # 判断插入到前面还是后面
                    distance_to_start = abs(desired_position - media_start)
                    distance_to_end = abs(desired_position - media_end)

                    if distance_to_start < distance_to_end:
                        # 插入到当前素材之前
                        insert_before_index = i
                        break
                    else:
                        # 继续检查是否可以插入到后面
                        continue

            # 如果没有找到插入点，插入到最后
            if insert_before_index is None:
                # 插入到最后一个素材之后
                last_media = sorted_media[-1]
                final_position = last_media['start_time'] + last_media['duration']
                print(f"🎯 插入到轨道末尾: {final_position:.1f}s")
                return final_position

            # 插入到指定位置之前，需要检查是否有足够空间
            if insert_before_index == 0:
                # 插入到第一个素材之前
                first_media = sorted_media[0]
                if first_media['start_time'] >= duration:
                    # 有足够空间插入到开头
                    final_position = max(0, first_media['start_time'] - duration)
                    print(f"🎯 插入到轨道开头: {final_position:.1f}s")
                    return final_position
                else:
                    # 空间不够，需要顺延后面的素材
                    final_position = 0
                    self.shift_media_items_after(track, final_position, duration)
                    print(f"🎯 插入到轨道开头并顺延后续素材: {final_position:.1f}s")
                    return final_position
            else:
                # 插入到两个素材之间
                prev_media = sorted_media[insert_before_index - 1]
                curr_media = sorted_media[insert_before_index]

                prev_end = prev_media['start_time'] + prev_media['duration']
                available_space = curr_media['start_time'] - prev_end

                if available_space >= duration:
                    # 有足够空间插入
                    final_position = prev_end
                    print(f"🎯 插入到素材间隙: {final_position:.1f}s")
                    return final_position
                else:
                    # 空间不够，需要顺延后面的素材
                    final_position = prev_end
                    shift_amount = duration - available_space
                    self.shift_media_items_after(track, curr_media['start_time'], shift_amount)
                    print(f"🎯 插入到素材间隙并顺延后续素材: {final_position:.1f}s (顺延{shift_amount:.1f}s)")
                    return final_position

        except Exception as e:
            print(f"❌ 查找插入位置失败: {e}")
            return max(0, desired_position)

    def shift_media_items_after(self, track: dict, after_time: float, shift_amount: float):
        """将指定时间之后的所有素材向后顺延"""
        try:
            shifted_count = 0
            for media in track['media_files']:
                if media['start_time'] >= after_time:
                    old_start = media['start_time']
                    media['start_time'] += shift_amount
                    shifted_count += 1
                    print(f"  📦 顺延素材: {media['name']} {old_start:.1f}s -> {media['start_time']:.1f}s")

            if shifted_count > 0:
                print(f"✅ 已顺延 {shifted_count} 个素材，顺延时长: {shift_amount:.1f}s")

        except Exception as e:
            print(f"❌ 顺延素材失败: {e}")







    def get_media_duration(self, file_path: str) -> float:
        """获取媒体文件时长"""
        try:
            file_ext = Path(file_path).suffix.lower()

            # 判断是否为音频文件
            if file_ext in ['.mp3', '.wav', '.aac', '.m4a', '.flac', '.ogg']:
                # 使用ffprobe获取音频时长
                try:
                    import subprocess
                    import json

                    cmd = [
                        'ffprobe',
                        '-v', 'quiet',
                        '-threads', '1',  # 强制单线程
                        '-print_format', 'json',
                        '-show_format',
                        file_path
                    ]

                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        data = json.loads(result.stdout)
                        duration = float(data['format']['duration'])
                        return duration
                except Exception as e:
                    print(f"ffprobe获取音频时长失败: {e}")

                # 备选方案：使用pydub
                try:
                    from pydub import AudioSegment
                    audio = AudioSegment.from_file(file_path)
                    return len(audio) / 1000.0  # 转换为秒
                except Exception as e:
                    print(f"pydub获取音频时长失败: {e}")

            # 视频文件或其他文件
            elif file_ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv']:
                # 使用OpenCV获取视频时长
                try:
                    import cv2
                    cap = cv2.VideoCapture(file_path)
                    if cap.isOpened():
                        fps = cap.get(cv2.CAP_PROP_FPS)
                        frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
                        if fps > 0:
                            duration = frame_count / fps
                            cap.release()
                            return duration
                    cap.release()
                except Exception as e:
                    print(f"OpenCV获取视频时长失败: {e}")

                # 备选方案：使用ffprobe
                try:
                    import subprocess
                    import json

                    cmd = [
                        'ffprobe',
                        '-v', 'quiet',
                        '-print_format', 'json',
                        '-show_format',
                        file_path
                    ]

                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        data = json.loads(result.stdout)
                        duration = float(data['format']['duration'])
                        return duration
                except Exception as e:
                    print(f"ffprobe获取视频时长失败: {e}")

            # 默认时长
            return 10.0

        except Exception as e:
            print(f"获取媒体时长失败: {e}")
            return 10.0

    def update_total_duration(self):
        """更新总时长 - 基于轨道内容的最后一个素材块结束时间"""
        max_end_time = 0.0

        for track in self.tracks:
            for media_item in track['media_files']:
                if isinstance(media_item, dict):
                    end_time = media_item['start_time'] + media_item['duration']
                    max_end_time = max(max_end_time, end_time)

        # 🔧 新逻辑：总时长就是最后一个素材块的结束时间，不添加额外缓冲
        # 如果没有素材，则设为30秒的最小时长
        if max_end_time > 0:
            new_duration = max_end_time
        else:
            new_duration = 30.0  # 空轨道时的最小时长

        # 使用全局参数管理器设置时长
        self.global_params.set_total_duration(new_duration)

        print(f"📏 轨道总时长更新: {new_duration:.1f}s (基于最后素材块结束时间: {max_end_time:.1f}s)")

    def has_content_at_position(self, position: float) -> bool:
        """检查指定位置是否有任何素材内容"""
        for track in self.tracks:
            for media_item in track['media_files']:
                if isinstance(media_item, dict):
                    start_time = media_item['start_time']
                    end_time = start_time + media_item['duration']
                    if start_time <= position <= end_time:
                        return True
        return False

    def get_last_media_end_time(self) -> float:
        """获取最后一个素材块的结束时间"""
        max_end_time = 0.0
        for track in self.tracks:
            for media_item in track['media_files']:
                if isinstance(media_item, dict):
                    end_time = media_item['start_time'] + media_item['duration']
                    max_end_time = max(max_end_time, end_time)
        return max_end_time

    def update_track_display(self, track_index: int):
        """更新轨道显示 - 完全按照原版逻辑"""
        if 0 <= track_index < len(self.track_widgets):
            track = self.tracks[track_index]
            track_widget = self.track_widgets[track_index]

            # 彻底清除现有内容 - 修复残留问题（按照原版）
            # 清除所有VideoThumbnailBlock
            from gui.components.video_thumbnail_block import VideoThumbnailBlock
            from gui.components.audio_waveform_block import AudioWaveformBlock

            for child in track_widget.findChildren(VideoThumbnailBlock):
                child.setParent(None)  # 立即解除父子关系
                child.deleteLater()

            # 清除所有AudioWaveformBlock
            for child in track_widget.findChildren(AudioWaveformBlock):
                child.setParent(None)  # 立即解除父子关系
                child.deleteLater()

            # 清除所有QLabel（兼容旧版本）
            from PySide6.QtWidgets import QLabel
            for child in track_widget.findChildren(QLabel):
                child.setParent(None)  # 立即解除父子关系
                child.deleteLater()

            # 强制处理挂起的删除操作
            from PySide6.QtWidgets import QApplication
            QApplication.processEvents()

            # 为每个媒体片段创建显示块（按照原版）
            for i, media_item in enumerate(track['media_files']):
                if isinstance(media_item, dict):
                    # 新格式：包含时间和位置信息
                    file_path = media_item['file_path']
                    start_time = media_item['start_time']
                    duration = media_item['duration']
                    name = media_item['name']
                else:
                    # 兼容旧格式：只有文件路径
                    file_path = media_item
                    start_time = i * 100  # 简单排列
                    duration = 10.0
                    name = Path(file_path).stem

                # 精确的时间轴对齐计算 - 素材左边缘精确对应时间刻度（按照原版）
                x_pos = self.global_params.time_to_pixels(start_time)
                width = max(80, self.global_params.time_to_pixels(duration))  # 最小宽度80像素

                print(f"素材 {name}: start_time={start_time:.3f}s -> x_pos={x_pos}px (像素比={self.global_params.pixels_per_second})")
                # 调试日志：轨道重建时的位置计算
                if 'trim_start' in media_item:
                    print(f"🔍 轨道重建调试: {name}")
                    print(f"   - media_item['start_time']: {media_item['start_time']:.2f}s")
                    print(f"   - media_item['trim_start']: {media_item['trim_start']:.2f}s")
                    print(f"   - 计算出的x_pos: {x_pos}px")
                    print(f"   - 对应时间轴位置: {x_pos / self.pixels_per_second:.2f}s")

                # 根据文件类型创建相应的媒体块（按照原版）
                file_ext = Path(file_path).suffix.lower()
                if file_ext in ['.mp3', '.wav', '.aac', '.m4a', '.flac', '.ogg']:
                    # 音频文件使用AudioWaveformBlock
                    media_block = AudioWaveformBlock(media_item, track_index, i, self)
                    media_block.setParent(track_widget)
                    print(f"Created AudioWaveformBlock for: {name}")
                else:
                    # 视频文件使用VideoThumbnailBlock
                    media_block = VideoThumbnailBlock(media_item, track_index, i, self, getattr(self, 'video_processor', None))
                    media_block.setParent(track_widget)
                    media_block.lower()  # 降低层级，确保overlay在上面
                    if hasattr(track_widget, 'trim_handles_overlay'):
                        track_widget.trim_handles_overlay.raise_()  # overlay始终在最顶层

                # 精确位置：素材左边缘直接对应时间轴刻度，无偏移（按照原版）
                media_block.setGeometry(x_pos, 0, width, self.global_params.track_height)  # 使用全局轨道高度
                media_block.show()

                # 添加时长信息和工具提示（按照原版）
                duration_text = f"{duration:.1f}s"
                media_block.setToolTip(f"{name}\n时长: {duration_text}\n开始: {start_time:.1f}s\n提示: 拖动可重新定位")

                print(f"轨道 {track['name']} 中显示媒体块: {name} 位置({x_pos}, 0) 大小({width}, {self.global_params.track_height})")

            # 强制轨道组件重绘，确保游标显示（按照原版）
            track_widget.update()

            # 🔧 修复：更新专门的游标绘制层（按照原版）
            if hasattr(track_widget, 'update_trim_handles'):
                track_widget.update_trim_handles()

    def clear_all_drag_indicators(self):
        """清除所有拖拽指示器"""
        # 清除时间轴的拖拽指示器
        if self.timeline_ruler:
            self.timeline_ruler.clear_drag_position()

        if self.scroll_content:
            self.scroll_content.clear_drag_position()

        # 清除所有轨道的拖拽指示器
        for track_widget in self.track_widgets:
            track_widget.clear_drag_position()
            track_widget.set_drag_active(False)

    def show_all_drag_indicators(self):
        """显示所有轨道的拖拽指示器"""
        for track_widget in self.track_widgets:
            track_widget.set_drag_active(True)

    def hide_all_drag_indicators(self):
        """隐藏所有轨道的拖拽指示器"""
        for track_widget in self.track_widgets:
            track_widget.set_drag_active(False)

    def add_track(self, track_type: str, name: str = ""):
        """添加新轨道 - 完全按照原版逻辑"""
        if not name:
            count = len([t for t in self.tracks if t['type'] == track_type]) + 1
            name = f"{track_type.capitalize()}{count}"

        # 创建轨道数据 - 完全按照原版格式
        track = {
            'type': track_type,
            'name': name,
            'media_files': [],  # 存储媒体片段对象
            'muted': False,
            'visible': True
        }

        self.tracks.append(track)
        track_index = len(self.tracks) - 1  # 获取新轨道的索引

        # 创建轨道标签（左侧固定区域） - 使用全局参数
        track_label = TrackLabel(name, track_type, track_index, self)
        track_label.setFixedHeight(self.global_params.track_height)
        # 连接删除信号
        track_label.delete_requested.connect(self.delete_track)
        self.labels_layout.addWidget(track_label)

        # 创建轨道显示区域（右侧滚动区域） - 使用全局参数
        track_widget = TrackWidget(track_type, self.global_params)
        track_widget.setFixedHeight(self.global_params.track_height)
        track_widget.setMinimumWidth(self.global_params.get_timeline_width())

        # 连接媒体块点击信号
        track_widget.media_clicked.connect(self.on_media_clicked)

        # 使用绝对定位来支持时间轴对齐
        track_widget.setContentsMargins(0, 0, 0, 0)

        # 添加到轨道容器
        self.tracks_layout.addWidget(track_widget)
        self.track_widgets.append(track_widget)

        # 连接拖放信号
        track_widget.media_dropped.connect(lambda file_path, drop_x: self.handle_track_drop(track_widget, file_path, drop_x))

        # 🔧 初始化轨道参数
        self.initialize_track_params(track_index)

        # 更新全局轨道数量
        track_count = len(self.tracks)
        self.global_params.set_track_count(track_count)
        # 🔧 新设计：不再需要更新统一播放头轨道参数
        # if self.unified_playhead:
        #     self.unified_playhead.set_track_params(
        #         track_count=track_count,
        #         track_height=self.global_params.track_height,
        #         track_spacing=self.global_params.track_spacing
        #     )

        print(f"添加轨道: {name} ({track_type})")
        return track_index  # 返回轨道索引

    def delete_track(self, track_index: int):
        """删除轨道"""
        if 0 <= track_index < len(self.tracks):
            # 移除轨道数据
            track = self.tracks.pop(track_index)

            # 移除轨道标签 - 需要考虑占位符偏移
            label_index = track_index + 1  # +1 因为第一个是时间标尺占位符
            if label_index < self.labels_layout.count():
                label_item = self.labels_layout.takeAt(label_index)
                if label_item and label_item.widget():
                    label_item.widget().deleteLater()

            # 移除轨道组件
            if track_index < len(self.track_widgets):
                track_widget = self.track_widgets.pop(track_index)

                # 断开信号连接，防止删除后仍然响应事件
                try:
                    track_widget.media_clicked.disconnect()
                    track_widget.media_dropped.disconnect()
                except:
                    pass  # 如果信号已经断开，忽略错误

                self.tracks_layout.removeWidget(track_widget)
                track_widget.deleteLater()

            # 更新轨道索引
            self.update_track_indices()

            # 更新全局轨道数量
            track_count = len(self.tracks)
            self.global_params.set_track_count(track_count)
            # 🔧 新设计：不再需要更新统一播放头轨道参数
            # if self.unified_playhead:
            #     self.unified_playhead.set_track_params(
            #         track_count=track_count,
            #         track_height=self.global_params.track_height,
            #         track_spacing=self.global_params.track_spacing
            #     )

            # 🗑️ 清除预览窗口中与该轨道关联的视频播放器
            self.clear_track_preview_data(track)

            # 如果所有轨道都被删除，清空视频预览
            if len(self.tracks) == 0:
                self.position_changed.emit(-1)  # 发送特殊信号表示清空预览

            print(f"删除轨道: {track['name']}")

    def clear_track_preview_data(self, track: Dict[str, Any]):
        """清除轨道关联的预览窗口数据"""
        try:
            # 🗑️ 清除该轨道上所有视频的播放器窗体
            if hasattr(self, 'parent') and self.parent():
                main_window = self.parent()
                while main_window and not hasattr(main_window, 'video_canvas'):
                    main_window = main_window.parent()

                if main_window and hasattr(main_window, 'video_canvas'):
                    # 遍历轨道上的所有媒体文件
                    for media_item in track.get('media_files', []):
                        if isinstance(media_item, dict):
                            file_path = media_item.get('file_path', '')
                            if file_path:
                                # 从视频画布中移除对应的播放器
                                main_window.video_canvas.remove_video_player(file_path)
                                print(f"🗑️ 已清除预览窗口中的视频播放器: {file_path}")

            print("🗑️ 轨道预览数据清除完成")
        except Exception as e:
            print(f"❌ 清除轨道预览数据失败: {e}")

    def initialize_track_params(self, track_index: int):
        """初始化轨道关联的参数"""
        try:
            if 0 <= track_index < len(self.tracks):
                track = self.tracks[track_index]

                # 🔧 初始化轨道关联的关键参数
                track_params = {
                    'track_index': track_index,
                    'track_type': track.get('type', 'video'),
                    'track_name': track.get('name', f'轨道{track_index + 1}'),
                    'muted': track.get('muted', False),
                    'visible': track.get('visible', True),
                    'volume': track.get('volume', 1.0),
                    'opacity': track.get('opacity', 1.0),
                    'locked': track.get('locked', False),
                    'solo': track.get('solo', False),
                    'media_files': track.get('media_files', []),
                    'effects': track.get('effects', []),
                    'transitions': track.get('transitions', [])
                }

                # 将参数存储到轨道中
                track['params'] = track_params

                print(f"🔧 轨道参数已初始化: {track['name']} (索引: {track_index})")
                return track_params
            else:
                print(f"❌ 轨道索引无效: {track_index}")
                return None

        except Exception as e:
            print(f"❌ 初始化轨道参数失败: {e}")
            return None

    def get_track_params(self, track_index: int):
        """获取轨道参数"""
        try:
            if 0 <= track_index < len(self.tracks):
                track = self.tracks[track_index]
                return track.get('params', {})
            return {}
        except Exception as e:
            print(f"❌ 获取轨道参数失败: {e}")
            return {}

    def update_track_params(self, track_index: int, **kwargs):
        """更新轨道参数"""
        try:
            if 0 <= track_index < len(self.tracks):
                track = self.tracks[track_index]
                if 'params' not in track:
                    self.initialize_track_params(track_index)

                # 更新参数
                track['params'].update(kwargs)
                print(f"🔧 轨道参数已更新: {track['name']}")
                return True
            return False
        except Exception as e:
            print(f"❌ 更新轨道参数失败: {e}")
            return False

    def update_track_indices(self):
        """更新轨道索引"""
        # 更新轨道组件的索引
        for i, track_widget in enumerate(self.track_widgets):
            if hasattr(track_widget, 'track_index'):
                track_widget.track_index = i

        # 更新轨道标签的索引
        for i in range(self.labels_layout.count()):
            item = self.labels_layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                # 跳过占位符（第一个组件）
                if i > 0 and hasattr(widget, 'track_index'):
                    widget.track_index = i - 1  # -1 因为第一个是占位符



    def toggle_timeline_playback(self):
        """切换时间轴播放状态"""
        if hasattr(self, 'timeline_playback_timer') and self.timeline_playback_timer.isActive():
            self.stop_timeline_playback()
        else:
            self.start_timeline_playback()

    def start_timeline_playback(self):
        """开始时间轴播放 - 完全依赖主窗口定时器"""
        # 🔧 修复：完全禁用MultiTrackTimeline的独立定时器
        # 只依赖主窗口的timeline_timer来驱动播放
        main_window = self.parent()
        while main_window and not hasattr(main_window, 'timeline_timer'):
            main_window = main_window.parent()

        if main_window and hasattr(main_window, 'start_timeline_playback'):

            # 委托给主窗口的播放控制
            main_window.start_timeline_playback()
            # 发出播放开始信号
            self.playback_started.emit()
            return

        # 如果找不到主窗口，输出警告但不创建定时器
        print("⚠️ 找不到主窗口，无法启动时间轴播放")
        self.playback_started.emit()

    def stop_timeline_playback(self):
        """停止时间轴播放 - 委托给主窗口"""
        # 🔧 修复：委托给主窗口的停止播放控制
        main_window = self.parent()
        while main_window and not hasattr(main_window, 'stop_timeline_playback'):
            main_window = main_window.parent()

        if main_window and hasattr(main_window, 'stop_timeline_playback'):

            main_window.stop_timeline_playback()
        else:
            print("⚠️ 找不到主窗口，无法停止时间轴播放")

        # 发出播放停止信号
        self.playback_stopped.emit()

    def update_timeline_playback(self):
        """更新时间轴播放 - 已废弃，由主窗口定时器驱动"""
        # 🔧 修复：此方法已废弃，所有播放更新由主窗口的update_timeline_playback处理
        print("⚠️ MultiTrackTimeline.update_timeline_playback 已废弃，应使用主窗口定时器")
        pass

    def get_current_media_at_position(self, position: float):
        """获取指定位置的媒体项"""
        for track in self.tracks:
            for media_item in track['media_files']:
                if isinstance(media_item, dict):
                    start_time = media_item['start_time']
                    end_time = start_time + media_item['duration']
                    if start_time <= position < end_time:
                        return media_item
        return None

    def get_all_media_at_position(self, position: float):
        """获取指定位置的所有媒体项（支持多轨道）"""
        media_items = []
        for track_index, track in enumerate(self.tracks):
            for media_item in track['media_files']:
                if isinstance(media_item, dict):
                    start_time = media_item['start_time']
                    end_time = start_time + media_item['duration']
                    if start_time <= position < end_time:
                        media_items.append({
                            'track_index': track_index,
                            'track_type': track['type'],
                            'media_item': media_item
                        })
        return media_items

    def get_video_media_at_position(self, position: float):
        """获取指定位置的视频媒体项"""
        for track in self.tracks:
            if track['type'] == 'video':
                for media_item in track['media_files']:
                    if isinstance(media_item, dict):
                        start_time = media_item['start_time']
                        end_time = start_time + media_item['duration']
                        if start_time <= position < end_time:
                            return media_item
        return None

    def get_audio_media_at_position(self, position: float):
        """获取指定位置的所有音频媒体项"""
        audio_items = []
        for track in self.tracks:
            if track['type'] == 'audio':
                for media_item in track['media_files']:
                    if isinstance(media_item, dict):
                        start_time = media_item['start_time']
                        end_time = start_time + media_item['duration']
                        if start_time <= position < end_time:
                            audio_items.append(media_item)
        return audio_items

    def find_next_media_after_position(self, position: float):
        """查找指定位置之后的下一个媒体项"""
        next_media = None
        min_start_time = float('inf')

        for track in self.tracks:
            for media_item in track['media_files']:
                if isinstance(media_item, dict):
                    start_time = media_item['start_time']
                    if start_time > position and start_time < min_start_time:
                        min_start_time = start_time
                        next_media = media_item

        return next_media

    def clear_selection(self):
        """清除选择"""
        # 清除所有媒体块的选择状态
        for track_widget in self.track_widgets:
            for child in track_widget.children():
                if hasattr(child, 'setSelected'):
                    child.setSelected(False)

    def get_selection(self):
        """获取当前选择"""
        selected_items = []
        for track_index, track_widget in enumerate(self.track_widgets):
            for child in track_widget.children():
                if hasattr(child, 'isSelected') and child.isSelected():
                    selected_items.append({
                        'track_index': track_index,
                        'media_item': getattr(child, 'media_item', None)
                    })
        return selected_items

    def on_snap_toggled(self, enabled: bool):
        """磁性吸附开关切换"""
        self.global_params.set_snap_enabled(enabled)

    def apply_snap(self, time_position: float) -> float:
        """应用磁性吸附，返回吸附后的时间位置 - 委托给全局参数管理器"""
        return self.global_params.apply_snap(time_position)

    def apply_snap_pixels(self, x_position: int) -> int:
        """应用磁性吸附（像素坐标），返回吸附后的像素位置"""
        time_position = self.global_params.pixels_to_time(x_position)
        snapped_time = self.global_params.apply_snap(time_position)
        return self.global_params.time_to_pixels(snapped_time)
