#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强功能对话框
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, 
                               QWidget, QLabel, QPushButton, QTextEdit, QProgressBar,
                               QGroupBox, QFormLayout, QSpinBox, QDoubleSpinBox,
                               QCheckBox, QComboBox, QFileDialog, QMessageBox,
                               QTableWidget, QTableWidgetItem, QHeaderView)
from PySide6.QtCore import Qt, QThread, Signal, QTimer
from PySide6.QtGui import QFont

from core.ai_features.enhanced_features import EnhancedFeatureManager
from core.common.logger import get_logger


class VideoAnalysisThread(QThread):
    """视频分析线程"""
    
    progress_updated = Signal(int)
    analysis_completed = Signal(dict)
    error_occurred = Signal(str)
    
    def __init__(self, enhanced_manager, video_path):
        super().__init__()
        self.enhanced_manager = enhanced_manager
        self.video_path = video_path
        self.logger = get_logger('video_analysis_thread')
    
    def run(self):
        try:
            self.progress_updated.emit(10)
            
            # 执行视频分析
            analysis_result = self.enhanced_manager.analyze_video_content(self.video_path)
            
            self.progress_updated.emit(100)
            self.analysis_completed.emit(analysis_result)
            
        except Exception as e:
            self.error_occurred.emit(str(e))


class AutoSyncThread(QThread):
    """自动同步线程"""
    
    progress_updated = Signal(int)
    sync_completed = Signal(list)
    error_occurred = Signal(str)
    
    def __init__(self, enhanced_manager, video_segments, audio_path):
        super().__init__()
        self.enhanced_manager = enhanced_manager
        self.video_segments = video_segments
        self.audio_path = audio_path
        self.logger = get_logger('auto_sync_thread')
    
    def run(self):
        try:
            self.progress_updated.emit(20)
            
            # 执行自动同步
            synced_segments = self.enhanced_manager.auto_sync_video_to_music(
                self.video_segments, self.audio_path
            )
            
            self.progress_updated.emit(100)
            self.sync_completed.emit(synced_segments)
            
        except Exception as e:
            self.error_occurred.emit(str(e))


class EnhancedFeaturesDialog(QDialog):
    """增强功能对话框"""
    
    def __init__(self, parent=None, config=None):
        super().__init__(parent)
        self.config = config
        self.enhanced_manager = EnhancedFeatureManager(config)
        self.logger = get_logger('enhanced_features_dialog')
        
        self.setWindowTitle("增强功能")
        self.setMinimumSize(800, 600)
        self.resize(1000, 700)
        
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        
        # 视频分析选项卡
        self.analysis_tab = self.create_analysis_tab()
        self.tab_widget.addTab(self.analysis_tab, "🔍 视频分析")
        
        # 自动踩点选项卡
        self.sync_tab = self.create_sync_tab()
        self.tab_widget.addTab(self.sync_tab, "🎵 自动踩点")
        
        # 文字识别选项卡
        self.ocr_tab = self.create_ocr_tab()
        self.tab_widget.addTab(self.ocr_tab, "📝 文字识别")
        
        # 音频分析选项卡
        self.audio_tab = self.create_audio_tab()
        self.tab_widget.addTab(self.audio_tab, "🎶 音频分析")
        
        layout.addWidget(self.tab_widget)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.close_button = QPushButton("关闭")
        self.close_button.clicked.connect(self.accept)
        
        button_layout.addStretch()
        button_layout.addWidget(self.close_button)
        
        layout.addLayout(button_layout)
    
    def create_analysis_tab(self):
        """创建视频分析选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 文件选择
        file_group = QGroupBox("视频文件")
        file_layout = QFormLayout(file_group)
        
        self.video_path_label = QLabel("未选择文件")
        self.select_video_button = QPushButton("选择视频文件")
        self.select_video_button.clicked.connect(self.select_video_file)
        
        file_layout.addRow("视频文件:", self.video_path_label)
        file_layout.addRow("", self.select_video_button)
        
        layout.addWidget(file_group)
        
        # 分析选项
        options_group = QGroupBox("分析选项")
        options_layout = QFormLayout(options_group)
        
        self.analyze_audio_check = QCheckBox("音频分析")
        self.analyze_audio_check.setChecked(True)
        
        self.analyze_text_check = QCheckBox("文字识别")
        self.analyze_text_check.setChecked(True)
        
        self.sample_interval_spin = QDoubleSpinBox()
        self.sample_interval_spin.setRange(0.5, 10.0)
        self.sample_interval_spin.setValue(2.0)
        self.sample_interval_spin.setSuffix(" 秒")
        
        options_layout.addRow("音频分析:", self.analyze_audio_check)
        options_layout.addRow("文字识别:", self.analyze_text_check)
        options_layout.addRow("采样间隔:", self.sample_interval_spin)
        
        layout.addWidget(options_group)
        
        # 开始分析按钮
        self.start_analysis_button = QPushButton("开始分析")
        self.start_analysis_button.clicked.connect(self.start_video_analysis)
        layout.addWidget(self.start_analysis_button)
        
        # 进度条
        self.analysis_progress = QProgressBar()
        self.analysis_progress.setVisible(False)
        layout.addWidget(self.analysis_progress)
        
        # 结果显示
        self.analysis_results = QTextEdit()
        self.analysis_results.setReadOnly(True)
        layout.addWidget(self.analysis_results)
        
        return widget
    
    def create_sync_tab(self):
        """创建自动踩点选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 音频文件选择
        audio_group = QGroupBox("背景音乐")
        audio_layout = QFormLayout(audio_group)
        
        self.audio_path_label = QLabel("未选择文件")
        self.select_audio_button = QPushButton("选择音频文件")
        self.select_audio_button.clicked.connect(self.select_audio_file)
        
        audio_layout.addRow("音频文件:", self.audio_path_label)
        audio_layout.addRow("", self.select_audio_button)
        
        layout.addWidget(audio_group)
        
        # 同步选项
        sync_options_group = QGroupBox("同步选项")
        sync_options_layout = QFormLayout(sync_options_group)
        
        self.sync_tolerance_spin = QDoubleSpinBox()
        self.sync_tolerance_spin.setRange(0.1, 2.0)
        self.sync_tolerance_spin.setValue(0.5)
        self.sync_tolerance_spin.setSuffix(" 秒")
        
        self.min_confidence_spin = QDoubleSpinBox()
        self.min_confidence_spin.setRange(0.1, 1.0)
        self.min_confidence_spin.setValue(0.6)
        self.min_confidence_spin.setSingleStep(0.1)
        
        sync_options_layout.addRow("同步容差:", self.sync_tolerance_spin)
        sync_options_layout.addRow("最小置信度:", self.min_confidence_spin)
        
        layout.addWidget(sync_options_group)
        
        # 开始同步按钮
        self.start_sync_button = QPushButton("开始自动踩点")
        self.start_sync_button.clicked.connect(self.start_auto_sync)
        layout.addWidget(self.start_sync_button)
        
        # 进度条
        self.sync_progress = QProgressBar()
        self.sync_progress.setVisible(False)
        layout.addWidget(self.sync_progress)
        
        # 同步结果表格
        self.sync_results_table = QTableWidget()
        self.sync_results_table.setColumnCount(5)
        self.sync_results_table.setHorizontalHeaderLabels([
            "片段ID", "原始时间", "同步时间", "调整量", "置信度"
        ])
        self.sync_results_table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.sync_results_table)
        
        return widget
    
    def create_ocr_tab(self):
        """创建文字识别选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # OCR选项
        ocr_options_group = QGroupBox("识别选项")
        ocr_options_layout = QFormLayout(ocr_options_group)
        
        self.ocr_engine_combo = QComboBox()
        self.ocr_engine_combo.addItems(["auto", "easyocr", "tesseract"])
        
        self.ocr_confidence_spin = QDoubleSpinBox()
        self.ocr_confidence_spin.setRange(0.1, 1.0)
        self.ocr_confidence_spin.setValue(0.5)
        self.ocr_confidence_spin.setSingleStep(0.1)
        
        self.subtitle_format_combo = QComboBox()
        self.subtitle_format_combo.addItems(["srt", "vtt", "txt"])
        
        ocr_options_layout.addRow("OCR引擎:", self.ocr_engine_combo)
        ocr_options_layout.addRow("置信度阈值:", self.ocr_confidence_spin)
        ocr_options_layout.addRow("字幕格式:", self.subtitle_format_combo)
        
        layout.addWidget(ocr_options_group)
        
        # 提取文字按钮
        self.extract_text_button = QPushButton("提取视频文字")
        self.extract_text_button.clicked.connect(self.extract_video_text)
        layout.addWidget(self.extract_text_button)
        
        # 文字结果
        self.text_results = QTextEdit()
        self.text_results.setReadOnly(True)
        layout.addWidget(self.text_results)
        
        # 保存字幕按钮
        self.save_subtitle_button = QPushButton("保存字幕文件")
        self.save_subtitle_button.clicked.connect(self.save_subtitle_file)
        self.save_subtitle_button.setEnabled(False)
        layout.addWidget(self.save_subtitle_button)
        
        return widget
    
    def create_audio_tab(self):
        """创建音频分析选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 音频特征显示
        features_group = QGroupBox("音频特征")
        features_layout = QFormLayout(features_group)
        
        self.tempo_label = QLabel("--")
        self.key_label = QLabel("--")
        self.energy_label = QLabel("--")
        self.duration_label = QLabel("--")
        
        features_layout.addRow("节拍 (BPM):", self.tempo_label)
        features_layout.addRow("调性:", self.key_label)
        features_layout.addRow("能量:", self.energy_label)
        features_layout.addRow("时长:", self.duration_label)
        
        layout.addWidget(features_group)
        
        # 节拍信息
        beats_group = QGroupBox("节拍信息")
        beats_layout = QFormLayout(beats_group)
        
        self.total_beats_label = QLabel("--")
        self.strong_beats_label = QLabel("--")
        self.time_signature_label = QLabel("--")
        
        beats_layout.addRow("总节拍数:", self.total_beats_label)
        beats_layout.addRow("强拍数:", self.strong_beats_label)
        beats_layout.addRow("拍号:", self.time_signature_label)
        
        layout.addWidget(beats_group)
        
        # 建议
        recommendations_group = QGroupBox("优化建议")
        self.recommendations_text = QTextEdit()
        self.recommendations_text.setReadOnly(True)
        self.recommendations_text.setMaximumHeight(150)
        
        recommendations_layout = QVBoxLayout(recommendations_group)
        recommendations_layout.addWidget(self.recommendations_text)
        
        layout.addWidget(recommendations_group)
        
        return widget
    
    def select_video_file(self):
        """选择视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择视频文件", "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv);;所有文件 (*)"
        )
        
        if file_path:
            self.video_path_label.setText(file_path)
            self.video_file_path = file_path
    
    def select_audio_file(self):
        """选择音频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择音频文件", "",
            "音频文件 (*.mp3 *.wav *.aac *.m4a *.flac);;所有文件 (*)"
        )
        
        if file_path:
            self.audio_path_label.setText(file_path)
            self.audio_file_path = file_path
    
    def start_video_analysis(self):
        """开始视频分析"""
        if not hasattr(self, 'video_file_path'):
            QMessageBox.warning(self, "警告", "请先选择视频文件")
            return
        
        self.start_analysis_button.setEnabled(False)
        self.analysis_progress.setVisible(True)
        self.analysis_progress.setValue(0)
        
        # 启动分析线程
        self.analysis_thread = VideoAnalysisThread(
            self.enhanced_manager, self.video_file_path
        )
        self.analysis_thread.progress_updated.connect(self.analysis_progress.setValue)
        self.analysis_thread.analysis_completed.connect(self.on_analysis_completed)
        self.analysis_thread.error_occurred.connect(self.on_analysis_error)
        self.analysis_thread.start()
    
    def on_analysis_completed(self, result):
        """分析完成"""
        self.start_analysis_button.setEnabled(True)
        self.analysis_progress.setVisible(False)
        
        # 显示结果
        self.display_analysis_results(result)
        
        # 更新音频选项卡
        if result.get('audio_features'):
            self.update_audio_features(result['audio_features'])
        
        if result.get('music_structure'):
            self.update_music_structure(result['music_structure'])
    
    def on_analysis_error(self, error_msg):
        """分析错误"""
        self.start_analysis_button.setEnabled(True)
        self.analysis_progress.setVisible(False)
        QMessageBox.critical(self, "错误", f"视频分析失败:\n{error_msg}")
    
    def display_analysis_results(self, result):
        """显示分析结果"""
        text = "=== 视频分析结果 ===\n\n"
        
        # 音频特征
        if result.get('audio_features'):
            features = result['audio_features']
            text += "🎵 音频特征:\n"
            text += f"  节拍: {features.get('tempo', 0):.1f} BPM\n"
            text += f"  调性: {features.get('key', 'Unknown')}\n"
            text += f"  能量: {features.get('energy', 0):.2f}\n"
            text += f"  时长: {features.get('duration', 0):.1f} 秒\n\n"
        
        # 文字内容
        if result.get('text_content'):
            text_content = result['text_content']
            text += f"📝 文字识别: 发现 {text_content['total_text_regions']} 个文字区域\n\n"
            
            for i, text_info in enumerate(text_content['merged_texts'][:5], 1):
                text += f"  {i}. \"{text_info['text']}\" (置信度: {text_info['confidence']:.2f})\n"
            
            if len(text_content['merged_texts']) > 5:
                text += f"  ... 还有 {len(text_content['merged_texts']) - 5} 个文字区域\n"
            text += "\n"
        
        # 建议
        if result.get('recommendations'):
            text += "💡 优化建议:\n"
            for rec in result['recommendations']:
                text += f"  • {rec}\n"
        
        self.analysis_results.setText(text)
    
    def update_audio_features(self, features):
        """更新音频特征显示"""
        self.tempo_label.setText(f"{features.get('tempo', 0):.1f}")
        self.key_label.setText(features.get('key', 'Unknown'))
        self.energy_label.setText(f"{features.get('energy', 0):.2f}")
        self.duration_label.setText(f"{features.get('duration', 0):.1f} 秒")
    
    def update_music_structure(self, structure):
        """更新音乐结构显示"""
        beat_points = structure.get('beat_points', [])
        strong_beats = structure.get('strong_beats', [])
        beat_pattern = structure.get('beat_pattern', {})
        
        self.total_beats_label.setText(str(len(beat_points)))
        self.strong_beats_label.setText(str(len(strong_beats)))
        self.time_signature_label.setText(beat_pattern.get('time_signature', 'Unknown'))
    
    def start_auto_sync(self):
        """开始自动同步"""
        if not hasattr(self, 'audio_file_path'):
            QMessageBox.warning(self, "警告", "请先选择音频文件")
            return
        
        # 这里需要从主窗口获取视频片段信息
        # 暂时使用示例数据
        video_segments = [
            {'start_time': 0.0, 'duration': 3.0, 'file_path': 'segment1.mp4'},
            {'start_time': 3.0, 'duration': 2.5, 'file_path': 'segment2.mp4'},
        ]
        
        self.start_sync_button.setEnabled(False)
        self.sync_progress.setVisible(True)
        self.sync_progress.setValue(0)
        
        # 启动同步线程
        self.sync_thread = AutoSyncThread(
            self.enhanced_manager, video_segments, self.audio_file_path
        )
        self.sync_thread.progress_updated.connect(self.sync_progress.setValue)
        self.sync_thread.sync_completed.connect(self.on_sync_completed)
        self.sync_thread.error_occurred.connect(self.on_sync_error)
        self.sync_thread.start()
    
    def on_sync_completed(self, synced_segments):
        """同步完成"""
        self.start_sync_button.setEnabled(True)
        self.sync_progress.setVisible(False)
        
        # 显示同步结果
        self.display_sync_results(synced_segments)
    
    def on_sync_error(self, error_msg):
        """同步错误"""
        self.start_sync_button.setEnabled(True)
        self.sync_progress.setVisible(False)
        QMessageBox.critical(self, "错误", f"自动同步失败:\n{error_msg}")
    
    def display_sync_results(self, synced_segments):
        """显示同步结果"""
        self.sync_results_table.setRowCount(len(synced_segments))
        
        for i, segment in enumerate(synced_segments):
            self.sync_results_table.setItem(i, 0, QTableWidgetItem(segment.get('id', str(i))))
            self.sync_results_table.setItem(i, 1, QTableWidgetItem(f"{segment.get('start_time', 0):.2f}s"))
            self.sync_results_table.setItem(i, 2, QTableWidgetItem(f"{segment.get('start_time', 0):.2f}s"))
            self.sync_results_table.setItem(i, 3, QTableWidgetItem("0.00s"))  # 需要计算调整量
            self.sync_results_table.setItem(i, 4, QTableWidgetItem("0.80"))   # 需要获取置信度
    
    def extract_video_text(self):
        """提取视频文字"""
        if not hasattr(self, 'video_file_path'):
            QMessageBox.warning(self, "警告", "请先选择视频文件")
            return
        
        try:
            subtitle_format = self.subtitle_format_combo.currentText()
            subtitle_content = self.enhanced_manager.extract_video_text(
                self.video_file_path, subtitle_format
            )
            
            if subtitle_content:
                self.text_results.setText(subtitle_content)
                self.save_subtitle_button.setEnabled(True)
                self.subtitle_content = subtitle_content
            else:
                self.text_results.setText("未检测到文字内容")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"文字提取失败:\n{str(e)}")
    
    def save_subtitle_file(self):
        """保存字幕文件"""
        if not hasattr(self, 'subtitle_content'):
            return
        
        subtitle_format = self.subtitle_format_combo.currentText()
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存字幕文件", f"subtitle.{subtitle_format}",
            f"{subtitle_format.upper()} 文件 (*.{subtitle_format});;所有文件 (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.subtitle_content)
                QMessageBox.information(self, "成功", f"字幕文件已保存到:\n{file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存失败:\n{str(e)}")
