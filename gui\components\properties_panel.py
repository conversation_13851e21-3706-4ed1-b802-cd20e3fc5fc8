#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
属性面板组件
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QGroupBox, QScrollArea, QFrame, QSpinBox,
    QDoubleSpinBox, QSlider, QComboBox, QPushButton,
    QLineEdit, QTextEdit
)
from PySide6.QtCore import Qt, Signal

class PropertiesPanel(QWidget):
    """属性面板"""
    
    property_changed = Signal(str, object)
    
    def __init__(self):
        super().__init__()
        self.current_video_info = None
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # 创建内容组件
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        
        # 视频信息组
        self.create_video_info_group(content_layout)
        
        # 选择信息组
        self.create_selection_info_group(content_layout)
        
        # 效果组
        self.create_effects_group(content_layout)
        
        # 导出设置组
        self.create_export_settings_group(content_layout)
        
        # 添加弹性空间
        content_layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)
    
    def create_video_info_group(self, parent_layout):
        """创建视频信息组"""
        group = QGroupBox("视频信息")
        layout = QVBoxLayout(group)
        
        # 文件名
        self.filename_label = QLabel("文件名: 无")
        layout.addWidget(self.filename_label)
        
        # 分辨率
        self.resolution_label = QLabel("分辨率: 无")
        layout.addWidget(self.resolution_label)
        
        # 时长
        self.duration_label = QLabel("时长: 无")
        layout.addWidget(self.duration_label)
        
        # 帧率
        self.fps_label = QLabel("帧率: 无")
        layout.addWidget(self.fps_label)
        
        # 编码格式
        self.codec_label = QLabel("编码: 无")
        layout.addWidget(self.codec_label)
        
        # 比特率
        self.bitrate_label = QLabel("比特率: 无")
        layout.addWidget(self.bitrate_label)
        
        parent_layout.addWidget(group)
    
    def create_selection_info_group(self, parent_layout):
        """创建选择信息组"""
        group = QGroupBox("选择信息")
        layout = QVBoxLayout(group)
        
        # 选择区域
        self.selection_label = QLabel("选择: 无")
        layout.addWidget(self.selection_label)
        
        # 选择时长
        self.selection_duration_label = QLabel("时长: 无")
        layout.addWidget(self.selection_duration_label)
        
        parent_layout.addWidget(group)
    
    def create_effects_group(self, parent_layout):
        """创建效果组"""
        group = QGroupBox("视频效果")
        layout = QVBoxLayout(group)
        
        # 亮度
        brightness_layout = QHBoxLayout()
        brightness_layout.addWidget(QLabel("亮度:"))
        self.brightness_slider = QSlider(Qt.Horizontal)
        self.brightness_slider.setRange(-100, 100)
        self.brightness_slider.setValue(0)
        self.brightness_slider.valueChanged.connect(
            lambda v: self.property_changed.emit("brightness", v)
        )
        brightness_layout.addWidget(self.brightness_slider)
        
        brightness_value = QLabel("0")
        self.brightness_slider.valueChanged.connect(
            lambda v: brightness_value.setText(str(v))
        )
        brightness_layout.addWidget(brightness_value)
        layout.addLayout(brightness_layout)
        
        # 对比度
        contrast_layout = QHBoxLayout()
        contrast_layout.addWidget(QLabel("对比度:"))
        self.contrast_slider = QSlider(Qt.Horizontal)
        self.contrast_slider.setRange(-100, 100)
        self.contrast_slider.setValue(0)
        self.contrast_slider.valueChanged.connect(
            lambda v: self.property_changed.emit("contrast", v)
        )
        contrast_layout.addWidget(self.contrast_slider)
        
        contrast_value = QLabel("0")
        self.contrast_slider.valueChanged.connect(
            lambda v: contrast_value.setText(str(v))
        )
        contrast_layout.addWidget(contrast_value)
        layout.addLayout(contrast_layout)
        
        # 饱和度
        saturation_layout = QHBoxLayout()
        saturation_layout.addWidget(QLabel("饱和度:"))
        self.saturation_slider = QSlider(Qt.Horizontal)
        self.saturation_slider.setRange(-100, 100)
        self.saturation_slider.setValue(0)
        self.saturation_slider.valueChanged.connect(
            lambda v: self.property_changed.emit("saturation", v)
        )
        saturation_layout.addWidget(self.saturation_slider)
        
        saturation_value = QLabel("0")
        self.saturation_slider.valueChanged.connect(
            lambda v: saturation_value.setText(str(v))
        )
        saturation_layout.addWidget(saturation_value)
        layout.addLayout(saturation_layout)
        
        # 重置按钮
        reset_btn = QPushButton("重置效果")
        reset_btn.clicked.connect(self.reset_effects)
        layout.addWidget(reset_btn)
        
        parent_layout.addWidget(group)
    
    def create_export_settings_group(self, parent_layout):
        """创建导出设置组"""
        group = QGroupBox("导出设置")
        layout = QVBoxLayout(group)
        
        # 输出格式
        format_layout = QHBoxLayout()
        format_layout.addWidget(QLabel("格式:"))
        self.format_combo = QComboBox()
        self.format_combo.addItems(["MP4", "AVI", "MOV", "MKV"])
        format_layout.addWidget(self.format_combo)
        layout.addLayout(format_layout)
        
        # 质量
        quality_layout = QHBoxLayout()
        quality_layout.addWidget(QLabel("质量:"))
        self.quality_combo = QComboBox()
        self.quality_combo.addItems(["低", "中", "高", "最高"])
        self.quality_combo.setCurrentText("高")
        quality_layout.addWidget(self.quality_combo)
        layout.addLayout(quality_layout)
        
        # 帧率
        fps_layout = QHBoxLayout()
        fps_layout.addWidget(QLabel("帧率:"))
        self.fps_spinbox = QSpinBox()
        self.fps_spinbox.setRange(1, 60)
        self.fps_spinbox.setValue(30)
        fps_layout.addWidget(self.fps_spinbox)
        layout.addLayout(fps_layout)
        
        parent_layout.addWidget(group)
    
    def update_video_info(self, video_info):
        """更新视频信息"""
        self.current_video_info = video_info
        
        if video_info:
            # 更新文件名
            filename = video_info.file_path.name
            self.filename_label.setText(f"文件名: {filename}")
            
            # 更新分辨率
            self.resolution_label.setText(f"分辨率: {video_info.width}x{video_info.height}")
            
            # 更新时长
            duration_str = self.format_time(video_info.duration)
            self.duration_label.setText(f"时长: {duration_str}")
            
            # 更新帧率
            self.fps_label.setText(f"帧率: {video_info.fps:.2f} fps")
            
            # 更新编码格式
            self.codec_label.setText(f"编码: {video_info.codec}")
            
            # 更新比特率
            if video_info.bitrate > 0:
                bitrate_str = self.format_bitrate(video_info.bitrate)
                self.bitrate_label.setText(f"比特率: {bitrate_str}")
            else:
                self.bitrate_label.setText("比特率: 未知")
        else:
            self.filename_label.setText("文件名: 无")
            self.resolution_label.setText("分辨率: 无")
            self.duration_label.setText("时长: 无")
            self.fps_label.setText("帧率: 无")
            self.codec_label.setText("编码: 无")
            self.bitrate_label.setText("比特率: 无")
    
    def update_selection_info(self, start_time: float, end_time: float):
        """更新选择信息"""
        start_str = self.format_time(start_time)
        end_str = self.format_time(end_time)
        self.selection_label.setText(f"选择: {start_str} - {end_str}")
        
        duration = end_time - start_time
        duration_str = self.format_time(duration)
        self.selection_duration_label.setText(f"时长: {duration_str}")
    
    def clear_selection_info(self):
        """清除选择信息"""
        self.selection_label.setText("选择: 无")
        self.selection_duration_label.setText("时长: 无")
    
    def reset_effects(self):
        """重置效果"""
        self.brightness_slider.setValue(0)
        self.contrast_slider.setValue(0)
        self.saturation_slider.setValue(0)
    
    def get_export_settings(self) -> dict:
        """获取导出设置"""
        return {
            'format': self.format_combo.currentText().lower(),
            'quality': self.quality_combo.currentText(),
            'fps': self.fps_spinbox.value()
        }
    
    def format_time(self, seconds: float) -> str:
        """格式化时间"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        return f"{hours:02d}:{minutes:02d}:{secs:02d}"
    
    def format_bitrate(self, bitrate: int) -> str:
        """格式化比特率"""
        if bitrate < 1000:
            return f"{bitrate} bps"
        elif bitrate < 1000000:
            return f"{bitrate / 1000:.1f} kbps"
        else:
            return f"{bitrate / 1000000:.1f} Mbps" 