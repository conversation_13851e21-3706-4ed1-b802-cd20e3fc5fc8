#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from PySide6.QtWidgets import QWidget
from PySide6.QtCore import Qt
from PySide6.QtGui import QPainter, QColor, QPen, QBrush, QPolygon
from PySide6.QtCore import QPoint

class TrimHandlesOverlay(QWidget):
    """专门的游标绘制层，覆盖整个轨道，支持往外拖扩展"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        print(f"🔧 TrimHandlesOverlay 初始化: parent={parent}")
        
        # 设置透明背景，不阻挡鼠标事件
        self.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground, True)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        
        # 确保始终在最顶层
        self.raise_()
        # 设置最高层级显示
        self.setAttribute(Qt.WidgetAttribute.WA_AlwaysStackOnTop, True)
        
        # 防止重复绘制的标志
        self._is_painting = False
        
        print(f"🔧 TrimHandlesOverlay 属性设置完成")
        print(f"   - WA_TransparentForMouseEvents: {self.testAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents)}")
        print(f"   - WA_NoSystemBackground: {self.testAttribute(Qt.WidgetAttribute.WA_NoSystemBackground)}")
        print(f"   - WA_TranslucentBackground: {self.testAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)}")
        print(f"   - WA_AlwaysStackOnTop: {self.testAttribute(Qt.WidgetAttribute.WA_AlwaysStackOnTop)}")
        
    def paintEvent(self, event):
        """绘制所有素材块的游标 - 最后绘制确保层级最高"""
        # 防止重复绘制
        if self._is_painting:
            return
        self._is_painting = True

        painter = None
        try:
            # 🔧 关键修复：先让所有子组件完成绘制
            super().paintEvent(event)

            # 检查父组件是否存在且有媒体块
            parent = self.parent()
            if not parent:
                return

            # 避免循环导入，使用类名字符串查找
            blocks = []
            for child in parent.children():
                if child.__class__.__name__ == 'VideoThumbnailBlock':
                    blocks.append(child)

            # 如果没有媒体块，不需要绘制游标
            if not blocks:
                return

            # 然后绘制游标，确保在最顶层
            painter = QPainter(self)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)

            # 使用最高级别的合成模式，确保游标不被遮挡
            painter.setCompositionMode(QPainter.CompositionMode.CompositionMode_SourceOver)

            # 绘制所有VideoThumbnailBlock的裁剪游标和扩展区
            for block in blocks:
                self.draw_trim_handles_for_block(painter, block)

        except Exception as e:
            print(f"❌ TrimHandlesOverlay paintEvent 错误: {e}")
        finally:
            # 🔧 修复：确保 QPainter 正确结束
            if painter and painter.isActive():
                painter.end()
            self._is_painting = False
    
    def draw_trim_handles_for_block(self, painter, block):
        """为指定的媒体块绘制裁剪游标"""
        try:
            if not block.isVisible():
                return
            
            # 获取媒体块的几何信息
            block_rect = block.geometry()
            block_x = block_rect.x()
            block_y = block_rect.y()
            block_width = block_rect.width()
            block_height = block_rect.height()
            
            # 获取裁剪状态
            if block.left_trim_dragging or block.right_trim_dragging:
                # 使用预览状态
                left_trim = getattr(block, 'preview_left_trim_pos', 0)
                right_trim = getattr(block, 'preview_right_trim_pos', 0)
            else:
                # 使用实际状态
                left_trim = getattr(block, 'left_trim_pos', 0)
                right_trim = getattr(block, 'right_trim_pos', 0)
            
            # 绘制左游标
            if True:  # 始终显示游标
                left_x = block_x + left_trim
                self.draw_trim_handle(painter, left_x, block_y, block_height, 'left', 
                                    block.left_trim_dragging)
            
            # 绘制右游标
            if True:  # 始终显示游标
                right_x = block_x + block_width - right_trim
                self.draw_trim_handle(painter, right_x, block_y, block_height, 'right', 
                                    block.right_trim_dragging)
                
        except Exception as e:
            print(f"绘制媒体块游标失败: {e}")
    
    def draw_trim_handle(self, painter, x, y, height, side, is_dragging):
        """绘制单个裁剪游标"""
        try:
            # 游标颜色
            if is_dragging:
                color = QColor(0, 200, 150, 255)  # 拖拽时的高亮色
                border_color = QColor(255, 255, 255, 255)
            else:
                color = QColor(255, 255, 255, 200)  # 普通状态
                border_color = QColor(0, 0, 0, 150)
            
            # 游标宽度
            handle_width = 8
            
            # 绘制游标主体（垂直线）
            painter.setPen(QPen(color, 3))
            painter.drawLine(int(x), y, int(x), y + height)
            
            # 绘制游标顶部和底部的三角形指示器
            triangle_size = 6
            
            # 顶部三角形
            if side == 'left':
                # 左游标：三角形指向右
                triangle = QPolygon([
                    QPoint(int(x), y),
                    QPoint(int(x + triangle_size), y + triangle_size),
                    QPoint(int(x), y + triangle_size * 2)
                ])
            else:
                # 右游标：三角形指向左
                triangle = QPolygon([
                    QPoint(int(x), y),
                    QPoint(int(x - triangle_size), y + triangle_size),
                    QPoint(int(x), y + triangle_size * 2)
                ])
            
            painter.setBrush(QBrush(color))
            painter.setPen(QPen(border_color, 1))
            painter.drawPolygon(triangle)
            
            # 底部三角形
            if side == 'left':
                # 左游标：三角形指向右
                triangle_bottom = QPolygon([
                    QPoint(int(x), y + height),
                    QPoint(int(x + triangle_size), y + height - triangle_size),
                    QPoint(int(x), y + height - triangle_size * 2)
                ])
            else:
                # 右游标：三角形指向左
                triangle_bottom = QPolygon([
                    QPoint(int(x), y + height),
                    QPoint(int(x - triangle_size), y + height - triangle_size),
                    QPoint(int(x), y + height - triangle_size * 2)
                ])
            
            painter.setBrush(QBrush(color))
            painter.setPen(QPen(border_color, 1))
            painter.drawPolygon(triangle_bottom)
            
        except Exception as e:
            print(f"绘制游标失败: {e}")
    
    def update_trim_handles(self):
        """更新游标显示"""
        self.update()
        
    def resizeEvent(self, event):
        """调整大小事件"""
        super().resizeEvent(event)
        # 确保覆盖整个父组件
        if self.parent():
            self.setGeometry(self.parent().rect())
