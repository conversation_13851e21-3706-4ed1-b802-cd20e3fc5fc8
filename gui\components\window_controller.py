"""
窗口控制器 - 处理无边框窗口的拖拽、调整大小等功能
"""

from PySide6.QtCore import Qt, QPoint, QRect
from PySide6.QtGui import QCursor
from PySide6.QtWidgets import QWidget


class WindowController:
    """窗口控制器 - 处理无边框窗口的各种操作"""
    
    def __init__(self, window):
        self.window = window
        self.drag_position = QPoint()
        self.is_dragging = False
        self.is_resizing = False
        self.resize_direction = None
        self.border_width = 5  # 边框宽度，用于调整大小
        
        # 保存窗口状态
        self.is_maximized = False
        self.normal_geometry = QRect()
        
    def handle_mouse_press(self, event):
        """处理鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.window.frameGeometry().topLeft()
            self.is_dragging = True
            
            # 检查是否在边框区域（用于调整大小）
            self.resize_direction = self.get_resize_direction(event.position().toPoint())
            if self.resize_direction:
                self.is_resizing = True
                self.is_dragging = False
                
    def handle_mouse_move(self, event):
        """处理鼠标移动事件"""
        if self.is_dragging and event.buttons() == Qt.MouseButton.LeftButton:
            # 拖拽窗口
            if not self.is_maximized:
                self.window.move(event.globalPosition().toPoint() - self.drag_position)
        elif self.is_resizing and event.buttons() == Qt.MouseButton.LeftButton:
            # 调整窗口大小
            self.resize_window(event.globalPosition().toPoint())
        else:
            # 更新鼠标光标
            self.update_cursor(event.position().toPoint())
            
    def handle_mouse_release(self, event):
        """处理鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.is_dragging = False
            self.is_resizing = False
            self.resize_direction = None
            self.window.setCursor(Qt.CursorShape.ArrowCursor)
            
    def handle_double_click(self, event):
        """处理双击事件 - 最大化/还原窗口"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.toggle_maximize()
            
    def get_resize_direction(self, pos):
        """获取调整大小的方向"""
        if self.is_maximized:
            return None
            
        rect = self.window.rect()
        x, y = pos.x(), pos.y()
        
        # 检查各个边框区域
        left = x <= self.border_width
        right = x >= rect.width() - self.border_width
        top = y <= self.border_width
        bottom = y >= rect.height() - self.border_width
        
        if top and left:
            return "top_left"
        elif top and right:
            return "top_right"
        elif bottom and left:
            return "bottom_left"
        elif bottom and right:
            return "bottom_right"
        elif top:
            return "top"
        elif bottom:
            return "bottom"
        elif left:
            return "left"
        elif right:
            return "right"
        else:
            return None
            
    def update_cursor(self, pos):
        """更新鼠标光标"""
        direction = self.get_resize_direction(pos)
        
        if direction == "top_left" or direction == "bottom_right":
            self.window.setCursor(Qt.CursorShape.SizeFDiagCursor)
        elif direction == "top_right" or direction == "bottom_left":
            self.window.setCursor(Qt.CursorShape.SizeBDiagCursor)
        elif direction == "top" or direction == "bottom":
            self.window.setCursor(Qt.CursorShape.SizeVerCursor)
        elif direction == "left" or direction == "right":
            self.window.setCursor(Qt.CursorShape.SizeHorCursor)
        else:
            self.window.setCursor(Qt.CursorShape.ArrowCursor)
            
    def resize_window(self, global_pos):
        """调整窗口大小"""
        if not self.resize_direction:
            return
            
        current_rect = self.window.geometry()
        
        if "left" in self.resize_direction:
            new_width = current_rect.right() - global_pos.x()
            if new_width >= self.window.minimumWidth():
                current_rect.setLeft(global_pos.x())
                
        if "right" in self.resize_direction:
            new_width = global_pos.x() - current_rect.left()
            if new_width >= self.window.minimumWidth():
                current_rect.setRight(global_pos.x())
                
        if "top" in self.resize_direction:
            new_height = current_rect.bottom() - global_pos.y()
            if new_height >= self.window.minimumHeight():
                current_rect.setTop(global_pos.y())
                
        if "bottom" in self.resize_direction:
            new_height = global_pos.y() - current_rect.top()
            if new_height >= self.window.minimumHeight():
                current_rect.setBottom(global_pos.y())
                
        self.window.setGeometry(current_rect)
        
    def toggle_maximize(self):
        """切换最大化状态"""
        if self.is_maximized:
            self.restore_window()
        else:
            self.maximize_window()
            
    def maximize_window(self):
        """最大化窗口"""
        if not self.is_maximized:
            self.normal_geometry = self.window.geometry()
            
            # 获取屏幕可用区域
            screen = self.window.screen()
            available_geometry = screen.availableGeometry()
            self.window.setGeometry(available_geometry)
            
            self.is_maximized = True
            
            # 更新标题栏按钮状态
            if hasattr(self.window, 'title_bar'):
                self.window.title_bar.update_maximize_button(True)
                
    def restore_window(self):
        """还原窗口"""
        if self.is_maximized:
            self.window.setGeometry(self.normal_geometry)
            self.is_maximized = False
            
            # 更新标题栏按钮状态
            if hasattr(self.window, 'title_bar'):
                self.window.title_bar.update_maximize_button(False)
                
    def minimize_window(self):
        """最小化窗口"""
        self.window.showMinimized()
        
    def close_window(self):
        """关闭窗口"""
        self.window.close()
        
    def is_in_title_bar_area(self, pos):
        """检查位置是否在标题栏区域"""
        if hasattr(self.window, 'title_bar'):
            title_bar_rect = self.window.title_bar.geometry()
            return title_bar_rect.contains(pos)
        return False
