#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量处理对话框
用于批量应用理发店模板到多个视频
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Optional, Any

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QTabWidget,
    QLabel, QPushButton, QLineEdit, QTextEdit, QComboBox, QSpinBox,
    QListWidget, QListWidgetItem, QTableWidget, QTableWidgetItem,
    QFileDialog, QMessageBox, QProgressBar, QGroupBox, QCheckBox,
    QHeaderView, QSplitter, QTreeWidget, QTreeWidgetItem, QFrame,
    QScrollArea, QWidget
)
from PySide6.QtCore import Qt, Signal, QTimer, QThread
from PySide6.QtGui import QFont, QIcon, QPixmap, QDragEnterEvent, QDropEvent

from core.templates.template_manager import HairSalonTemplateManager
from core.batch_processing.batch_processor import HairSalonBatchProcessor, BatchProcessingJob

class VideoDropWidget(QListWidget):
    """支持拖拽的视频列表组件"""
    
    files_dropped = Signal(list)  # 文件列表
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.setDragDropMode(QListWidget.DragDropMode.DropOnly)
        
        # 设置样式
        self.setStyleSheet("""
            QListWidget {
                border: 2px dashed #cccccc;
                border-radius: 10px;
                background-color: #f8f8f8;
                color: #666666;
                font-size: 14px;
                padding: 20px;
            }
            QListWidget:hover {
                border-color: #4CAF50;
                background-color: #f0f8f0;
            }
        """)
        
        # 添加提示文本
        self.setPlaceholderText("拖拽视频文件到这里，或点击'添加文件'按钮")
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
        else:
            event.ignore()
    
    def dropEvent(self, event: QDropEvent):
        """拖拽放置事件"""
        files = []
        for url in event.mimeData().urls():
            file_path = url.toLocalFile()
            if file_path.lower().endswith(('.mp4', '.avi', '.mov', '.mkv', '.wmv')):
                files.append(file_path)
        
        if files:
            self.files_dropped.emit(files)
        
        event.acceptProposedAction()

class BatchVideoItem(QWidget):
    """批量视频项组件"""
    
    item_changed = Signal(int, dict)  # index, data
    item_removed = Signal(int)  # index
    
    def __init__(self, index: int, video_data: Dict, parent=None):
        super().__init__(parent)
        self.index = index
        self.video_data = video_data
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 序号
        self.index_label = QLabel(f"{self.index + 1}.")
        self.index_label.setMinimumWidth(30)
        layout.addWidget(self.index_label)
        
        # 批次名称
        self.batch_name_edit = QLineEdit(self.video_data.get('batch_name', ''))
        self.batch_name_edit.setPlaceholderText("客户名称_日期")
        self.batch_name_edit.textChanged.connect(self.on_data_changed)
        layout.addWidget(self.batch_name_edit)
        
        # 视频文件按钮
        video_layout = QVBoxLayout()
        
        self.before_btn = QPushButton("改造前")
        self.before_btn.clicked.connect(lambda: self.browse_video('before'))
        self.before_btn.setToolTip(self.video_data.get('videos', {}).get('before', '未选择'))
        video_layout.addWidget(self.before_btn)
        
        self.process_btn = QPushButton("美发过程")
        self.process_btn.clicked.connect(lambda: self.browse_video('process'))
        self.process_btn.setToolTip(self.video_data.get('videos', {}).get('process', '未选择'))
        video_layout.addWidget(self.process_btn)
        
        self.after_btn = QPushButton("改造后")
        self.after_btn.clicked.connect(lambda: self.browse_video('after'))
        self.after_btn.setToolTip(self.video_data.get('videos', {}).get('after', '未选择'))
        video_layout.addWidget(self.after_btn)
        
        layout.addLayout(video_layout)
        
        # 自定义设置
        settings_layout = QVBoxLayout()
        
        self.watermark_edit = QLineEdit(
            self.video_data.get('custom_settings', {}).get('watermark_text', '')
        )
        self.watermark_edit.setPlaceholderText("自定义水印文字")
        self.watermark_edit.textChanged.connect(self.on_data_changed)
        settings_layout.addWidget(self.watermark_edit)
        
        volume_layout = QHBoxLayout()
        volume_layout.addWidget(QLabel("音量:"))
        self.volume_spin = QSpinBox()
        self.volume_spin.setRange(0, 100)
        self.volume_spin.setValue(
            int(self.video_data.get('custom_settings', {}).get('music_volume', 0.7) * 100)
        )
        self.volume_spin.setSuffix("%")
        self.volume_spin.valueChanged.connect(self.on_data_changed)
        volume_layout.addWidget(self.volume_spin)
        settings_layout.addLayout(volume_layout)
        
        layout.addLayout(settings_layout)
        
        # 删除按钮
        remove_btn = QPushButton("✖")
        remove_btn.setMaximumSize(30, 30)
        remove_btn.clicked.connect(lambda: self.item_removed.emit(self.index))
        layout.addWidget(remove_btn)
        
        # 更新按钮状态
        self.update_button_states()
    
    def browse_video(self, video_type: str):
        """浏览视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, f"选择{self.get_video_type_name(video_type)}视频",
            "", "视频文件 (*.mp4 *.avi *.mov *.mkv);;所有文件 (*.*)"
        )
        
        if file_path:
            if 'videos' not in self.video_data:
                self.video_data['videos'] = {}
            self.video_data['videos'][video_type] = file_path
            
            # 更新按钮提示
            button = getattr(self, f"{video_type}_btn")
            button.setToolTip(file_path)
            
            self.update_button_states()
            self.on_data_changed()
    
    def get_video_type_name(self, video_type: str) -> str:
        """获取视频类型中文名"""
        names = {
            'before': '改造前',
            'process': '美发过程',
            'after': '改造后'
        }
        return names.get(video_type, video_type)
    
    def update_button_states(self):
        """更新按钮状态"""
        videos = self.video_data.get('videos', {})
        
        for video_type in ['before', 'process', 'after']:
            button = getattr(self, f"{video_type}_btn")
            if video_type in videos and videos[video_type]:
                button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; }")
            else:
                button.setStyleSheet("")
    
    def on_data_changed(self):
        """数据改变"""
        # 更新批次名称
        self.video_data['batch_name'] = self.batch_name_edit.text()
        
        # 更新输出文件名
        batch_name = self.batch_name_edit.text().strip()
        if batch_name:
            self.video_data['output_name'] = f"{batch_name}_剪辑成品.mp4"
        
        # 更新自定义设置
        if 'custom_settings' not in self.video_data:
            self.video_data['custom_settings'] = {}
        
        self.video_data['custom_settings']['watermark_text'] = self.watermark_edit.text()
        self.video_data['custom_settings']['music_volume'] = self.volume_spin.value() / 100.0
        
        self.item_changed.emit(self.index, self.video_data)
    
    def get_video_data(self) -> Dict:
        """获取视频数据"""
        return self.video_data.copy()
    
    def is_complete(self) -> bool:
        """检查是否完整"""
        videos = self.video_data.get('videos', {})
        required_videos = ['before', 'process', 'after']
        
        return (
            self.video_data.get('batch_name', '').strip() and
            all(videos.get(vtype) for vtype in required_videos)
        )

class BatchProgressDialog(QDialog):
    """批量处理进度对话框"""
    
    def __init__(self, job_id: str, batch_processor: HairSalonBatchProcessor, parent=None):
        super().__init__(parent)
        self.job_id = job_id
        self.batch_processor = batch_processor
        self.init_ui()
        
        # 设置进度回调
        self.batch_processor.set_progress_callback(self.on_progress_update)
        self.batch_processor.set_completion_callback(self.on_completion)
        
        # 定时器更新状态
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_status)
        self.update_timer.start(1000)  # 每秒更新
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("批量处理进度")
        self.setModal(True)
        self.resize(500, 300)
        
        layout = QVBoxLayout(self)
        
        # 任务信息
        info_group = QGroupBox("任务信息")
        info_layout = QGridLayout(info_group)
        
        info_layout.addWidget(QLabel("任务ID:"), 0, 0)
        self.job_id_label = QLabel(self.job_id)
        info_layout.addWidget(self.job_id_label, 0, 1)
        
        info_layout.addWidget(QLabel("状态:"), 1, 0)
        self.status_label = QLabel("运行中...")
        info_layout.addWidget(self.status_label, 1, 1)
        
        info_layout.addWidget(QLabel("进度:"), 2, 0)
        self.progress_label = QLabel("0/0")
        info_layout.addWidget(self.progress_label, 2, 1)
        
        layout.addWidget(info_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        layout.addWidget(self.progress_bar)
        
        # 日志
        log_group = QGroupBox("处理日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(150)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.cancel_btn = QPushButton("取消任务")
        self.cancel_btn.clicked.connect(self.cancel_job)
        button_layout.addWidget(self.cancel_btn)
        
        button_layout.addStretch()
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.accept)
        self.close_btn.setEnabled(False)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
        
        # 添加初始日志
        self.add_log(f"开始批量处理任务: {self.job_id}")
    
    def on_progress_update(self, job_id: str, progress: float, current_batch: int, total_batches: int):
        """进度更新回调"""
        if job_id == self.job_id:
            self.progress_bar.setValue(int(progress))
            self.progress_label.setText(f"{current_batch}/{total_batches}")
            self.add_log(f"正在处理第 {current_batch}/{total_batches} 个批次... ({progress:.1f}%)")
    
    def on_completion(self, job_id: str, status: str, success_count: int, error_count: int):
        """完成回调"""
        if job_id == self.job_id:
            self.update_timer.stop()
            
            self.status_label.setText(f"已完成 ({status})")
            self.cancel_btn.setEnabled(False)
            self.close_btn.setEnabled(True)
            
            if status == "completed":
                self.add_log(f"✅ 批量处理完成！成功: {success_count}, 失败: {error_count}")
                if error_count == 0:
                    QMessageBox.information(self, "完成", f"所有 {success_count} 个视频都处理成功！")
                else:
                    QMessageBox.warning(self, "部分完成", 
                                      f"处理完成：成功 {success_count} 个，失败 {error_count} 个")
            else:
                self.add_log(f"❌ 批量处理失败或被取消")
                QMessageBox.critical(self, "失败", "批量处理失败或被取消")
    
    def update_status(self):
        """更新状态"""
        status = self.batch_processor.get_job_status(self.job_id)
        if status:
            if status['status'] in ['completed', 'failed', 'cancelled']:
                self.update_timer.stop()
    
    def add_log(self, message: str):
        """添加日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
    
    def cancel_job(self):
        """取消任务"""
        reply = QMessageBox.question(
            self, "确认取消",
            "确定要取消当前批量处理任务吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            success = self.batch_processor.cancel_job(self.job_id)
            if success:
                self.add_log("任务已取消")
                self.status_label.setText("已取消")
                self.cancel_btn.setEnabled(False)
                self.close_btn.setEnabled(True)

class HairSalonBatchDialog(QDialog):
    """理发店批量处理对话框"""
    
    def __init__(self, template_manager: HairSalonTemplateManager,
                 batch_processor: HairSalonBatchProcessor, parent=None):
        super().__init__(parent)
        self.template_manager = template_manager
        self.batch_processor = batch_processor
        
        self.video_items = []  # 批量视频项列表
        
        self.init_ui()
        self.load_templates()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("理发店批量视频处理")
        self.setModal(True)
        self.resize(900, 600)
        
        layout = QVBoxLayout(self)
        
        # 模板选择
        template_group = QGroupBox("选择模板")
        template_layout = QHBoxLayout(template_group)
        
        template_layout.addWidget(QLabel("视频模板:"))
        self.template_combo = QComboBox()
        self.template_combo.currentTextChanged.connect(self.on_template_changed)
        template_layout.addWidget(self.template_combo)
        
        refresh_btn = QPushButton("刷新模板")
        refresh_btn.clicked.connect(self.load_templates)
        template_layout.addWidget(refresh_btn)
        
        template_layout.addStretch()
        
        layout.addWidget(template_group)
        
        # 视频批次
        batch_group = QGroupBox("视频批次")
        batch_layout = QVBoxLayout(batch_group)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        add_batch_btn = QPushButton("添加批次")
        add_batch_btn.clicked.connect(self.add_batch)
        toolbar_layout.addWidget(add_batch_btn)
        
        import_btn = QPushButton("从文件夹导入")
        import_btn.clicked.connect(self.import_from_folder)
        toolbar_layout.addWidget(import_btn)
        
        clear_btn = QPushButton("清空列表")
        clear_btn.clicked.connect(self.clear_batches)
        toolbar_layout.addWidget(clear_btn)
        
        toolbar_layout.addStretch()
        
        self.batch_count_label = QLabel("批次数量: 0")
        toolbar_layout.addWidget(self.batch_count_label)
        
        batch_layout.addLayout(toolbar_layout)
        
        # 视频列表滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        self.batch_container = QWidget()
        self.batch_layout = QVBoxLayout(self.batch_container)
        self.batch_layout.addStretch()
        
        scroll_area.setWidget(self.batch_container)
        batch_layout.addWidget(scroll_area)
        
        layout.addWidget(batch_group)
        
        # 输出设置
        output_group = QGroupBox("输出设置")
        output_layout = QGridLayout(output_group)
        
        output_layout.addWidget(QLabel("输出目录:"), 0, 0)
        self.output_dir_edit = QLineEdit(str(self.batch_processor.output_dir))
        output_layout.addWidget(self.output_dir_edit, 0, 1)
        
        browse_output_btn = QPushButton("浏览...")
        browse_output_btn.clicked.connect(self.browse_output_dir)
        output_layout.addWidget(browse_output_btn, 0, 2)
        
        layout.addWidget(output_group)
        
        # 按钮组
        button_layout = QHBoxLayout()
        
        validate_btn = QPushButton("验证批次")
        validate_btn.clicked.connect(self.validate_batches)
        button_layout.addWidget(validate_btn)
        
        button_layout.addStretch()
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        self.start_btn = QPushButton("开始批量处理")
        self.start_btn.clicked.connect(self.start_batch_processing)
        self.start_btn.setEnabled(False)
        button_layout.addWidget(self.start_btn)
        
        layout.addLayout(button_layout)
    
    def load_templates(self):
        """加载模板列表"""
        self.template_combo.clear()
        try:
            templates = self.template_manager.list_templates()
            
            if not templates:
                self.template_combo.addItem("暂无模板", None)
                return
            
            for template in templates:
                # 安全地获取模板信息，防止None值错误
                name = template.get('name', '未命名模板')
                total_duration = template.get('total_duration', 0.0)
                template_id = template.get('id')
                
                if template_id:  # 只添加有效的模板
                    self.template_combo.addItem(
                        f"{name} ({total_duration:.1f}s)",
                        template_id
                    )
            
            if self.template_combo.count() > 0:
                self.template_combo.setCurrentIndex(0)
                self.on_template_changed()
            
        except Exception as e:
            print(f"❌ 加载模板列表失败: {e}")
            self.template_combo.addItem("加载模板失败", None)
    
    def on_template_changed(self):
        """模板改变"""
        self.validate_batches()
    
    def add_batch(self):
        """添加批次"""
        batch_data = {
            'batch_name': f'客户{len(self.video_items) + 1}',
            'videos': {},
            'custom_settings': {
                'watermark_text': '高端理发店',
                'music_volume': 0.7
            }
        }
        
        item = BatchVideoItem(len(self.video_items), batch_data)
        item.item_changed.connect(self.on_batch_item_changed)
        item.item_removed.connect(self.remove_batch)
        
        self.video_items.append(item)
        
        # 插入到布局中（在stretch之前）
        self.batch_layout.insertWidget(self.batch_layout.count() - 1, item)
        
        self.update_batch_count()
        self.validate_batches()
    
    def remove_batch(self, index: int):
        """删除批次"""
        if 0 <= index < len(self.video_items):
            item = self.video_items.pop(index)
            item.setParent(None)
            
            # 更新后续项的索引
            for i in range(index, len(self.video_items)):
                self.video_items[i].index = i
                self.video_items[i].index_label.setText(f"{i + 1}.")
            
            self.update_batch_count()
            self.validate_batches()
    
    def on_batch_item_changed(self, index: int, data: Dict):
        """批次项改变"""
        self.validate_batches()
    
    def import_from_folder(self):
        """从文件夹导入"""
        folder = QFileDialog.getExistingDirectory(self, "选择包含视频文件的文件夹")
        if not folder:
            return
        
        folder_path = Path(folder)
        video_files = []
        
        # 扫描视频文件
        for ext in ['*.mp4', '*.avi', '*.mov', '*.mkv']:
            video_files.extend(folder_path.glob(ext))
        
        if not video_files:
            QMessageBox.information(self, "提示", "文件夹中没有找到视频文件")
            return
        
        # 按文件名分组（假设文件名包含客户信息）
        groups = {}
        for video_file in video_files:
            name = video_file.stem
            # 简单的分组逻辑：按下划线分割，取第一部分作为客户名
            client_name = name.split('_')[0] if '_' in name else name
            
            if client_name not in groups:
                groups[client_name] = []
            groups[client_name].append(str(video_file))
        
        # 为每个分组创建批次
        added_count = 0
        for client_name, files in groups.items():
            if len(files) >= 3:  # 至少需要3个视频文件
                batch_data = {
                    'batch_name': client_name,
                    'videos': {
                        'before': files[0],
                        'process': files[1] if len(files) > 1 else '',
                        'after': files[2] if len(files) > 2 else ''
                    },
                    'custom_settings': {
                        'watermark_text': f'{client_name} - 高端理发店',
                        'music_volume': 0.7
                    }
                }
                
                item = BatchVideoItem(len(self.video_items), batch_data)
                item.item_changed.connect(self.on_batch_item_changed)
                item.item_removed.connect(self.remove_batch)
                
                self.video_items.append(item)
                self.batch_layout.insertWidget(self.batch_layout.count() - 1, item)
                added_count += 1
        
        if added_count > 0:
            self.update_batch_count()
            self.validate_batches()
            QMessageBox.information(self, "导入完成", f"成功导入 {added_count} 个批次")
        else:
            QMessageBox.warning(self, "导入失败", "没有找到符合要求的视频文件组合")
    
    def clear_batches(self):
        """清空批次"""
        if not self.video_items:
            return
        
        reply = QMessageBox.question(
            self, "确认清空",
            "确定要清空所有批次吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            for item in self.video_items:
                item.setParent(None)
            self.video_items.clear()
            self.update_batch_count()
            self.validate_batches()
    
    def browse_output_dir(self):
        """浏览输出目录"""
        folder = QFileDialog.getExistingDirectory(
            self, "选择输出目录", self.output_dir_edit.text()
        )
        if folder:
            self.output_dir_edit.setText(folder)
            self.batch_processor.output_dir = Path(folder)
    
    def update_batch_count(self):
        """更新批次数量"""
        count = len(self.video_items)
        self.batch_count_label.setText(f"批次数量: {count}")
    
    def validate_batches(self):
        """验证批次"""
        template_id = self.template_combo.currentData()
        valid_count = 0
        
        if template_id and self.video_items:
            for item in self.video_items:
                if item.is_complete():
                    valid_count += 1
        
        self.start_btn.setEnabled(
            template_id is not None and 
            valid_count > 0 and 
            self.output_dir_edit.text().strip()
        )
        
        if self.video_items:
            invalid_count = len(self.video_items) - valid_count
            if invalid_count > 0:
                self.start_btn.setText(f"开始处理 ({valid_count}/{len(self.video_items)} 有效)")
            else:
                self.start_btn.setText(f"开始批量处理 ({valid_count} 个批次)")
        else:
            self.start_btn.setText("开始批量处理")
    
    def start_batch_processing(self):
        """开始批量处理"""
        template_id = self.template_combo.currentData()
        if not template_id:
            QMessageBox.warning(self, "警告", "请选择一个模版")
            return
        
        # 收集有效的批次数据
        valid_batches = []
        for item in self.video_items:
            if item.is_complete():
                valid_batches.append(item.get_video_data())
        
        if not valid_batches:
            QMessageBox.warning(self, "警告", "没有有效的批次数据")
            return
        
        # 确认处理
        reply = QMessageBox.question(
            self, "确认处理",
            f"确定要开始处理 {len(valid_batches)} 个批次吗？\n"
            f"输出目录: {self.output_dir_edit.text()}",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply != QMessageBox.Yes:
            return
        
        # 创建批量处理任务
        job_id = self.batch_processor.create_batch_job(template_id, valid_batches)
        
        # 开始处理
        success = self.batch_processor.start_batch_processing(job_id)
        
        if success:
            # 显示进度对话框
            progress_dialog = BatchProgressDialog(job_id, self.batch_processor, self)
            progress_dialog.exec()
            
            # 关闭当前对话框
            self.accept()
        else:
            QMessageBox.critical(self, "错误", "启动批量处理失败") 