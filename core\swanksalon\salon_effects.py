#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
理发店专属特效库
专门用于SWANKSALON的转场特效和视觉效果
"""

import cv2
import numpy as np
from typing import Dict, List, Any, Tuple, Optional, Callable
from pathlib import Path
import json
import math
from core.logger import get_logger


class SalonEffects:
    """理发店专属特效库"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = get_logger('salon_effects')
        
        # 特效参数
        self.effect_params = self.config.get('effect_params', {
            'transition_duration': 0.5,
            'particle_count': 100,
            'glow_intensity': 0.8,
            'sparkle_density': 0.3
        })
        
        # 注册所有特效
        self.effects = {
            'scissor_light': self.scissor_light_transition,
            'hair_particle': self.hair_particle_transition,
            'color_dissolve': self.color_dissolve_transition,
            'mirror_reflection': self.mirror_reflection_transition,
            'shampoo_bubbles': self.shampoo_bubbles_transition,
            'hair_strand': self.hair_strand_transition,
            'salon_sparkle': self.salon_sparkle_transition,
            'color_splash': self.color_splash_transition,
            'cutting_flash': self.cutting_flash_transition,
            'style_reveal': self.style_reveal_transition
        }
        
        self.logger.info(f"理发店特效库初始化完成，共{len(self.effects)}种特效")
    
    def apply_transition(self, frame1: np.ndarray, frame2: np.ndarray, 
                        effect_name: str, progress: float, **kwargs) -> np.ndarray:
        """
        应用转场特效
        
        Args:
            frame1: 第一帧
            frame2: 第二帧
            effect_name: 特效名称
            progress: 进度 (0.0-1.0)
            **kwargs: 额外参数
            
        Returns:
            处理后的帧
        """
        try:
            if effect_name not in self.effects:
                self.logger.warning(f"未知特效: {effect_name}，使用默认淡入淡出")
                return cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)
            
            effect_func = self.effects[effect_name]
            return effect_func(frame1, frame2, progress, **kwargs)
            
        except Exception as e:
            self.logger.error(f"应用特效失败 {effect_name}: {e}")
            return cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)
    
    def scissor_light_transition(self, frame1: np.ndarray, frame2: np.ndarray, 
                               progress: float, **kwargs) -> np.ndarray:
        """剪刀光效转场"""
        try:
            h, w = frame1.shape[:2]
            
            # 创建剪刀形状的光效路径
            center_x, center_y = w // 2, h // 2
            
            # 剪刀的两个刀片路径
            blade1_start = (center_x - int(w * 0.3), center_y - int(h * 0.2))
            blade1_end = (center_x + int(w * 0.3), center_y + int(h * 0.2))
            
            blade2_start = (center_x - int(w * 0.3), center_y + int(h * 0.2))
            blade2_end = (center_x + int(w * 0.3), center_y - int(h * 0.2))
            
            # 创建光效遮罩
            mask = np.zeros((h, w), dtype=np.uint8)
            
            # 根据进度绘制剪刀光效
            if progress < 0.5:
                # 剪刀闭合阶段
                angle = progress * 60  # 最大60度
                
                # 旋转刀片
                cos_a, sin_a = np.cos(np.radians(angle)), np.sin(np.radians(angle))
                
                # 绘制旋转的刀片
                thickness = max(2, int(w * 0.01))
                cv2.line(mask, blade1_start, blade1_end, 255, thickness)
                cv2.line(mask, blade2_start, blade2_end, 255, thickness)
            else:
                # 光效扩散阶段
                expand_progress = (progress - 0.5) * 2
                thickness = int(w * 0.02 * (1 + expand_progress * 2))
                
                cv2.line(mask, blade1_start, blade1_end, 255, thickness)
                cv2.line(mask, blade2_start, blade2_end, 255, thickness)
            
            # 创建光晕效果
            glow = np.zeros_like(frame1)
            glow_thickness = max(5, int(w * 0.03))
            
            cv2.line(glow, blade1_start, blade1_end, (0, 255, 255), glow_thickness)
            cv2.line(glow, blade2_start, blade2_end, (0, 255, 255), glow_thickness)
            
            # 高斯模糊产生光晕
            glow = cv2.GaussianBlur(glow, (21, 21), 0)
            
            # 应用转场
            mask_3d = cv2.cvtColor(mask, cv2.COLOR_GRAY2BGR) / 255.0
            result = frame1 * (1 - mask_3d * progress) + frame2 * (mask_3d * progress)
            
            # 添加光效
            glow_intensity = kwargs.get('glow_intensity', 0.8) * progress
            result = cv2.addWeighted(result.astype(np.uint8), 1.0, glow, glow_intensity, 0)
            
            return result.astype(np.uint8)
            
        except Exception as e:
            self.logger.error(f"剪刀光效转场失败: {e}")
            return cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)
    
    def hair_particle_transition(self, frame1: np.ndarray, frame2: np.ndarray, 
                               progress: float, **kwargs) -> np.ndarray:
        """发丝粒子消散转场"""
        try:
            h, w = frame1.shape[:2]
            result = frame1.copy()
            
            # 粒子参数
            particle_count = kwargs.get('particle_count', 150)
            max_particles = int(particle_count * progress)
            
            # 生成发丝状粒子
            for _ in range(max_particles):
                # 随机起始位置
                start_x = np.random.randint(0, w)
                start_y = np.random.randint(0, h)
                
                # 发丝长度和方向
                length = np.random.randint(10, 30)
                angle = np.random.uniform(0, 2 * np.pi)
                
                end_x = int(start_x + length * np.cos(angle))
                end_y = int(start_y + length * np.sin(angle))
                
                # 确保在画面内
                end_x = max(0, min(w-1, end_x))
                end_y = max(0, min(h-1, end_y))
                
                # 粒子颜色（从原图采样）
                if 0 <= start_y < h and 0 <= start_x < w:
                    color = frame1[start_y, start_x].tolist()
                    
                    # 绘制发丝状粒子
                    cv2.line(result, (start_x, start_y), (end_x, end_y), color, 1)
                    
                    # 添加一些随机点增加细节
                    for i in range(3):
                        px = int(start_x + i * (end_x - start_x) / 3)
                        py = int(start_y + i * (end_y - start_y) / 3)
                        if 0 <= px < w and 0 <= py < h:
                            cv2.circle(result, (px, py), 1, color, -1)
            
            # 混合两帧
            return cv2.addWeighted(result, 1-progress, frame2, progress, 0)
            
        except Exception as e:
            self.logger.error(f"发丝粒子转场失败: {e}")
            return cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)
    
    def color_dissolve_transition(self, frame1: np.ndarray, frame2: np.ndarray, 
                                progress: float, **kwargs) -> np.ndarray:
        """品牌色溶解转场"""
        try:
            h, w = frame1.shape[:2]
            
            # 获取主题颜色
            theme_colors = kwargs.get('theme_colors', ['#FF3366', '#33CCFF', '#66FF33'])
            
            # 创建彩色噪声
            noise = np.random.rand(h, w)
            color_overlay = np.zeros_like(frame1)
            
            # 为每个颜色区域分配不同的颜色
            for i, color_hex in enumerate(theme_colors):
                # 解析颜色
                color_hex = color_hex.lstrip('#')
                if len(color_hex) == 6:
                    r = int(color_hex[0:2], 16)
                    g = int(color_hex[2:4], 16)
                    b = int(color_hex[4:6], 16)
                    bgr_color = [b, g, r]
                else:
                    bgr_color = [255, 255, 255]
                
                # 创建颜色区域
                threshold_low = i / len(theme_colors)
                threshold_high = (i + 1) / len(theme_colors)
                color_mask = (noise >= threshold_low) & (noise < threshold_high)
                
                color_overlay[color_mask] = bgr_color
            
            # 创建溶解遮罩
            dissolve_threshold = progress
            dissolve_mask = (noise < dissolve_threshold).astype(np.float32)
            dissolve_mask = np.stack([dissolve_mask] * 3, axis=2)
            
            # 应用溶解效果
            result = frame1 * (1 - dissolve_mask) + frame2 * dissolve_mask
            
            # 添加颜色效果
            color_intensity = kwargs.get('color_intensity', 0.3) * progress
            result = result * (1 - color_intensity) + color_overlay * color_intensity
            
            return result.astype(np.uint8)
            
        except Exception as e:
            self.logger.error(f"色彩溶解转场失败: {e}")
            return cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)
    
    def mirror_reflection_transition(self, frame1: np.ndarray, frame2: np.ndarray, 
                                   progress: float, **kwargs) -> np.ndarray:
        """镜面反射转场"""
        try:
            h, w = frame1.shape[:2]
            
            # 创建镜面分割线
            split_pos = int(w * progress)
            
            result = np.zeros_like(frame1)
            
            # 左侧显示第一帧
            if split_pos > 0:
                result[:, :split_pos] = frame1[:, :split_pos]
            
            # 右侧显示第二帧的镜像
            if split_pos < w:
                right_part = frame2[:, split_pos:]
                # 水平翻转创建镜像效果
                mirrored = cv2.flip(right_part, 1)
                result[:, split_pos:] = mirrored
            
            # 添加镜面光效
            if split_pos > 5 and split_pos < w - 5:
                # 在分割线处添加高光
                cv2.line(result, (split_pos, 0), (split_pos, h), (255, 255, 255), 3)
                
                # 添加渐变光效
                for i in range(10):
                    alpha = 0.3 * (1 - i / 10)
                    if split_pos - i >= 0:
                        result[:, split_pos - i] = cv2.addWeighted(
                            result[:, split_pos - i], 1 - alpha,
                            np.full_like(result[:, split_pos - i], 255), alpha, 0
                        )
                    if split_pos + i < w:
                        result[:, split_pos + i] = cv2.addWeighted(
                            result[:, split_pos + i], 1 - alpha,
                            np.full_like(result[:, split_pos + i], 255), alpha, 0
                        )
            
            return result
            
        except Exception as e:
            self.logger.error(f"镜面反射转场失败: {e}")
            return cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)
    
    def shampoo_bubbles_transition(self, frame1: np.ndarray, frame2: np.ndarray, 
                                 progress: float, **kwargs) -> np.ndarray:
        """洗发泡泡转场"""
        try:
            h, w = frame1.shape[:2]
            result = cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)
            
            # 泡泡参数
            bubble_count = kwargs.get('bubble_count', 50)
            max_bubbles = int(bubble_count * progress)
            
            # 生成泡泡
            for _ in range(max_bubbles):
                # 随机位置和大小
                x = np.random.randint(0, w)
                y = np.random.randint(0, h)
                radius = np.random.randint(5, 25)
                
                # 泡泡透明度
                alpha = np.random.uniform(0.3, 0.8)
                
                # 创建泡泡遮罩
                bubble_mask = np.zeros((h, w), dtype=np.uint8)
                cv2.circle(bubble_mask, (x, y), radius, 255, -1)
                
                # 泡泡颜色（白色带透明）
                bubble_color = np.full_like(result, [255, 255, 255])
                
                # 应用泡泡
                mask_3d = cv2.cvtColor(bubble_mask, cv2.COLOR_GRAY2BGR) / 255.0
                result = result * (1 - mask_3d * alpha) + bubble_color * (mask_3d * alpha)
                
                # 添加泡泡边缘高光
                cv2.circle(result, (x, y), radius, (255, 255, 255), 2)
            
            return result.astype(np.uint8)
            
        except Exception as e:
            self.logger.error(f"洗发泡泡转场失败: {e}")
            return cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)
    
    def hair_strand_transition(self, frame1: np.ndarray, frame2: np.ndarray, 
                             progress: float, **kwargs) -> np.ndarray:
        """发丝飘动转场"""
        try:
            h, w = frame1.shape[:2]
            
            # 创建波浪形发丝路径
            result = frame1.copy()
            
            # 发丝数量
            strand_count = kwargs.get('strand_count', 20)
            
            for i in range(strand_count):
                # 发丝起始位置
                start_x = int(w * i / strand_count)
                
                # 创建波浪路径
                points = []
                for y in range(0, h, 5):
                    wave_offset = int(30 * np.sin(y * 0.02 + progress * 10) * progress)
                    x = start_x + wave_offset
                    x = max(0, min(w-1, x))
                    points.append((x, y))
                
                # 绘制发丝
                if len(points) > 1:
                    for j in range(len(points) - 1):
                        cv2.line(result, points[j], points[j+1], (100, 50, 25), 2)
            
            # 混合两帧
            return cv2.addWeighted(result, 1-progress, frame2, progress, 0)
            
        except Exception as e:
            self.logger.error(f"发丝飘动转场失败: {e}")
            return cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)
    
    def salon_sparkle_transition(self, frame1: np.ndarray, frame2: np.ndarray, 
                               progress: float, **kwargs) -> np.ndarray:
        """理发店闪光转场"""
        try:
            h, w = frame1.shape[:2]
            result = cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)
            
            # 闪光参数
            sparkle_density = kwargs.get('sparkle_density', 0.5)
            sparkle_count = int(100 * sparkle_density * progress)
            
            for _ in range(sparkle_count):
                x = np.random.randint(0, w)
                y = np.random.randint(0, h)
                
                # 星形闪光
                size = np.random.randint(3, 12)
                brightness = np.random.randint(200, 255)
                
                # 绘制十字星形
                cv2.line(result, (x-size, y), (x+size, y), (brightness, brightness, brightness), 2)
                cv2.line(result, (x, y-size), (x, y+size), (brightness, brightness, brightness), 2)
                cv2.line(result, (x-size//2, y-size//2), (x+size//2, y+size//2), (brightness, brightness, brightness), 1)
                cv2.line(result, (x-size//2, y+size//2), (x+size//2, y-size//2), (brightness, brightness, brightness), 1)
            
            return result
            
        except Exception as e:
            self.logger.error(f"理发店闪光转场失败: {e}")
            return cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)
    
    def color_splash_transition(self, frame1: np.ndarray, frame2: np.ndarray, 
                              progress: float, **kwargs) -> np.ndarray:
        """色彩飞溅转场"""
        try:
            h, w = frame1.shape[:2]
            
            # 创建飞溅中心
            center_x = kwargs.get('center_x', w // 2)
            center_y = kwargs.get('center_y', h // 2)
            
            # 飞溅半径
            max_radius = int(np.sqrt(w*w + h*h) / 2)
            current_radius = int(max_radius * progress)
            
            # 创建圆形遮罩
            mask = np.zeros((h, w), dtype=np.uint8)
            cv2.circle(mask, (center_x, center_y), current_radius, 255, -1)
            
            # 添加飞溅边缘效果
            splash_points = []
            for angle in range(0, 360, 10):
                rad = np.radians(angle)
                # 随机化边缘
                radius_var = current_radius + np.random.randint(-20, 20)
                x = int(center_x + radius_var * np.cos(rad))
                y = int(center_y + radius_var * np.sin(rad))
                
                if 0 <= x < w and 0 <= y < h:
                    splash_points.append((x, y))
            
            # 绘制飞溅边缘
            if len(splash_points) > 2:
                pts = np.array(splash_points, np.int32)
                cv2.fillPoly(mask, [pts], 255)
            
            # 应用转场
            mask_3d = cv2.cvtColor(mask, cv2.COLOR_GRAY2BGR) / 255.0
            result = frame1 * (1 - mask_3d) + frame2 * mask_3d
            
            return result.astype(np.uint8)
            
        except Exception as e:
            self.logger.error(f"色彩飞溅转场失败: {e}")
            return cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)
    
    def cutting_flash_transition(self, frame1: np.ndarray, frame2: np.ndarray, 
                               progress: float, **kwargs) -> np.ndarray:
        """剪切闪光转场"""
        try:
            if progress < 0.1:
                # 准备阶段
                return frame1
            elif progress < 0.3:
                # 闪光阶段
                flash_intensity = (progress - 0.1) / 0.2
                white_flash = np.full_like(frame1, 255)
                return cv2.addWeighted(frame1, 1-flash_intensity, white_flash, flash_intensity, 0)
            elif progress < 0.5:
                # 切换阶段
                return frame2
            else:
                # 淡入阶段
                fade_progress = (progress - 0.5) / 0.5
                return cv2.addWeighted(frame2, fade_progress, frame1, 1-fade_progress, 0)
                
        except Exception as e:
            self.logger.error(f"剪切闪光转场失败: {e}")
            return cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)
    
    def style_reveal_transition(self, frame1: np.ndarray, frame2: np.ndarray, 
                              progress: float, **kwargs) -> np.ndarray:
        """造型揭示转场"""
        try:
            h, w = frame1.shape[:2]
            
            # 从上到下的揭示效果
            reveal_height = int(h * progress)
            
            result = frame1.copy()
            
            if reveal_height > 0:
                # 上半部分显示新造型
                result[:reveal_height] = frame2[:reveal_height]
                
                # 添加分界线光效
                if reveal_height < h:
                    # 渐变边缘
                    for i in range(min(10, h - reveal_height)):
                        alpha = 0.8 * (1 - i / 10)
                        if reveal_height + i < h:
                            result[reveal_height + i] = cv2.addWeighted(
                                result[reveal_height + i], 1 - alpha,
                                np.full_like(result[reveal_height + i], 255), alpha, 0
                            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"造型揭示转场失败: {e}")
            return cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)
    
    def get_available_effects(self) -> List[str]:
        """获取可用特效列表"""
        return list(self.effects.keys())
    
    def get_effect_info(self, effect_name: str) -> Dict[str, Any]:
        """获取特效信息"""
        effect_descriptions = {
            'scissor_light': '剪刀光效 - 模拟剪刀闭合的光效转场',
            'hair_particle': '发丝粒子 - 发丝状粒子消散效果',
            'color_dissolve': '色彩溶解 - 品牌色彩溶解转场',
            'mirror_reflection': '镜面反射 - 镜子反射效果',
            'shampoo_bubbles': '洗发泡泡 - 泡泡飘浮效果',
            'hair_strand': '发丝飘动 - 发丝波浪飘动',
            'salon_sparkle': '理发店闪光 - 星形闪光效果',
            'color_splash': '色彩飞溅 - 圆形色彩扩散',
            'cutting_flash': '剪切闪光 - 快速闪光切换',
            'style_reveal': '造型揭示 - 从上到下揭示新造型'
        }
        
        return {
            'name': effect_name,
            'description': effect_descriptions.get(effect_name, '未知特效'),
            'available': effect_name in self.effects,
            'parameters': self._get_effect_parameters(effect_name)
        }
    
    def _get_effect_parameters(self, effect_name: str) -> Dict[str, Any]:
        """获取特效参数"""
        parameters = {
            'scissor_light': {'glow_intensity': 0.8},
            'hair_particle': {'particle_count': 150},
            'color_dissolve': {'theme_colors': ['#FF3366', '#33CCFF'], 'color_intensity': 0.3},
            'mirror_reflection': {},
            'shampoo_bubbles': {'bubble_count': 50},
            'hair_strand': {'strand_count': 20},
            'salon_sparkle': {'sparkle_density': 0.5},
            'color_splash': {'center_x': None, 'center_y': None},
            'cutting_flash': {},
            'style_reveal': {}
        }
        
        return parameters.get(effect_name, {})
