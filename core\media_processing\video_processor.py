#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频处理核心模块
集成 FFmpeg 和基础视频处理功能
"""

import os
import subprocess
import json
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import cv2
import numpy as np

from ..common.logger import get_logger
from ..common.exceptions import VideoProcessingError, FFmpegError, handle_exception

class VideoInfo:
    """视频信息类"""
    
    def __init__(self, file_path: str):
        self.file_path = Path(file_path)
        self.duration = 0.0
        self.width = 0
        self.height = 0
        self.fps = 0.0
        self.codec = ""
        self.format = ""
        self.bitrate = 0
        self.audio_codec = ""
        self.audio_channels = 0
        self.audio_sample_rate = 0
        
        self._load_info()

    @staticmethod
    def create_placeholder(file_path: str):
        """创建占位符VideoInfo"""
        placeholder = VideoInfo.__new__(VideoInfo)  # 创建实例但不调用__init__
        placeholder.file_path = Path(file_path)
        placeholder.duration = 5.0  # 默认5秒
        placeholder.width = 1920
        placeholder.height = 1080
        placeholder.fps = 30.0
        placeholder.codec = "placeholder"
        placeholder.format = "placeholder"
        placeholder.bitrate = 0
        placeholder.audio_codec = ""
        placeholder.audio_channels = 0
        placeholder.audio_sample_rate = 0
        return placeholder

    def _load_info(self):
        """加载视频信息"""
        try:
            # 检查是否是占位符文件
            if str(self.file_path).startswith('placeholder_'):
                print(f"Skipping FFprobe for placeholder: {self.file_path}")
                return

            # 检查文件是否存在
            if not self.file_path.exists():
                print(f"Video file not found: {self.file_path}")
                return

            # 使用 FFprobe 获取视频信息
            cmd = [
                'ffprobe',
                '-v', 'quiet',
                '-threads', '1',  # 强制单线程
                '-print_format', 'json',
                '-show_format',
                '-show_streams',
                str(self.file_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')
            if result.returncode != 0:
                raise Exception(f"FFprobe 执行失败: {result.stderr}")
            
            if not result.stdout:
                raise Exception("FFprobe 没有输出数据")
            
            try:
                data = json.loads(result.stdout)
            except (json.JSONDecodeError, ValueError) as e:
                raise Exception(f"解析FFprobe输出失败: {e}")
            
            # 解析格式信息
            if 'format' in data:
                format_info = data['format']
                self.duration = float(format_info.get('duration', 0))
                self.format = format_info.get('format_name', '')
                self.bitrate = int(format_info.get('bit_rate', 0))
            
            # 解析流信息
            if 'streams' in data:
                for stream in data['streams']:
                    if stream['codec_type'] == 'video':
                        self.width = int(stream.get('width', 0))
                        self.height = int(stream.get('height', 0))
                        self.codec = stream.get('codec_name', '')
                        
                        # 计算帧率
                        fps_str = stream.get('r_frame_rate', '0/1')
                        if '/' in fps_str:
                            num, den = fps_str.split('/')
                            if int(den) != 0:
                                self.fps = float(num) / float(den)
                    
                    elif stream['codec_type'] == 'audio':
                        self.audio_codec = stream.get('codec_name', '')
                        self.audio_channels = int(stream.get('channels', 0))
                        self.audio_sample_rate = int(stream.get('sample_rate', 0))
        
        except Exception as e:
            print(f"获取视频信息失败: {e}")
            # 尝试使用 OpenCV 作为备用方案
            self._load_info_opencv()
    
    def _load_info_opencv(self):
        """使用 OpenCV 获取基本视频信息"""
        try:
            cap = cv2.VideoCapture(str(self.file_path))
            if cap.isOpened():
                self.width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                self.height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                self.fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
                if self.fps > 0:
                    self.duration = frame_count / self.fps
                cap.release()
        except Exception as e:
            print(f"OpenCV 获取视频信息失败: {e}")

class VideoProcessor:
    """视频处理器"""

    def __init__(self, config):
        self.config = config
        self.ffmpeg_path = config.get('ffmpeg.path', 'ffmpeg')
        self.threads = config.get('ffmpeg.threads', -1)
        # 缓存提取的缩略图
        self._thumbnail_cache = {}
        # 初始化日志器
        self.logger = get_logger('video_processor')
    
    def check_ffmpeg(self) -> bool:
        """检查 FFmpeg 是否可用"""
        try:
            result = subprocess.run([self.ffmpeg_path, '-version'], 
                                  capture_output=True, text=True, encoding='utf-8', errors='ignore')
            return result.returncode == 0
        except:
            return False
    
    def get_video_info(self, file_path: str) -> VideoInfo:
        """获取视频信息"""
        # 检查是否是占位符文件
        if file_path.startswith('placeholder_'):
            # 为占位符创建默认的VideoInfo
            return VideoInfo.create_placeholder(file_path)

        return VideoInfo(file_path)
    
    def extract_frames(self, video_path: str, output_dir: str, 
                      start_time: float = 0, count: int = 10) -> List[str]:
        """提取视频帧"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        cmd = [
            self.ffmpeg_path,
            '-threads', '1',  # 强制单线程
            '-thread_type', 'slice',  # 使用slice线程类型
            '-i', video_path,
            '-ss', str(start_time),
            '-vframes', str(count),
            '-q:v', '2',
            '-f', 'image2',
            str(output_path / 'frame_%04d.jpg')
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')
            if result.returncode == 0:
                return [str(f) for f in output_path.glob('frame_*.jpg')]
            else:
                print(f"提取帧失败: {result.stderr}")
                return []
        except Exception as e:
            print(f"提取帧异常: {e}")
            return []
    
    def cut_video(self, input_path: str, output_path: str, 
                  start_time: float, end_time: float) -> bool:
        """剪切视频"""
        duration = end_time - start_time
        
        cmd = [
            self.ffmpeg_path,
            '-i', input_path,
            '-ss', str(start_time),
            '-t', str(duration),
            '-c', 'copy',  # 使用流复制，速度快
            '-avoid_negative_ts', 'make_zero',
            output_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')
            return result.returncode == 0
        except Exception as e:
            print(f"剪切视频异常: {e}")
            return False
    
    def merge_videos(self, input_paths: List[str], output_path: str) -> bool:
        """合并视频"""
        if len(input_paths) < 2:
            return False
        
        # 创建临时文件列表
        temp_list_file = self.config.get_temp_dir() / "merge_list.txt"
        
        try:
            with open(temp_list_file, 'w', encoding='utf-8') as f:
                for path in input_paths:
                    # 转换为绝对路径并转义
                    abs_path = Path(path).resolve()
                    f.write(f"file '{abs_path}'\n")
            
            cmd = [
                self.ffmpeg_path,
                '-f', 'concat',
                '-safe', '0',
                '-i', str(temp_list_file),
                '-c', 'copy',
                output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')
            return result.returncode == 0
            
        except Exception as e:
            print(f"合并视频异常: {e}")
            return False
        finally:
            # 清理临时文件
            if temp_list_file.exists():
                temp_list_file.unlink()
    
    def add_watermark(self, input_path: str, output_path: str, 
                     watermark_path: str, position: str = "bottom-right",
                     opacity: float = 0.5) -> bool:
        """添加水印"""
        # 根据位置设置覆盖位置
        position_map = {
            "top-left": "10:10",
            "top-right": "W-w-10:10",
            "bottom-left": "10:H-h-10",
            "bottom-right": "W-w-10:H-h-10",
            "center": "(W-w)/2:(H-h)/2"
        }
        
        overlay_pos = position_map.get(position, "W-w-10:H-h-10")
        
        cmd = [
            self.ffmpeg_path,
            '-i', input_path,
            '-i', watermark_path,
            '-filter_complex', 
            f'[1:v]format=yuva420p,colorchannelmixer=aa={opacity}[logo];[0:v][logo]overlay={overlay_pos}',
            '-codec:a', 'copy',
            output_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
        except Exception as e:
            print(f"添加水印异常: {e}")
            return False
    
    def resize_video(self, input_path: str, output_path: str, 
                    width: int, height: int, maintain_aspect: bool = True) -> bool:
        """调整视频尺寸"""
        if maintain_aspect:
            scale_filter = f"scale={width}:{height}:force_original_aspect_ratio=decrease"
        else:
            scale_filter = f"scale={width}:{height}"
        
        cmd = [
            self.ffmpeg_path,
            '-threads', '1',  # 强制单线程
            '-thread_type', 'slice',  # 使用slice线程类型
            '-i', input_path,
            '-vf', scale_filter,
            '-c:a', 'copy',
            output_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')
            return result.returncode == 0
        except Exception as e:
            print(f"调整视频尺寸异常: {e}")
            return False
    
    def get_thumbnail_frames(self, video_path: str, timestamps: List[float], width: int = 160, height: int = 90):
        """获取指定时间点的缩略图帧

        Args:
            video_path: 视频文件路径
            timestamps: 时间点列表（秒）
            width: 缩略图宽度
            height: 缩略图高度

        Returns:
            List[QPixmap]: 缩略图列表
        """
        from PySide6.QtGui import QPixmap, QImage
        from PySide6.QtCore import Qt

        # 检查是否是占位符文件
        if video_path.startswith('placeholder_'):
            print(f"Skipping thumbnail generation for placeholder: {video_path}")
            return []

        # 检查文件是否存在
        if not os.path.exists(video_path):
            print(f"Video file not found for thumbnail: {video_path}")
            return []

        cache_key = f"{video_path}_{width}x{height}"
        if cache_key not in self._thumbnail_cache:
            self._thumbnail_cache[cache_key] = {}
        
        thumbnails = []
        
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return [QPixmap(width, height) for _ in timestamps]  # 返回空白图像
            
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            for timestamp in timestamps:
                # 检查缓存
                if timestamp in self._thumbnail_cache[cache_key]:
                    thumbnails.append(self._thumbnail_cache[cache_key][timestamp])
                    continue
                
                # 跳转到指定时间
                frame_number = int(timestamp * fps)
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
                
                ret, frame = cap.read()
                if ret:
                    # 调整尺寸
                    frame_resized = cv2.resize(frame, (width, height))
                    # 转换颜色格式 BGR -> RGB
                    frame_rgb = cv2.cvtColor(frame_resized, cv2.COLOR_BGR2RGB)
                    
                    # 转换为QImage
                    height_img, width_img, channel = frame_rgb.shape
                    bytes_per_line = 3 * width_img
                    qimage = QImage(frame_rgb.data, width_img, height_img, bytes_per_line, QImage.Format.Format_RGB888)
                    
                    # 转换为QPixmap
                    pixmap = QPixmap.fromImage(qimage)
                    
                    # 缓存
                    self._thumbnail_cache[cache_key][timestamp] = pixmap
                    thumbnails.append(pixmap)
                else:
                    # 如果无法读取帧，创建空白图像
                    pixmap = QPixmap(width, height)
                    pixmap.fill(Qt.GlobalColor.black)
                    thumbnails.append(pixmap)
            
            cap.release()
            
        except Exception as e:
            print(f"获取缩略图失败: {e}")
            # 返回空白图像
            thumbnails = [QPixmap(width, height) for _ in timestamps]
            for pixmap in thumbnails:
                pixmap.fill(Qt.GlobalColor.black)
        
        return thumbnails
    
    def clear_thumbnail_cache(self):
        """清除缩略图缓存"""
        self._thumbnail_cache.clear()
    
    def apply_template_effects_to_video(self, input_path: str, output_path: str,
                                      start_time: float, end_time: float,
                                      filters: List) -> bool:
        """应用模板效果到视频片段"""
        try:
            duration = end_time - start_time
            
            # 构建滤镜链
            filter_chain = []
            
            for filter_item in filters:
                filter_type = filter_item.filter_type
                intensity = filter_item.intensity
                properties = filter_item.properties or {}
                
                if filter_type == 'color_grade':
                    # 色彩分级
                    brightness = properties.get('brightness', 0) * intensity / 100
                    contrast = properties.get('contrast', 0) * intensity / 100
                    saturation = properties.get('saturation', 0) * intensity / 100
                    filter_chain.append(f"eq=brightness={brightness}:contrast={contrast}:saturation={saturation}")
                
                elif filter_type == 'sharpen':
                    # 锐化
                    amount = properties.get('amount', 0.5) * intensity
                    filter_chain.append(f"unsharp=5:5:{amount}:5:5:0.0")
                
                elif filter_type == 'blur':
                    # 模糊
                    radius = properties.get('radius', 2) * intensity
                    filter_chain.append(f"boxblur={radius}:{radius}")
                
                elif filter_type == 'vintage':
                    # 复古效果
                    filter_chain.append(f"colorchannelmixer=.393:.769:.189:0:.349:.686:.168:0:.272:.534:.131:0:0:0:0:1")
            
            # 构建命令
            cmd = [
                self.ffmpeg_path,
                '-threads', '1',  # 强制单线程
                '-thread_type', 'slice',  # 使用slice线程类型
                '-i', input_path,
                '-ss', str(start_time),
                '-t', str(duration)
            ]
            
            if filter_chain:
                cmd.extend(['-vf', ','.join(filter_chain)])
            
            cmd.extend(['-c:a', 'copy', output_path])
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
            
        except Exception as e:
            print(f"应用模板效果失败: {e}")
            # 如果应用效果失败，使用普通剪切
            return self.cut_video(input_path, output_path, start_time, end_time)
    
    def merge_videos_with_transitions(self, input_paths: List[str], output_path: str,
                                    segments: List[dict]) -> bool:
        """合并视频并应用转场效果"""
        try:
            if len(input_paths) < 2:
                return False
            
            # 检查是否有转场需要应用
            has_transitions = any(seg.get('template_transitions') for seg in segments)
            
            if not has_transitions:
                # 没有转场，使用普通合并
                return self.merge_videos(input_paths, output_path)
            
            # 构建复杂的滤镜图
            filter_complex = []
            input_labels = []
            
            for i, path in enumerate(input_paths):
                input_labels.append(f"[{i}:v]")
            
            # 简化处理：使用交叉淡化转场
            if len(input_paths) == 2:
                filter_complex.append(f"{input_labels[0]}{input_labels[1]}xfade=transition=fade:duration=1:offset=0[v]")
                cmd = [
                    self.ffmpeg_path,
                    '-i', input_paths[0],
                    '-i', input_paths[1],
                    '-filter_complex', ';'.join(filter_complex),
                    '-map', '[v]',
                    '-c:a', 'copy',
                    output_path
                ]
            else:
                # 多个视频的情况，先用普通合并
                return self.merge_videos(input_paths, output_path)
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
            
        except Exception as e:
            print(f"转场合并失败: {e}")
            # 回退到普通合并
            return self.merge_videos(input_paths, output_path)
    
    def mix_audio_with_effects(self, video_path: str, audio_segments: List[dict], 
                             output_path: str) -> bool:
        """混合音频并应用效果"""
        try:
            if not audio_segments:
                return False
            
            # 构建音频混合命令
            inputs = ['-i', video_path]
            filter_complex = ['[0:a]']
            
            for i, audio_seg in enumerate(audio_segments):
                audio_path = audio_seg['path']
                if not os.path.exists(audio_path):
                    continue
                
                inputs.extend(['-i', audio_path])
                
                # 构建音频处理链
                audio_filters = []
                
                # 音量调整
                volume = audio_seg.get('volume', 1.0)
                if volume != 1.0:
                    audio_filters.append(f"volume={volume}")
                
                # 淡入淡出
                fade_in = audio_seg.get('fade_in', 0.0)
                fade_out = audio_seg.get('fade_out', 0.0)
                
                if fade_in > 0:
                    audio_filters.append(f"afade=t=in:ss=0:d={fade_in}")
                
                if fade_out > 0:
                    audio_filters.append(f"afade=t=out:st={audio_seg.get('start_time', 0)}:d={fade_out}")
                
                if audio_filters:
                    filter_complex.append(f"[{i+1}:a]{','.join(audio_filters)}[a{i+1}]")
                    filter_complex.append(f"[a{i+1}]")
                else:
                    filter_complex.append(f"[{i+1}:a]")
            
            # 混合所有音频
            if len(audio_segments) > 0:
                filter_complex.append(f"amix=inputs={len(audio_segments)+1}:duration=longest[aout]")
            
            cmd = [
                self.ffmpeg_path
            ] + inputs + [
                '-filter_complex', ''.join(filter_complex),
                '-map', '0:v',
                '-map', '[aout]',
                '-c:v', 'copy',
                '-c:a', 'aac',
                output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
            
        except Exception as e:
            print(f"音频混合失败: {e}")
            return False
    
    def apply_filter(self, input_path: str, output_path: str, filter_type: str, **kwargs) -> bool:
        """应用视频滤镜"""
        try:
            filter_map = {
                'brightness': lambda v: f"eq=brightness={v/100}",
                'contrast': lambda v: f"eq=contrast={v/100}",
                'saturation': lambda v: f"eq=saturation={v/100}",
                'blur': lambda v: f"boxblur={v}:{v}",
                'sharpen': lambda v: f"unsharp=5:5:{v}:5:5:0.0",
                'vintage': lambda v: "colorchannelmixer=.393:.769:.189:0:.349:.686:.168:0:.272:.534:.131:0:0:0:0:1",
                'grayscale': lambda v: "colorchannelmixer=.299:.587:.114:0:.299:.587:.114:0:.299:.587:.114:0:0:0:0:1",
                'sepia': lambda v: "colorchannelmixer=.393:.769:.189:0:.349:.686:.168:0:.272:.534:.131:0:0:0:0:1"
            }
            
            if filter_type not in filter_map:
                return False
            
            value = kwargs.get('value', 1.0)
            filter_str = filter_map[filter_type](value)
            
            cmd = [
                self.ffmpeg_path,
                '-i', input_path,
                '-vf', filter_str,
                '-c:a', 'copy',
                output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
            
        except Exception as e:
            print(f"应用滤镜失败: {e}")
            return False
    
    def apply_transition(self, input1: str, input2: str, output_path: str, 
                        transition_type: str = 'fade', duration: float = 1.0) -> bool:
        """应用转场效果"""
        try:
            transition_map = {
                'fade': 'fade',
                'dissolve': 'dissolve',
                'wipe': 'wipeleft',
                'slide': 'slideleft'
            }
            
            transition = transition_map.get(transition_type, 'fade')
            
            cmd = [
                self.ffmpeg_path,
                '-i', input1,
                '-i', input2,
                '-filter_complex',
                f'[0:v][1:v]xfade=transition={transition}:duration={duration}:offset=0[v]',
                '-map', '[v]',
                '-c:a', 'copy',
                output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
            
        except Exception as e:
            print(f"应用转场失败: {e}")
            return False
    
    def create_slideshow(self, image_paths: List[str], output_path: str,
                        duration_per_image: float = 3.0, transition_duration: float = 0.5) -> bool:
        """创建幻灯片视频"""
        try:
            if not image_paths:
                return False
            
            # 创建临时文件列表
            temp_list_file = self.config.get_temp_dir() / "slideshow_list.txt"
            
            with open(temp_list_file, 'w', encoding='utf-8') as f:
                for image_path in image_paths:
                    f.write(f"file '{Path(image_path).resolve()}'\n")
                    f.write(f"duration {duration_per_image}\n")
            
            cmd = [
                self.ffmpeg_path,
                '-f', 'concat',
                '-safe', '0',
                '-i', str(temp_list_file),
                '-vf', f'scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2',
                '-pix_fmt', 'yuv420p',
                '-r', '30',
                output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            # 清理临时文件
            if temp_list_file.exists():
                temp_list_file.unlink()
            
            return result.returncode == 0
            
        except Exception as e:
            print(f"创建幻灯片失败: {e}")
            return False
    
    def auto_detect_highlights(self, video_path: str, min_segment_duration: float = 2.0) -> List[dict]:
        """自动识别视频中的高亮片段
        
        Args:
            video_path: 视频文件路径
            min_segment_duration: 最小片段时长（秒）
            
        Returns:
            List[dict]: 高亮片段列表，每个包含 start_time, end_time, confidence
        """
        try:
            import cv2
            import numpy as np
            from collections import deque
            
            print(f"🔍 开始分析视频高亮片段: {video_path}")
            
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                print(f"❌ 无法打开视频文件: {video_path}")
                return []
            
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = total_frames / fps
            
            # 分析参数
            sample_interval = max(1, int(fps / 2))  # 每0.5秒采样一次
            window_size = int(fps * 3)  # 3秒窗口
            
            # 存储分析数据
            motion_scores = []
            brightness_scores = []
            contrast_scores = []
            timestamps = []
            
            frame_count = 0
            prev_frame = None
            
            print(f"📊 视频信息: {duration:.1f}秒, {fps:.1f}fps, {total_frames}帧")
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 只处理采样帧
                if frame_count % sample_interval == 0:
                    timestamp = frame_count / fps
                    timestamps.append(timestamp)
                    
                    # 转换为灰度图
                    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                    
                    # 1. 运动检测（光流分析）
                    motion_score = 0.0
                    if prev_frame is not None:
                        # 计算帧差
                        diff = cv2.absdiff(gray, prev_frame)
                        motion_score = np.mean(diff) / 255.0
                    
                    motion_scores.append(motion_score)
                    
                    # 2. 亮度分析
                    brightness = np.mean(gray) / 255.0
                    brightness_scores.append(brightness)
                    
                    # 3. 对比度分析
                    contrast = np.std(gray) / 255.0
                    contrast_scores.append(contrast)
                    
                    prev_frame = gray.copy()
                
                frame_count += 1
                
                # 进度显示
                if frame_count % (total_frames // 10) == 0:
                    progress = (frame_count / total_frames) * 100
                    print(f"📈 分析进度: {progress:.1f}%")
            
            cap.release()
            
            if len(motion_scores) < 10:
                print("❌ 视频太短，无法进行有效分析")
                return []
            
            # 数据平滑处理
            motion_scores = self._smooth_signal(motion_scores, window_size=5)
            brightness_scores = self._smooth_signal(brightness_scores, window_size=5)
            contrast_scores = self._smooth_signal(contrast_scores, window_size=5)
            
            # 计算综合评分
            highlight_scores = []
            for i in range(len(motion_scores)):
                # 归一化各项指标
                motion_norm = motion_scores[i]
                brightness_norm = brightness_scores[i]
                contrast_norm = contrast_scores[i]
                
                # 计算综合评分（可调整权重）
                score = (
                    motion_norm * 0.4 +      # 运动权重40%
                    contrast_norm * 0.4 +    # 对比度权重40%
                    brightness_norm * 0.2    # 亮度权重20%
                )
                
                highlight_scores.append(score)
            
            # 识别高亮片段
            highlights = self._find_highlight_segments(
                timestamps, highlight_scores, min_segment_duration, fps
            )
            
            print(f"✅ 识别到 {len(highlights)} 个高亮片段")
            for i, segment in enumerate(highlights):
                print(f"   片段{i+1}: {segment['start_time']:.1f}s - {segment['end_time']:.1f}s (置信度: {segment['confidence']:.2f})")
            
            return highlights
            
        except Exception as e:
            print(f"❌ 高亮片段识别失败: {e}")
            return []
    
    def _smooth_signal(self, signal: List[float], window_size: int = 5) -> List[float]:
        """信号平滑处理"""
        if len(signal) < window_size:
            return signal
        
        smoothed = []
        for i in range(len(signal)):
            start = max(0, i - window_size // 2)
            end = min(len(signal), i + window_size // 2 + 1)
            smoothed.append(np.mean(signal[start:end]))
        
        return smoothed
    
    def _find_highlight_segments(self, timestamps: List[float], scores: List[float], 
                               min_duration: float, fps: float) -> List[dict]:
        """从评分中找出高亮片段"""
        if len(scores) == 0:
            return []
        
        # 计算阈值（使用75%分位数）
        threshold = np.percentile(scores, 75)
        
        highlights = []
        in_highlight = False
        start_time = 0
        start_idx = 0
        
        for i, (timestamp, score) in enumerate(zip(timestamps, scores)):
            if score >= threshold and not in_highlight:
                # 开始新的高亮片段
                in_highlight = True
                start_time = timestamp
                start_idx = i
                
            elif score < threshold and in_highlight:
                # 结束当前高亮片段
                in_highlight = False
                duration = timestamp - start_time
                
                if duration >= min_duration:
                    # 计算片段的平均置信度
                    segment_scores = scores[start_idx:i]
                    confidence = np.mean(segment_scores)
                    
                    highlights.append({
                        'start_time': start_time,
                        'end_time': timestamp,
                        'duration': duration,
                        'confidence': confidence
                    })
        
        # 处理最后一个片段
        if in_highlight:
            duration = timestamps[-1] - start_time
            if duration >= min_duration:
                segment_scores = scores[start_idx:]
                confidence = np.mean(segment_scores)
                
                highlights.append({
                    'start_time': start_time,
                    'end_time': timestamps[-1],
                    'duration': duration,
                    'confidence': confidence
                })
        
        # 按置信度排序
        highlights.sort(key=lambda x: x['confidence'], reverse=True)
        
        return highlights
    
    def auto_trim_to_highlights(self, video_path: str, output_dir: str, 
                              max_segments: int = 5) -> List[str]:
        """自动剪辑视频到高亮片段
        
        Args:
            video_path: 输入视频路径
            output_dir: 输出目录
            max_segments: 最大片段数量
            
        Returns:
            List[str]: 输出文件路径列表
        """
        try:
            import os
            
            # 检测高亮片段
            highlights = self.auto_detect_highlights(video_path)
            
            if not highlights:
                print("❌ 未检测到高亮片段")
                return []
            
            # 限制片段数量
            highlights = highlights[:max_segments]
            
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            output_files = []
            base_name = os.path.splitext(os.path.basename(video_path))[0]
            
            for i, segment in enumerate(highlights):
                output_file = os.path.join(
                    output_dir, 
                    f"{base_name}_highlight_{i+1:02d}.mp4"
                )
                
                # 剪辑片段
                success = self.cut_video(
                    video_path, output_file,
                    segment['start_time'], segment['end_time']
                )
                
                if success:
                    output_files.append(output_file)
                    print(f"✅ 已生成高亮片段: {output_file}")
                else:
                    print(f"❌ 生成高亮片段失败: {output_file}")
            
            return output_files
            
        except Exception as e:
            print(f"❌ 自动剪辑失败: {e}")
            return []

    @handle_exception
    def compose_audio_video_timeline(self, video_segments: List[Dict], audio_segments: List[Dict],
                                   output_path: str) -> bool:
        """合成时间轴上的音视频"""
        try:
            self.logger.info("Starting audio-video composition...")

            if not video_segments:
                raise VideoProcessingError("No video segments provided")

            # 创建临时目录
            import tempfile
            temp_dir = tempfile.mkdtemp()

            try:
                # 第一步：合并视频片段
                video_list_file = os.path.join(temp_dir, "video_list.txt")
                merged_video = os.path.join(temp_dir, "merged_video.mp4")

                # 创建视频列表文件
                with open(video_list_file, 'w', encoding='utf-8') as f:
                    for segment in video_segments:
                        file_path = segment.get('file_path', '')
                        if os.path.exists(file_path):
                            # 转义文件路径中的特殊字符
                            escaped_path = file_path.replace("'", "'\"'\"'")
                            f.write(f"file '{escaped_path}'\n")

                # 合并视频
                cmd = [
                    self.ffmpeg_path,
                    '-hide_banner',
                    '-loglevel', 'error',
                    '-f', 'concat',
                    '-safe', '0',
                    '-i', video_list_file,
                    '-c', 'copy',
                    '-y', merged_video
                ]

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
                if result.returncode != 0:
                    self.logger.warning(f"Video concat failed, trying re-encode: {result.stderr}")

                    # 如果直接拷贝失败，尝试重新编码
                    cmd = [
                        self.ffmpeg_path,
                        '-hide_banner',
                        '-loglevel', 'error',
                        '-f', 'concat',
                        '-safe', '0',
                        '-i', video_list_file,
                        '-c:v', 'libx264',
                        '-preset', 'fast',
                        '-crf', '23',
                        '-y', merged_video
                    ]

                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
                    if result.returncode != 0:
                        raise VideoProcessingError(f"Video merge failed: {result.stderr}")

                # 第二步：处理音频
                if audio_segments:
                    audio_file = audio_segments[0].get('file_path', '')
                    if os.path.exists(audio_file):
                        # 合成音视频
                        volume = audio_segments[0].get('volume', 0.7)

                        cmd = [
                            self.ffmpeg_path,
                            '-hide_banner',
                            '-loglevel', 'error',
                            '-i', merged_video,
                            '-i', audio_file,
                            '-c:v', 'copy',
                            '-c:a', 'aac',
                            '-b:a', '128k',
                            '-filter:a', f'volume={volume}',
                            '-map', '0:v:0',
                            '-map', '1:a:0',
                            '-shortest',  # 以较短的流为准
                            '-avoid_negative_ts', 'make_zero',
                            '-y', output_path
                        ]

                        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
                        if result.returncode != 0:
                            raise VideoProcessingError(f"Audio-video composition failed: {result.stderr}")
                    else:
                        # 没有音频，直接复制视频
                        import shutil
                        shutil.copy2(merged_video, output_path)
                else:
                    # 没有音频，直接复制视频
                    import shutil
                    shutil.copy2(merged_video, output_path)

                self.logger.info(f"Audio-video composition completed: {output_path}")
                return True

            finally:
                # 清理临时文件
                import shutil
                shutil.rmtree(temp_dir, ignore_errors=True)

        except Exception as e:
            self.logger.error(f"Audio-video composition failed: {str(e)}")
            return False