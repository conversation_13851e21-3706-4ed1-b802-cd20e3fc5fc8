#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能模板处理器 - 增强功能与理发店模板的深度融合
"""

import os
import json
from pathlib import Path
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass
import tempfile

from ..common.logger import get_logger
from ..common.exceptions import TemplateError, VideoProcessingError, handle_exception
from ..common.enhanced_features import EnhancedFeatureManager
from ..templates.template_manager import HairSalonTemplate, TemplateSegment
from .auto_sync import VideoSegment, SyncPoint


@dataclass
class SmartTemplateResult:
    """智能模板处理结果"""
    success: bool
    template_id: str
    processed_segments: List[Dict[str, Any]]
    sync_points: List[SyncPoint]
    audio_analysis: Dict[str, Any]
    text_content: List[Dict[str, Any]]
    recommendations: List[str]
    processing_time: float
    error_message: Optional[str] = None


@dataclass
class AutomationSettings:
    """自动化设置"""
    enable_auto_sync: bool = True
    enable_smart_trimming: bool = True
    enable_text_extraction: bool = True
    enable_audio_analysis: bool = True
    sync_tolerance: float = 0.3
    min_segment_confidence: float = 0.6
    auto_adjust_timing: bool = True
    preserve_original_duration: bool = False


class SmartTemplateProcessor:
    """智能模板处理器 - 融合增强功能的自动化模板处理"""
    
    def __init__(self, config, template_manager, video_processor):
        self.config = config
        self.template_manager = template_manager
        self.video_processor = video_processor
        self.enhanced_manager = EnhancedFeatureManager(config)
        self.logger = get_logger('smart_template_processor')
        
        # 默认自动化设置
        self.automation_settings = AutomationSettings()
        
        self.logger.info("Smart template processor initialized")
    
    @handle_exception
    def process_template_with_intelligence(self, template: HairSalonTemplate, 
                                         video_files: Dict[str, str],
                                         automation_settings: Optional[AutomationSettings] = None) -> SmartTemplateResult:
        """使用智能增强功能处理模板"""
        import time
        start_time = time.time()
        
        if automation_settings:
            self.automation_settings = automation_settings
        
        try:
            self.logger.info(f"Starting intelligent template processing: {template.name}")
            
            result = SmartTemplateResult(
                success=False,
                template_id=template.id,
                processed_segments=[],
                sync_points=[],
                audio_analysis={},
                text_content=[],
                recommendations=[],
                processing_time=0.0
            )
            
            # 1. 音频分析和节拍检测
            if self.automation_settings.enable_audio_analysis:
                audio_analysis = self._analyze_template_audio(template)
                result.audio_analysis = audio_analysis
                self.logger.info(f"Audio analysis completed: {audio_analysis.get('tempo', 0):.1f} BPM")
            
            # 2. 智能视频片段分析
            video_analysis = self._analyze_video_segments(video_files)
            
            # 3. 文字内容提取
            if self.automation_settings.enable_text_extraction:
                text_content = self._extract_text_from_videos(video_files)
                result.text_content = text_content
                self.logger.info(f"Text extraction completed: {len(text_content)} text regions found")
            
            # 4. 智能片段匹配和优化
            optimized_segments = self._optimize_segments_with_ai(
                template, video_files, result.audio_analysis, video_analysis
            )
            result.processed_segments = optimized_segments
            
            # 5. 自动踩点同步
            if self.automation_settings.enable_auto_sync and result.audio_analysis:
                sync_points = self._generate_smart_sync_points(
                    template, optimized_segments, result.audio_analysis
                )
                result.sync_points = sync_points
                self.logger.info(f"Auto-sync completed: {len(sync_points)} sync points generated")
            
            # 6. 生成智能建议
            result.recommendations = self._generate_smart_recommendations(
                template, result.audio_analysis, video_analysis, result.text_content
            )
            
            result.success = True
            result.processing_time = time.time() - start_time
            
            self.logger.info(f"Intelligent template processing completed in {result.processing_time:.2f}s")
            return result
            
        except Exception as e:
            result.error_message = str(e)
            result.processing_time = time.time() - start_time
            self.logger.error(f"Intelligent template processing failed: {str(e)}")
            return result

    def _analyze_template_audio(self, template: HairSalonTemplate) -> Dict[str, Any]:
        """分析模板音频"""
        try:
            if not template.music or not template.music[0].audio_path:
                return {}

            audio_path = template.music[0].audio_path
            if not os.path.exists(audio_path):
                self.logger.warning(f"Audio file not found: {audio_path}")
                return {}

            # 使用增强功能分析音频
            features = self.enhanced_manager.audio_processor.analyze_audio_features(audio_path)
            music_structure = self.enhanced_manager.auto_sync_processor.analyze_music_structure(audio_path)

            return {
                'features': features,
                'music_structure': music_structure,
                'audio_path': audio_path
            }

        except Exception as e:
            self.logger.error(f"Audio analysis failed: {str(e)}")
            return {}

    def _analyze_video_segments(self, video_files: Dict[str, str]) -> Dict[str, Any]:
        """分析视频片段特征"""
        analysis_results = {}

        for segment_type, video_path in video_files.items():
            if not os.path.exists(video_path):
                continue

            try:
                # 使用增强功能分析视频
                video_analysis = self.enhanced_manager.analyze_video_content(video_path)

                # 提取关键信息
                analysis_results[segment_type] = {
                    'path': video_path,
                    'audio_features': video_analysis.get('audio_features', {}),
                    'text_content': video_analysis.get('text_content', {}),
                    'recommendations': video_analysis.get('recommendations', []),
                    'duration': video_analysis.get('audio_features', {}).get('duration', 0)
                }

                self.logger.debug(f"Video analysis completed for {segment_type}: {video_path}")

            except Exception as e:
                self.logger.error(f"Video analysis failed for {segment_type}: {str(e)}")
                analysis_results[segment_type] = {
                    'path': video_path,
                    'error': str(e)
                }

        return analysis_results

    def _extract_text_from_videos(self, video_files: Dict[str, str]) -> List[Dict[str, Any]]:
        """从视频中提取文字内容"""
        all_text_content = []

        for segment_type, video_path in video_files.items():
            if not os.path.exists(video_path):
                continue

            try:
                # 提取文字
                text_frames = self.enhanced_manager.ocr_processor.extract_text_from_video(
                    video_path, sample_interval=3.0, confidence_threshold=0.6
                )

                if text_frames:
                    merged_texts = self.enhanced_manager.ocr_processor.merge_similar_text_regions(text_frames)

                    for text_info in merged_texts:
                        text_info['segment_type'] = segment_type
                        text_info['video_path'] = video_path
                        all_text_content.append(text_info)

                self.logger.debug(f"Text extraction completed for {segment_type}: {len(text_frames)} frames")

            except Exception as e:
                self.logger.error(f"Text extraction failed for {segment_type}: {str(e)}")

        return all_text_content

    def _optimize_segments_with_ai(self, template: HairSalonTemplate,
                                  video_files: Dict[str, str],
                                  audio_analysis: Dict[str, Any],
                                  video_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """使用AI优化片段设置"""
        optimized_segments = []

        # 获取音频节拍信息
        tempo = audio_analysis.get('features', {}).get('tempo', 120)
        beat_interval = 60.0 / tempo

        for segment in template.segments:
            segment_type = segment.segment_type

            if segment_type not in video_files:
                continue

            video_path = video_files[segment_type]
            video_info = video_analysis.get(segment_type, {})

            # 智能时长调整
            optimal_duration = self._calculate_optimal_segment_duration(
                segment, video_info, audio_analysis
            )

            # 智能起始时间调整
            optimal_start_time = self._calculate_optimal_start_time(
                segment, audio_analysis, beat_interval
            )

            optimized_segment = {
                'segment_type': segment_type,
                'name': segment.name,
                'video_path': video_path,
                'original_duration': segment.duration,
                'optimal_duration': optimal_duration,
                'original_position': segment.position,
                'optimal_position': optimal_start_time,
                'confidence': self._calculate_optimization_confidence(segment, video_info, audio_analysis),
                'applied_optimizations': []
            }

            # 记录应用的优化
            if abs(optimal_duration - segment.duration) > 0.5:
                optimized_segment['applied_optimizations'].append('duration_adjustment')

            if abs(optimal_start_time - segment.position) > 0.2:
                optimized_segment['applied_optimizations'].append('timing_sync')

            optimized_segments.append(optimized_segment)

            self.logger.debug(f"Segment optimized: {segment_type} - duration: {segment.duration:.1f}s -> {optimal_duration:.1f}s")

        return optimized_segments

    def _calculate_optimal_segment_duration(self, segment: TemplateSegment,
                                          video_info: Dict[str, Any],
                                          audio_analysis: Dict[str, Any]) -> float:
        """计算最优片段时长"""
        original_duration = segment.duration

        # 基于音乐节拍调整
        tempo = audio_analysis.get('features', {}).get('tempo', 120)
        beat_interval = 60.0 / tempo

        # 根据片段类型设置目标节拍数
        if segment.segment_type == 'intro':
            target_beats = 4  # 1小节
        elif segment.segment_type == 'outro':
            target_beats = 4  # 1小节
        elif segment.segment_type in ['before', 'after']:
            target_beats = 8  # 2小节
        elif segment.segment_type == 'process':
            target_beats = 16  # 4小节
        else:
            target_beats = 8  # 默认2小节

        optimal_duration = target_beats * beat_interval

        # 考虑视频实际时长
        video_duration = video_info.get('duration', 0)
        if video_duration > 0:
            # 不超过视频实际时长的90%
            max_duration = video_duration * 0.9
            optimal_duration = min(optimal_duration, max_duration)

        # 保持在合理范围内
        min_duration = 1.0
        max_duration = 30.0
        optimal_duration = max(min_duration, min(max_duration, optimal_duration))

        return optimal_duration

    def _calculate_optimal_start_time(self, segment: TemplateSegment,
                                    audio_analysis: Dict[str, Any],
                                    beat_interval: float) -> float:
        """计算最优开始时间（对齐到节拍）"""
        original_position = segment.position

        # 找到最接近的节拍点
        beat_position = round(original_position / beat_interval) * beat_interval

        return beat_position

    def _calculate_optimization_confidence(self, segment: TemplateSegment,
                                         video_info: Dict[str, Any],
                                         audio_analysis: Dict[str, Any]) -> float:
        """计算优化置信度"""
        confidence = 0.5  # 基础置信度

        # 有音频分析数据增加置信度
        if audio_analysis.get('features'):
            confidence += 0.2

        # 有视频分析数据增加置信度
        if video_info and not video_info.get('error'):
            confidence += 0.2

        # 节拍明显的音乐增加置信度
        tempo = audio_analysis.get('features', {}).get('tempo', 0)
        if 80 <= tempo <= 160:  # 合理的节拍范围
            confidence += 0.1

        return min(1.0, confidence)

    def _generate_smart_sync_points(self, template: HairSalonTemplate,
                                   optimized_segments: List[Dict[str, Any]],
                                   audio_analysis: Dict[str, Any]) -> List[SyncPoint]:
        """生成智能同步点"""
        try:
            if not audio_analysis.get('music_structure'):
                return []

            music_structure = audio_analysis['music_structure']
            beat_points = music_structure.get('beat_points', [])

            if not beat_points:
                return []

            # 转换为VideoSegment对象
            video_segments = []
            for seg in optimized_segments:
                video_segment = VideoSegment(
                    id=seg['segment_type'],
                    start_time=seg['optimal_position'],
                    duration=seg['optimal_duration'],
                    end_time=seg['optimal_position'] + seg['optimal_duration'],
                    file_path=seg['video_path'],
                    segment_type=seg['segment_type']
                )
                video_segments.append(video_segment)

            # 生成同步点
            sync_points = self.enhanced_manager.auto_sync_processor.generate_sync_points(
                music_structure, video_segments
            )

            return sync_points

        except Exception as e:
            self.logger.error(f"Smart sync point generation failed: {str(e)}")
            return []

    def _generate_smart_recommendations(self, template: HairSalonTemplate,
                                       audio_analysis: Dict[str, Any],
                                       video_analysis: Dict[str, Any],
                                       text_content: List[Dict[str, Any]]) -> List[str]:
        """生成智能建议"""
        recommendations = []

        # 音频相关建议
        if audio_analysis.get('features'):
            features = audio_analysis['features']
            tempo = features.get('tempo', 0)
            energy = features.get('energy', 0)

            if tempo < 90:
                recommendations.append("🎵 音乐节奏较慢，建议使用较长的视频片段展示细节")
            elif tempo > 140:
                recommendations.append("🎵 音乐节奏较快，建议使用快速剪切增强动感")

            if energy < 0.3:
                recommendations.append("🔊 音频能量较低，建议增加音量或选择更有活力的音乐")
            elif energy > 0.7:
                recommendations.append("⚡ 音频能量很高，适合制作动感十足的视频")

        # 视频相关建议
        total_video_duration = sum(
            info.get('duration', 0) for info in video_analysis.values()
            if not info.get('error')
        )
        template_duration = template.total_duration

        if total_video_duration > template_duration * 1.5:
            recommendations.append("✂️ 视频素材较多，建议精选最佳片段或延长模板时长")
        elif total_video_duration < template_duration * 0.7:
            recommendations.append("📹 视频素材不足，建议补充更多内容或缩短模板时长")

        # 文字内容建议
        if text_content:
            unique_texts = len(set(item['text'] for item in text_content))
            if unique_texts > 5:
                recommendations.append("📝 检测到丰富的文字内容，建议生成字幕文件")

            # 检查是否有价格信息
            price_texts = [item for item in text_content if any(char.isdigit() for char in item['text']) and '￥' in item['text']]
            if price_texts:
                recommendations.append("💰 检测到价格信息，建议在合适位置突出显示")

        # 模板结构建议
        if len(template.segments) < 3:
            recommendations.append("🎬 模板片段较少，建议增加更多片段丰富内容")

        return recommendations

    @handle_exception
    def apply_smart_template(self, template: HairSalonTemplate,
                           video_files: Dict[str, str],
                           output_path: str,
                           automation_settings: Optional[AutomationSettings] = None) -> bool:
        """应用智能模板处理"""
        try:
            self.logger.info(f"Applying smart template: {template.name}")

            # 1. 智能分析和优化
            smart_result = self.process_template_with_intelligence(
                template, video_files, automation_settings
            )

            if not smart_result.success:
                raise TemplateError(f"Smart template processing failed: {smart_result.error_message}")

            # 2. 应用优化后的设置到模板
            optimized_template = self._create_optimized_template(template, smart_result)

            # 3. 使用传统模板系统处理
            result = self.template_manager.apply_template_to_videos(optimized_template, video_files)

            if not result['success']:
                raise TemplateError(f"Template application failed: {', '.join(result['errors'])}")

            # 4. 生成最终视频
            success = self._generate_final_video(result, output_path, smart_result)

            if success:
                self.logger.info(f"Smart template applied successfully: {output_path}")

                # 保存处理报告
                self._save_processing_report(smart_result, output_path)

            return success

        except Exception as e:
            self.logger.error(f"Smart template application failed: {str(e)}")
            return False

    def _create_optimized_template(self, original_template: HairSalonTemplate,
                                  smart_result: SmartTemplateResult) -> HairSalonTemplate:
        """创建优化后的模板"""
        # 深拷贝原模板
        import copy
        optimized_template = copy.deepcopy(original_template)

        # 应用优化
        for optimized_seg in smart_result.processed_segments:
            segment_type = optimized_seg['segment_type']

            # 找到对应的模板片段
            for template_seg in optimized_template.segments:
                if template_seg.segment_type == segment_type:
                    if self.automation_settings.auto_adjust_timing:
                        template_seg.duration = optimized_seg['optimal_duration']
                        template_seg.position = optimized_seg['optimal_position']
                    break

        return optimized_template

    def _generate_final_video(self, template_result: Dict[str, Any],
                            output_path: str,
                            smart_result: SmartTemplateResult) -> bool:
        """生成最终视频"""
        try:
            # 这里可以集成更多智能处理
            # 目前使用现有的视频处理流程

            # 收集处理后的视频片段
            processed_segments = []
            for segment_info in template_result['segments']:
                if segment_info['applied'] and segment_info['video_path']:
                    processed_segments.append(segment_info['video_path'])

            if not processed_segments:
                return False

            # 合并视频
            return self.video_processor.merge_videos(processed_segments, output_path)

        except Exception as e:
            self.logger.error(f"Final video generation failed: {str(e)}")
            return False

    def _save_processing_report(self, smart_result: SmartTemplateResult, output_path: str):
        """保存处理报告"""
        try:
            report_path = Path(output_path).with_suffix('.json')

            report = {
                'template_id': smart_result.template_id,
                'processing_time': smart_result.processing_time,
                'audio_analysis': smart_result.audio_analysis,
                'processed_segments': smart_result.processed_segments,
                'sync_points': [
                    {
                        'timestamp': sp.timestamp,
                        'beat_timestamp': sp.beat_timestamp,
                        'confidence': sp.confidence,
                        'sync_type': sp.sync_type
                    } for sp in smart_result.sync_points
                ],
                'text_content': smart_result.text_content,
                'recommendations': smart_result.recommendations
            }

            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)

            self.logger.info(f"Processing report saved: {report_path}")

        except Exception as e:
            self.logger.error(f"Failed to save processing report: {str(e)}")
