"""
项目管理器 - 负责项目的保存、加载和管理
"""

import json
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import shutil

class ProjectManager:
    def __init__(self):
        self.current_project_path = None
        self.project_data = {}
        self.projects_dir = Path("projects")
        self.config_dir = Path("config")
        self.last_project_file = self.config_dir / "last_project.json"
        
        # 确保目录存在
        self.projects_dir.mkdir(exist_ok=True)
        self.config_dir.mkdir(exist_ok=True)
        
    def create_new_project(self, project_name: str, project_path: str = None) -> Dict[str, Any]:
        """创建新项目"""
        if not project_path:
            # 🔧 修改：确保所有项目都保存到projects文件夹
            project_path = self.projects_dir / f"{project_name}.json"
        else:
            project_path = Path(project_path)
            # 如果指定了路径但不在projects目录下，则移动到projects目录
            if not str(project_path).startswith(str(self.projects_dir)):
                project_path = self.projects_dir / project_path.name
            
        # 创建项目数据结构
        project_data = {
            "project_info": {
                "name": project_name,
                "created_time": datetime.now().isoformat(),
                "modified_time": datetime.now().isoformat(),
                "version": "1.0"
            },
            "timeline": {
                "total_duration": 30.0,
                "tracks": [],
                "global_settings": {
                    "fps": 30,
                    "resolution": "1920x1080",
                    "sample_rate": 44100
                }
            },
            "templates": [],
            "media_files": [],
            "settings": {
                "last_export_path": "",
                "auto_save": True,
                "auto_save_interval": 300  # 5分钟
            }
        }
        
        self.project_data = project_data
        self.current_project_path = project_path
        
        # 保存项目
        self.save_project()
        
        # 记录为最后打开的项目
        self.save_last_project_info()
        
        return project_data
    
    def save_project(self, project_path: str = None) -> bool:
        """保存当前项目"""
        try:
            if not project_path:
                project_path = self.current_project_path
                
            if not project_path:
                raise ValueError("没有指定项目路径")
                
            project_path = Path(project_path)
            
            # 更新修改时间
            if "project_info" in self.project_data:
                self.project_data["project_info"]["modified_time"] = datetime.now().isoformat()
            
            # 保存到JSON文件
            with open(project_path, 'w', encoding='utf-8') as f:
                json.dump(self.project_data, f, ensure_ascii=False, indent=2)
                
            self.current_project_path = project_path
            print(f"✅ 项目已保存: {project_path}")
            return True
            
        except Exception as e:
            print(f"❌ 保存项目失败: {e}")
            return False
    
    def load_project(self, project_path: str) -> Optional[Dict[str, Any]]:
        """加载项目"""
        try:
            project_path = Path(project_path)
            
            if not project_path.exists():
                print(f"❌ 项目文件不存在: {project_path}")
                return None
                
            with open(project_path, 'r', encoding='utf-8') as f:
                project_data = json.load(f)
                
            self.project_data = project_data
            self.current_project_path = project_path
            
            # 记录为最后打开的项目
            self.save_last_project_info()
            
            print(f"✅ 项目已加载: {project_path}")
            return project_data
            
        except Exception as e:
            print(f"❌ 加载项目失败: {e}")
            return None
    
    def save_timeline_data(self, tracks: List[Dict], total_duration: float, global_settings: Dict = None):
        """保存时间轴数据"""
        if "timeline" not in self.project_data:
            self.project_data["timeline"] = {}
            
        self.project_data["timeline"]["tracks"] = self.serialize_tracks(tracks)
        self.project_data["timeline"]["total_duration"] = total_duration
        
        if global_settings:
            self.project_data["timeline"]["global_settings"].update(global_settings)
            
        print(f"💾 时间轴数据已更新: {len(tracks)} 个轨道, 总时长 {total_duration}s")
    
    def serialize_tracks(self, tracks: List[Dict]) -> List[Dict]:
        """序列化轨道数据"""
        serialized_tracks = []
        
        for track in tracks:
            track_data = {
                "name": track.get("name", ""),
                "type": track.get("type", "video"),
                "enabled": track.get("enabled", True),
                "volume": track.get("volume", 1.0),
                "media_files": []
            }
            
            # 序列化媒体文件
            for media in track.get("media_files", []):
                media_data = {
                    "file_path": media.get("file_path", ""),
                    "name": media.get("name", ""),
                    "start_time": media.get("start_time", 0.0),
                    "duration": media.get("duration", 0.0),
                    "is_placeholder": media.get("is_placeholder", False),
                    "template_segment": media.get("template_segment", False),
                    "segment_type": media.get("segment_type", "normal"),
                    "volume": media.get("volume", 1.0),
                    "effects": media.get("effects", [])
                }
                
                # 如果是占位符，保存占位符文本
                if media.get("is_placeholder"):
                    media_data["placeholder_text"] = media.get("placeholder_text", "")
                    
                track_data["media_files"].append(media_data)
                
            serialized_tracks.append(track_data)
            
        return serialized_tracks
    
    def save_templates_data(self, templates: List[Dict]):
        """保存模板数据"""
        self.project_data["templates"] = []
        
        for template in templates:
            template_data = {
                "id": template.get("id", ""),
                "name": template.get("name", ""),
                "description": template.get("description", ""),
                "icon": template.get("icon", ""),
                "image_path": template.get("image_path", ""),
                "settings": template.get("settings", {}),
                "created_time": template.get("created_time", datetime.now().isoformat())
            }
            self.project_data["templates"].append(template_data)
            
        print(f"📋 模板数据已更新: {len(templates)} 个模板")
    
    def save_media_files_data(self, media_files: List[str]):
        """保存媒体文件列表"""
        self.project_data["media_files"] = []
        
        for file_path in media_files:
            if os.path.exists(file_path):
                file_info = {
                    "path": file_path,
                    "name": os.path.basename(file_path),
                    "size": os.path.getsize(file_path),
                    "modified_time": datetime.fromtimestamp(os.path.getmtime(file_path)).isoformat()
                }
                self.project_data["media_files"].append(file_info)
                
        print(f"🎬 媒体文件数据已更新: {len(self.project_data['media_files'])} 个文件")
    
    def save_last_project_info(self):
        """保存最后打开的项目信息"""
        try:
            if self.current_project_path:
                last_project_info = {
                    "project_path": str(self.current_project_path),
                    "project_name": self.project_data.get("project_info", {}).get("name", ""),
                    "last_opened": datetime.now().isoformat()
                }
                
                with open(self.last_project_file, 'w', encoding='utf-8') as f:
                    json.dump(last_project_info, f, ensure_ascii=False, indent=2)
                    
                print(f"📝 最后项目信息已保存")
                
        except Exception as e:
            print(f"❌ 保存最后项目信息失败: {e}")
    
    def get_last_project_info(self) -> Optional[Dict[str, Any]]:
        """获取最后打开的项目信息"""
        try:
            if self.last_project_file.exists():
                with open(self.last_project_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return None
        except Exception as e:
            print(f"❌ 读取最后项目信息失败: {e}")
            return None
    
    def auto_load_last_project(self) -> Optional[Dict[str, Any]]:
        """自动加载最后的项目"""
        last_info = self.get_last_project_info()
        if last_info and "project_path" in last_info:
            project_path = last_info["project_path"]
            if os.path.exists(project_path):
                print(f"🔄 自动加载最后的项目: {last_info.get('project_name', '')}")
                return self.load_project(project_path)
            else:
                print(f"⚠️ 最后的项目文件不存在: {project_path}")
        return None
    
    def export_project(self, export_path: str, include_media: bool = False) -> bool:
        """导出项目"""
        try:
            export_path = Path(export_path)
            
            if include_media:
                # 创建项目文件夹
                project_dir = export_path.parent / export_path.stem
                project_dir.mkdir(exist_ok=True)
                
                # 复制媒体文件
                media_dir = project_dir / "media"
                media_dir.mkdir(exist_ok=True)
                
                # 更新项目数据中的媒体路径
                updated_project_data = self.project_data.copy()
                
                # 复制并更新媒体文件路径
                for media_info in updated_project_data.get("media_files", []):
                    original_path = media_info["path"]
                    if os.path.exists(original_path):
                        filename = os.path.basename(original_path)
                        new_path = media_dir / filename
                        shutil.copy2(original_path, new_path)
                        media_info["path"] = str(new_path.relative_to(project_dir))
                
                # 保存项目文件
                project_file = project_dir / f"{export_path.stem}.json"
                with open(project_file, 'w', encoding='utf-8') as f:
                    json.dump(updated_project_data, f, ensure_ascii=False, indent=2)
                    
                print(f"✅ 项目已导出（包含媒体）: {project_dir}")
            else:
                # 只导出项目文件
                with open(export_path, 'w', encoding='utf-8') as f:
                    json.dump(self.project_data, f, ensure_ascii=False, indent=2)
                    
                print(f"✅ 项目已导出: {export_path}")
                
            return True
            
        except Exception as e:
            print(f"❌ 导出项目失败: {e}")
            return False
    
    def get_recent_projects(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的项目列表"""
        recent_projects = []
        
        try:
            # 扫描项目目录
            for project_file in self.projects_dir.glob("*.json"):
                try:
                    with open(project_file, 'r', encoding='utf-8') as f:
                        project_data = json.load(f)
                        
                    project_info = project_data.get("project_info", {})
                    recent_projects.append({
                        "path": str(project_file),
                        "name": project_info.get("name", project_file.stem),
                        "modified_time": project_info.get("modified_time", ""),
                        "created_time": project_info.get("created_time", "")
                    })
                    
                except Exception as e:
                    print(f"⚠️ 读取项目文件失败: {project_file}, {e}")
                    continue
            
            # 按修改时间排序
            recent_projects.sort(key=lambda x: x["modified_time"], reverse=True)
            return recent_projects[:limit]
            
        except Exception as e:
            print(f"❌ 获取最近项目失败: {e}")
            return []
    
    def get_current_project_data(self) -> Dict[str, Any]:
        """获取当前项目数据"""
        return self.project_data.copy()
    
    def is_project_modified(self) -> bool:
        """检查项目是否已修改"""
        # 这里可以实现更复杂的修改检测逻辑
        return True  # 简化实现，总是返回True
