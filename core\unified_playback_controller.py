#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一播放控制器 - 整合视频、音频播放的统一接口
"""

from PySide6.QtCore import QObject, Signal, QTimer, QThread
from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput
import numpy as np
from typing import Dict, List, Optional, Tuple
from enum import Enum

from .unified_project_manager import UnifiedProjectManager, PlaybackState, TrackType
from .unified_video_renderer import UnifiedVideoRenderer, VideoEffect, RenderRequest

class PlaybackTrigger(Enum):
    """播放触发方式"""
    USER_PLAY_BUTTON = "user_play_button"      # 用户点击播放按钮
    TIMELINE_CLICK = "timeline_click"          # 点击时间轴
    PLAYHEAD_DRAG = "playhead_drag"           # 拖拽播放头
    KEYBOARD_SPACE = "keyboard_space"         # 空格键
    EXTERNAL_API = "external_api"             # 外部API调用

class UnifiedPlaybackController(QObject):
    """
    统一播放控制器
    - 统一处理所有播放触发方式
    - 同步视频和音频播放
    - 管理播放状态和位置
    """
    
    # 信号定义
    frame_rendered = Signal(str, np.ndarray)  # video_path, frame
    audio_position_changed = Signal(float)    # position
    playback_error = Signal(str)              # error_message
    
    def __init__(self, project_manager: UnifiedProjectManager):
        super().__init__()
        
        self.project_manager = project_manager
        
        # 视频渲染器
        self.video_renderer = UnifiedVideoRenderer()
        self.video_renderer.frame_ready.connect(self.frame_rendered)
        self.video_renderer.position_changed.connect(self._on_video_position_changed)
        
        # 音频播放器（可以有多个，对应多个音频轨道）
        self.audio_players: Dict[str, QMediaPlayer] = {}
        self.audio_outputs: Dict[str, QAudioOutput] = {}
        
        # 播放控制
        self.playback_timer = QTimer()
        self.playback_timer.timeout.connect(self._on_playback_tick)
        
        # 连接项目管理器信号
        self.project_manager.playback_state_changed.connect(self._on_playback_state_changed)
        self.project_manager.playhead_position_changed.connect(self._on_playhead_position_changed)
        
        print("✅ 统一播放控制器初始化完成")
    
    # ==================== 统一播放接口 ====================
    
    def play(self, trigger: PlaybackTrigger = PlaybackTrigger.USER_PLAY_BUTTON):
        """
        统一播放方法 - 处理所有播放触发方式
        """
        try:
            current_state = self.project_manager.get_playback_state()
            current_position = self.project_manager.get_playhead_position()
            
            print(f"▶️ 播放触发: {trigger.value}, 当前位置: {current_position:.2f}s")
            
            if current_state == PlaybackState.PLAYING:
                # 已在播放，根据触发方式决定行为
                if trigger in [PlaybackTrigger.USER_PLAY_BUTTON, PlaybackTrigger.KEYBOARD_SPACE]:
                    # 暂停播放
                    self.pause()
                elif trigger in [PlaybackTrigger.TIMELINE_CLICK, PlaybackTrigger.PLAYHEAD_DRAG]:
                    # 跳转到新位置继续播放
                    self._start_playback_from_position(current_position)
                return
            
            elif current_state == PlaybackState.PAUSED:
                # 从暂停状态恢复播放
                self._resume_playback()
                
            elif current_state == PlaybackState.STOPPED:
                # 从停止状态开始播放
                self._start_playback_from_position(current_position)
            
        except Exception as e:
            print(f"❌ 播放失败: {e}")
            self.playback_error.emit(str(e))
    
    def pause(self):
        """暂停播放"""
        try:
            self.project_manager.set_playback_state(PlaybackState.PAUSED)
            self._pause_all_media()
            print("⏸️ 播放已暂停")
            
        except Exception as e:
            print(f"❌ 暂停失败: {e}")
            self.playback_error.emit(str(e))
    
    def stop(self):
        """停止播放"""
        try:
            self.project_manager.set_playback_state(PlaybackState.STOPPED)
            self._stop_all_media()
            self.project_manager.set_playhead_position(0.0)
            print("⏹️ 播放已停止")
            
        except Exception as e:
            print(f"❌ 停止失败: {e}")
            self.playback_error.emit(str(e))
    
    def seek(self, position: float, trigger: PlaybackTrigger = PlaybackTrigger.TIMELINE_CLICK):
        """
        统一跳转方法 - 处理所有跳转触发方式
        """
        try:
            current_state = self.project_manager.get_playback_state()
            
            # 更新播放头位置
            self.project_manager.set_playhead_position(position)
            
            print(f"🎯 跳转触发: {trigger.value}, 目标位置: {position:.2f}s")
            
            if current_state == PlaybackState.PLAYING:
                # 播放中跳转：同步所有媒体到新位置
                self._seek_all_media(position)
                
            elif current_state in [PlaybackState.PAUSED, PlaybackState.STOPPED]:
                # 暂停/停止状态跳转：渲染预览帧
                self._render_preview_at_position(position)
            
        except Exception as e:
            print(f"❌ 跳转失败: {e}")
            self.playback_error.emit(str(e))
    
    # ==================== 内部播放控制 ====================
    
    def _start_playback_from_position(self, position: float):
        """从指定位置开始播放"""
        # 获取当前位置的所有媒体项
        media_items = self.project_manager.get_media_items_at_position(position)
        
        # 启动视频播放
        self._start_video_playback(media_items, position)
        
        # 启动音频播放
        self._start_audio_playback(media_items, position)
        
        # 设置播放状态
        self.project_manager.set_playback_state(PlaybackState.PLAYING)
        
        # 启动播放定时器
        fps = self.project_manager.frame_rate
        interval = int(1000 / fps)  # 毫秒
        self.playback_timer.start(interval)
    
    def _resume_playback(self):
        """恢复播放"""
        current_position = self.project_manager.get_playhead_position()
        self._start_playback_from_position(current_position)
    
    def _pause_all_media(self):
        """暂停所有媒体"""
        # 暂停视频渲染
        for video_path in self.video_renderer._playing_videos:
            self.video_renderer.stop_playback(video_path)
        
        # 暂停音频播放
        for player in self.audio_players.values():
            if player.playbackState() == QMediaPlayer.PlaybackState.PlayingState:
                player.pause()
        
        # 停止播放定时器
        self.playback_timer.stop()
    
    def _stop_all_media(self):
        """停止所有媒体"""
        # 停止视频渲染
        for video_path in list(self.video_renderer._playing_videos.keys()):
            self.video_renderer.stop_playback(video_path)
        
        # 停止音频播放
        for player in self.audio_players.values():
            player.stop()
        
        # 停止播放定时器
        self.playback_timer.stop()
    
    def _seek_all_media(self, position: float):
        """跳转所有媒体到指定位置"""
        media_items = self.project_manager.get_media_items_at_position(position)
        
        # 跳转视频
        for track, item in media_items:
            if track.track_type == TrackType.VIDEO:
                # 计算在源文件中的相对位置
                relative_pos = position - item.timeline_start + item.source_start
                effects = self._get_item_effects(item)
                
                self.video_renderer.seek_to_position(
                    item.file_path, relative_pos, effects, 
                    self.project_manager.preview_window.quality
                )
        
        # 跳转音频
        for track, item in media_items:
            if track.track_type == TrackType.AUDIO:
                player = self._get_audio_player(item.file_path)
                if player:
                    relative_pos = position - item.timeline_start + item.source_start
                    player.setPosition(int(relative_pos * 1000))  # 毫秒
    
    def _render_preview_at_position(self, position: float):
        """渲染指定位置的预览帧"""
        media_items = self.project_manager.get_media_items_at_position(position)
        
        for track, item in media_items:
            if track.track_type == TrackType.VIDEO:
                relative_pos = position - item.timeline_start + item.source_start
                effects = self._get_item_effects(item)
                
                self.video_renderer.seek_to_position(
                    item.file_path, relative_pos, effects,
                    self.project_manager.preview_window.quality
                )
                break  # 只渲染最上层的视频
    
    def _start_video_playback(self, media_items: List, position: float):
        """启动视频播放"""
        for track, item in media_items:
            if track.track_type == TrackType.VIDEO and track.enabled and item.enabled:
                relative_pos = position - item.timeline_start + item.source_start
                effects = self._get_item_effects(item)
                
                self.video_renderer.start_playback(
                    item.file_path, effects, relative_pos,
                    self.project_manager.preview_window.quality
                )
    
    def _start_audio_playback(self, media_items: List, position: float):
        """启动音频播放"""
        for track, item in media_items:
            if track.track_type == TrackType.AUDIO and track.enabled and item.enabled:
                player = self._get_audio_player(item.file_path)
                if player:
                    relative_pos = position - item.timeline_start + item.source_start
                    player.setPosition(int(relative_pos * 1000))  # 毫秒
                    player.play()
    
    def _get_audio_player(self, file_path: str) -> Optional[QMediaPlayer]:
        """获取或创建音频播放器"""
        if file_path not in self.audio_players:
            player = QMediaPlayer()
            output = QAudioOutput()
            player.setAudioOutput(output)
            player.setSource(file_path)
            
            self.audio_players[file_path] = player
            self.audio_outputs[file_path] = output
        
        return self.audio_players[file_path]
    
    def _get_item_effects(self, item) -> List[VideoEffect]:
        """获取媒体项的效果列表"""
        effects = []
        for effect in item.effects:
            if effect.enabled:
                effects.append(VideoEffect(effect.effect_type, effect.value, True))
        return effects
    
    # ==================== 事件处理 ====================
    
    def _on_playback_tick(self):
        """播放定时器回调"""
        current_position = self.project_manager.get_playhead_position()
        playback_speed = self.project_manager.get_playback_speed()
        
        # 计算下一帧位置
        frame_duration = 1.0 / self.project_manager.frame_rate
        next_position = current_position + (frame_duration * playback_speed)
        
        # 检查是否到达项目结尾
        project_duration = self.project_manager.get_project_duration()
        if next_position >= project_duration:
            self.stop()
            return
        
        # 更新播放头位置
        self.project_manager.set_playhead_position(next_position)
    
    def _on_playback_state_changed(self, state: PlaybackState):
        """播放状态变化处理"""
        print(f"🎵 播放状态变化: {state.value}")
    
    def _on_playhead_position_changed(self, position: float):
        """播放头位置变化处理"""
        # 如果不在播放状态，渲染预览帧
        if self.project_manager.get_playback_state() != PlaybackState.PLAYING:
            self._render_preview_at_position(position)
    
    def _on_video_position_changed(self, video_path: str, position: float):
        """视频位置变化处理"""
        # 可以用于同步检查
        pass
    
    # ==================== 资源清理 ====================
    
    def cleanup(self):
        """清理资源"""
        self._stop_all_media()
        
        # 清理音频播放器
        for player in self.audio_players.values():
            player.stop()
        self.audio_players.clear()
        self.audio_outputs.clear()
        
        # 清理视频渲染器
        self.video_renderer.cleanup()
        
        print("🧹 统一播放控制器资源已清理")
