#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SWANKSALON 专用批量处理器
专门用于批量处理多个视频文件的高效处理系统
"""

import os
import time
import threading
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue
import json

from core.logger import get_logger
from core.swanksalon_engine import SwankSalonEngine


@dataclass
class SwankSalonTask:
    """SWANKSALON处理任务"""
    id: str
    video_path: str
    output_path: str
    config: Dict[str, Any]
    priority: int = 0
    status: str = "pending"  # pending, processing, completed, failed
    progress: float = 0.0
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    error_message: Optional[str] = None


@dataclass
class BatchStats:
    """批量处理统计"""
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    processing_tasks: int = 0
    pending_tasks: int = 0
    total_processing_time: float = 0.0
    average_processing_time: float = 0.0


class SwankSalonBatchProcessor:
    """SWANKSALON专用批量处理器"""
    
    def __init__(self, max_workers: int = 2):
        self.logger = get_logger('swanksalon_batch')
        self.max_workers = max_workers
        
        # 任务管理
        self.tasks: Dict[str, SwankSalonTask] = {}
        self.task_queue = Queue()
        self.stats = BatchStats()
        
        # 线程管理
        self.executor: Optional[ThreadPoolExecutor] = None
        self.is_processing = False
        self._lock = threading.Lock()
        
        # 回调函数
        self.progress_callback: Optional[Callable] = None
        self.status_callback: Optional[Callable] = None
        self.completion_callback: Optional[Callable] = None
        
        self.logger.info(f"SWANKSALON批量处理器初始化完成，最大工作线程数: {max_workers}")
    
    def set_callbacks(self, progress_callback: Optional[Callable] = None,
                     status_callback: Optional[Callable] = None,
                     completion_callback: Optional[Callable] = None):
        """设置回调函数"""
        self.progress_callback = progress_callback
        self.status_callback = status_callback
        self.completion_callback = completion_callback
    
    def add_task(self, task: SwankSalonTask) -> bool:
        """添加处理任务"""
        try:
            with self._lock:
                if task.id in self.tasks:
                    self.logger.warning(f"任务ID已存在: {task.id}")
                    return False
                
                self.tasks[task.id] = task
                self.task_queue.put(task)
                self.stats.total_tasks += 1
                self.stats.pending_tasks += 1
                
                self.logger.info(f"任务已添加: {task.id}")
                return True
                
        except Exception as e:
            self.logger.error(f"添加任务失败: {e}")
            return False
    
    def create_batch_tasks(self, config: Dict[str, Any], output_dir: str) -> List[str]:
        """
        根据配置创建批量任务
        
        Args:
            config: SWANKSALON配置
            output_dir: 输出目录
            
        Returns:
            创建的任务ID列表
        """
        try:
            os.makedirs(output_dir, exist_ok=True)
            task_ids = []
            
            videos = config.get('videos', [])
            for i, video_config in enumerate(videos):
                video_path = video_config.get('path', '')
                if not os.path.exists(video_path):
                    self.logger.warning(f"视频文件不存在: {video_path}")
                    continue
                
                # 生成任务ID和输出路径
                task_id = f"swanksalon_{i+1:03d}_{int(time.time())}"
                output_filename = f"swanksalon_output_{i+1:03d}.mp4"
                output_path = os.path.join(output_dir, output_filename)
                
                # 创建单个视频的配置
                single_config = config.copy()
                single_config['videos'] = [video_config]
                
                # 创建任务
                task = SwankSalonTask(
                    id=task_id,
                    video_path=video_path,
                    output_path=output_path,
                    config=single_config,
                    priority=video_config.get('priority', 0)
                )
                
                if self.add_task(task):
                    task_ids.append(task_id)
            
            self.logger.info(f"批量创建任务完成: {len(task_ids)} 个任务")
            return task_ids
            
        except Exception as e:
            self.logger.error(f"创建批量任务失败: {e}")
            return []
    
    def start_processing(self) -> bool:
        """开始批量处理"""
        try:
            if self.is_processing:
                self.logger.warning("批量处理已在进行中")
                return False
            
            if self.stats.pending_tasks == 0:
                self.logger.warning("没有待处理的任务")
                return False
            
            self.is_processing = True
            self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
            
            # 启动处理线程
            processing_thread = threading.Thread(target=self._process_tasks_loop)
            processing_thread.daemon = True
            processing_thread.start()
            
            self.logger.info("SWANKSALON批量处理已启动")
            return True
            
        except Exception as e:
            self.logger.error(f"启动批量处理失败: {e}")
            self.is_processing = False
            return False
    
    def stop_processing(self):
        """停止批量处理"""
        try:
            if not self.is_processing:
                return
            
            self.is_processing = False
            
            if self.executor:
                self.executor.shutdown(wait=True)
                self.executor = None
            
            self.logger.info("SWANKSALON批量处理已停止")
            
        except Exception as e:
            self.logger.error(f"停止批量处理失败: {e}")
    
    def _process_tasks_loop(self):
        """处理任务的主循环"""
        try:
            futures = {}
            
            while self.is_processing and (self.stats.pending_tasks > 0 or futures):
                # 提交新任务
                while len(futures) < self.max_workers and not self.task_queue.empty():
                    try:
                        task = self.task_queue.get_nowait()
                        future = self.executor.submit(self._process_single_task, task)
                        futures[future] = task
                        
                        # 更新任务状态
                        with self._lock:
                            task.status = "processing"
                            task.start_time = time.time()
                            self.stats.pending_tasks -= 1
                            self.stats.processing_tasks += 1
                        
                        # 状态回调
                        if self.status_callback:
                            self.status_callback(f"开始处理: {os.path.basename(task.video_path)}")
                        
                    except:
                        break
                
                # 检查完成的任务
                if futures:
                    for future in as_completed(futures, timeout=1.0):
                        task = futures.pop(future)
                        success = False
                        
                        try:
                            success = future.result()
                        except Exception as e:
                            task.error_message = str(e)
                            self.logger.error(f"任务处理异常 {task.id}: {e}")
                        
                        # 更新任务状态
                        with self._lock:
                            task.status = "completed" if success else "failed"
                            task.end_time = time.time()
                            task.progress = 100.0
                            
                            self.stats.processing_tasks -= 1
                            if success:
                                self.stats.completed_tasks += 1
                            else:
                                self.stats.failed_tasks += 1
                            
                            # 更新处理时间统计
                            if task.start_time and task.end_time:
                                processing_time = task.end_time - task.start_time
                                self.stats.total_processing_time += processing_time
                                
                                completed_count = self.stats.completed_tasks + self.stats.failed_tasks
                                if completed_count > 0:
                                    self.stats.average_processing_time = self.stats.total_processing_time / completed_count
                        
                        # 进度回调
                        if self.progress_callback:
                            progress = self.get_progress_percentage()
                            self.progress_callback(int(progress))
                        
                        # 状态回调
                        if self.status_callback:
                            status = "完成" if success else "失败"
                            self.status_callback(f"{status}: {os.path.basename(task.video_path)}")
                        
                        break  # 只处理一个完成的任务
                
                # 短暂休眠
                time.sleep(0.1)
            
            # 处理完成
            self.is_processing = False
            
            # 完成回调
            if self.completion_callback:
                success_rate = self.stats.completed_tasks / max(1, self.stats.total_tasks) * 100
                message = f"批量处理完成！成功: {self.stats.completed_tasks}, 失败: {self.stats.failed_tasks}, 成功率: {success_rate:.1f}%"
                self.completion_callback(self.stats.completed_tasks > 0, message)
            
            self.logger.info(f"SWANKSALON批量处理完成: 成功 {self.stats.completed_tasks}, 失败 {self.stats.failed_tasks}")
            
        except Exception as e:
            self.logger.error(f"批量处理主循环异常: {e}")
            self.is_processing = False
    
    def _process_single_task(self, task: SwankSalonTask) -> bool:
        """处理单个任务"""
        try:
            self.logger.info(f"开始处理任务: {task.id}")
            
            # 创建SWANKSALON引擎
            engine = SwankSalonEngine(task.config)
            
            # 处理单个视频
            success = engine.process_single(task.video_path, task.output_path)
            
            if success:
                self.logger.info(f"任务处理成功: {task.id}")
            else:
                self.logger.error(f"任务处理失败: {task.id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"处理任务异常 {task.id}: {e}")
            return False
    
    def get_task_status(self, task_id: str) -> Optional[SwankSalonTask]:
        """获取任务状态"""
        return self.tasks.get(task_id)
    
    def get_all_tasks(self) -> List[SwankSalonTask]:
        """获取所有任务"""
        return list(self.tasks.values())
    
    def get_stats(self) -> BatchStats:
        """获取处理统计"""
        return self.stats
    
    def get_progress_percentage(self) -> float:
        """获取总体进度百分比"""
        try:
            if self.stats.total_tasks <= 0:
                return 0.0
            
            completed = self.stats.completed_tasks + self.stats.failed_tasks
            return (completed / self.stats.total_tasks) * 100.0
            
        except:
            return 0.0
    
    def estimate_remaining_time(self) -> float:
        """估算剩余处理时间（秒）"""
        try:
            if self.stats.average_processing_time <= 0:
                return 0.0
            
            remaining_tasks = self.stats.pending_tasks + self.stats.processing_tasks
            return remaining_tasks * self.stats.average_processing_time
            
        except:
            return 0.0
    
    def clear_completed_tasks(self):
        """清理已完成的任务"""
        try:
            with self._lock:
                completed_tasks = [task_id for task_id, task in self.tasks.items() 
                                 if task.status in ["completed", "failed"]]
                
                for task_id in completed_tasks:
                    del self.tasks[task_id]
                
                # 重置统计
                self.stats.total_tasks -= len(completed_tasks)
                self.stats.completed_tasks = 0
                self.stats.failed_tasks = 0
                
                self.logger.info(f"清理了 {len(completed_tasks)} 个已完成任务")
                
        except Exception as e:
            self.logger.error(f"清理任务失败: {e}")
    
    def export_report(self, output_path: str) -> bool:
        """导出处理报告"""
        try:
            report = {
                'timestamp': time.time(),
                'processing_summary': {
                    'total_tasks': self.stats.total_tasks,
                    'completed_tasks': self.stats.completed_tasks,
                    'failed_tasks': self.stats.failed_tasks,
                    'success_rate': self.stats.completed_tasks / max(1, self.stats.total_tasks) * 100,
                    'total_processing_time': self.stats.total_processing_time,
                    'average_processing_time': self.stats.average_processing_time
                },
                'task_details': []
            }
            
            for task in self.tasks.values():
                task_info = {
                    'id': task.id,
                    'video_path': task.video_path,
                    'output_path': task.output_path,
                    'status': task.status,
                    'progress': task.progress,
                    'processing_time': (task.end_time - task.start_time) if task.start_time and task.end_time else 0,
                    'error_message': task.error_message
                }
                report['task_details'].append(task_info)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=4, ensure_ascii=False)
            
            self.logger.info(f"SWANKSALON处理报告已导出: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出处理报告失败: {e}")
            return False
