#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主界面模块
包含应用程序的主窗口界面和拆分后的各个组件
"""

# 导出主要组件
from .main_window_simplified import MainWindow
from .media_library import MediaLibrary, ModernButton
from .multi_track_timeline import MultiTrackTimeline, TimelineScrollContent
from .timeline_components import TimelineRuler, PlayheadOverlay, TrimHandlesOverlay
from .track_components import TrackLabel
from .track_widget import TrackWidget
from .video_thumbnail_block import VideoThumbnailBlock
from .audio_waveform_block import AudioWaveformBlock

__all__ = [
    'MainWindow',
    'MediaLibrary',
    'ModernButton',
    'MultiTrackTimeline',
    'TimelineScrollContent',
    'TimelineRuler',
    'PlayheadOverlay',
    'TrimHandlesOverlay',
    'TrackLabel',
    'TrackWidget',
    'VideoThumbnailBlock',
    'AudioWaveformBlock'
]
