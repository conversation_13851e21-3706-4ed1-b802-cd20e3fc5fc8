#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像处理工具模块
"""

import cv2
import numpy as np
from pathlib import Path
from typing import Optional, Tuple, Union

try:
    from PIL import Image, ImageEnhance, ImageFilter
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False


def resize_image(image_path: Union[str, Path], 
                output_path: Union[str, Path],
                size: Tuple[int, int],
                keep_aspect_ratio: bool = True,
                quality: int = 95) -> bool:
    """
    调整图像大小
    
    Args:
        image_path: 输入图像路径
        output_path: 输出图像路径
        size: 目标大小 (width, height)
        keep_aspect_ratio: 是否保持宽高比
        quality: JPEG质量 (1-100)
        
    Returns:
        是否成功
    """
    try:
        if PIL_AVAILABLE:
            return _resize_image_pil(image_path, output_path, size, keep_aspect_ratio, quality)
        else:
            return _resize_image_cv2(image_path, output_path, size, keep_aspect_ratio)
    except Exception:
        return False


def _resize_image_pil(image_path: Union[str, Path], 
                     output_path: Union[str, Path],
                     size: Tuple[int, int],
                     keep_aspect_ratio: bool,
                     quality: int) -> bool:
    """使用PIL调整图像大小"""
    with Image.open(str(image_path)) as img:
        if keep_aspect_ratio:
            img.thumbnail(size, Image.Resampling.LANCZOS)
        else:
            img = img.resize(size, Image.Resampling.LANCZOS)
        
        # 保存图像
        save_kwargs = {}
        if str(output_path).lower().endswith(('.jpg', '.jpeg')):
            save_kwargs['quality'] = quality
            save_kwargs['optimize'] = True
        
        img.save(str(output_path), **save_kwargs)
        return True


def _resize_image_cv2(image_path: Union[str, Path], 
                     output_path: Union[str, Path],
                     size: Tuple[int, int],
                     keep_aspect_ratio: bool) -> bool:
    """使用OpenCV调整图像大小"""
    img = cv2.imread(str(image_path))
    if img is None:
        return False
    
    if keep_aspect_ratio:
        h, w = img.shape[:2]
        target_w, target_h = size
        
        # 计算缩放比例
        scale = min(target_w / w, target_h / h)
        new_w = int(w * scale)
        new_h = int(h * scale)
        
        resized = cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_LANCZOS4)
    else:
        resized = cv2.resize(img, size, interpolation=cv2.INTER_LANCZOS4)
    
    return cv2.imwrite(str(output_path), resized)


def create_thumbnail(image_path: Union[str, Path], 
                    output_path: Union[str, Path],
                    size: Tuple[int, int] = (150, 150),
                    quality: int = 85) -> bool:
    """
    创建缩略图
    
    Args:
        image_path: 输入图像路径
        output_path: 输出缩略图路径
        size: 缩略图大小
        quality: JPEG质量
        
    Returns:
        是否成功创建缩略图
    """
    return resize_image(image_path, output_path, size, keep_aspect_ratio=True, quality=quality)


def crop_image(image_path: Union[str, Path],
               output_path: Union[str, Path],
               crop_box: Tuple[int, int, int, int]) -> bool:
    """
    裁剪图像
    
    Args:
        image_path: 输入图像路径
        output_path: 输出图像路径
        crop_box: 裁剪框 (x, y, width, height)
        
    Returns:
        是否成功
    """
    try:
        if PIL_AVAILABLE:
            with Image.open(str(image_path)) as img:
                x, y, w, h = crop_box
                cropped = img.crop((x, y, x + w, y + h))
                cropped.save(str(output_path))
                return True
        else:
            img = cv2.imread(str(image_path))
            if img is None:
                return False
            
            x, y, w, h = crop_box
            cropped = img[y:y+h, x:x+w]
            return cv2.imwrite(str(output_path), cropped)
    except Exception:
        return False


def enhance_image(image_path: Union[str, Path],
                 output_path: Union[str, Path],
                 brightness: float = 1.0,
                 contrast: float = 1.0,
                 saturation: float = 1.0,
                 sharpness: float = 1.0) -> bool:
    """
    图像增强
    
    Args:
        image_path: 输入图像路径
        output_path: 输出图像路径
        brightness: 亮度调整 (1.0为原始)
        contrast: 对比度调整 (1.0为原始)
        saturation: 饱和度调整 (1.0为原始)
        sharpness: 锐度调整 (1.0为原始)
        
    Returns:
        是否成功
    """
    if not PIL_AVAILABLE:
        return False
    
    try:
        with Image.open(str(image_path)) as img:
            # 亮度调整
            if brightness != 1.0:
                enhancer = ImageEnhance.Brightness(img)
                img = enhancer.enhance(brightness)
            
            # 对比度调整
            if contrast != 1.0:
                enhancer = ImageEnhance.Contrast(img)
                img = enhancer.enhance(contrast)
            
            # 饱和度调整
            if saturation != 1.0:
                enhancer = ImageEnhance.Color(img)
                img = enhancer.enhance(saturation)
            
            # 锐度调整
            if sharpness != 1.0:
                enhancer = ImageEnhance.Sharpness(img)
                img = enhancer.enhance(sharpness)
            
            img.save(str(output_path))
            return True
    except Exception:
        return False


def apply_blur(image_path: Union[str, Path],
               output_path: Union[str, Path],
               blur_radius: float = 2.0) -> bool:
    """
    应用模糊效果
    
    Args:
        image_path: 输入图像路径
        output_path: 输出图像路径
        blur_radius: 模糊半径
        
    Returns:
        是否成功
    """
    try:
        if PIL_AVAILABLE:
            with Image.open(str(image_path)) as img:
                blurred = img.filter(ImageFilter.GaussianBlur(radius=blur_radius))
                blurred.save(str(output_path))
                return True
        else:
            img = cv2.imread(str(image_path))
            if img is None:
                return False
            
            kernel_size = int(blur_radius * 6) | 1  # 确保为奇数
            blurred = cv2.GaussianBlur(img, (kernel_size, kernel_size), blur_radius)
            return cv2.imwrite(str(output_path), blurred)
    except Exception:
        return False


def get_image_info(image_path: Union[str, Path]) -> dict:
    """
    获取图像信息
    
    Args:
        image_path: 图像路径
        
    Returns:
        图像信息字典
    """
    try:
        if PIL_AVAILABLE:
            with Image.open(str(image_path)) as img:
                return {
                    'width': img.width,
                    'height': img.height,
                    'mode': img.mode,
                    'format': img.format,
                    'has_transparency': img.mode in ('RGBA', 'LA') or 'transparency' in img.info
                }
        else:
            img = cv2.imread(str(image_path))
            if img is None:
                raise ValueError("无法读取图像")
            
            h, w = img.shape[:2]
            channels = img.shape[2] if len(img.shape) > 2 else 1
            
            return {
                'width': w,
                'height': h,
                'channels': channels,
                'dtype': str(img.dtype)
            }
    except Exception as e:
        raise ValueError(f"无法获取图像信息: {e}")


def convert_format(image_path: Union[str, Path],
                  output_path: Union[str, Path],
                  quality: int = 95) -> bool:
    """
    转换图像格式
    
    Args:
        image_path: 输入图像路径
        output_path: 输出图像路径
        quality: JPEG质量
        
    Returns:
        是否成功
    """
    try:
        if PIL_AVAILABLE:
            with Image.open(str(image_path)) as img:
                # 如果是RGBA模式且要保存为JPEG，转换为RGB
                if img.mode == 'RGBA' and str(output_path).lower().endswith(('.jpg', '.jpeg')):
                    # 创建白色背景
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    background.paste(img, mask=img.split()[-1])  # 使用alpha通道作为mask
                    img = background
                
                save_kwargs = {}
                if str(output_path).lower().endswith(('.jpg', '.jpeg')):
                    save_kwargs['quality'] = quality
                    save_kwargs['optimize'] = True
                
                img.save(str(output_path), **save_kwargs)
                return True
        else:
            img = cv2.imread(str(image_path))
            if img is None:
                return False
            return cv2.imwrite(str(output_path), img)
    except Exception:
        return False
