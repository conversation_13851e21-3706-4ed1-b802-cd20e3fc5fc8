#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能通知系统 - 提供人性化的用户反馈和指导
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                               QPushButton, QFrame, QGraphicsOpacityEffect,
                               QApplication, QSystemTrayIcon, QMenu, QMessageBox)
from PySide6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, QRect, Signal
from PySide6.QtGui import QFont, QIcon, QPixmap, QPainter, QColor, QBrush

from enum import Enum
from typing import Optional, Callable
import time

from core.common.logger import get_logger


class NotificationType(Enum):
    """通知类型"""
    INFO = "info"
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"
    TIP = "tip"
    PROGRESS = "progress"


class SmartNotification(QWidget):
    """智能通知组件"""
    
    # 信号
    clicked = Signal()
    action_clicked = Signal(str)
    
    def __init__(self, notification_type: NotificationType, title: str, 
                 message: str, duration: int = 5000, parent=None):
        super().__init__(parent)
        self.notification_type = notification_type
        self.title = title
        self.message = message
        self.duration = duration
        self.logger = get_logger('smart_notification')
        
        self.setFixedSize(350, 100)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        
        self.init_ui()
        self.setup_animations()
        
        # 自动隐藏定时器
        if duration > 0:
            QTimer.singleShot(duration, self.hide_notification)
    
    def init_ui(self):
        """初始化界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 图标区域
        icon_label = QLabel()
        icon_label.setFixedSize(32, 32)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 根据类型设置图标和颜色
        icon_text, bg_color, text_color = self.get_style_for_type()
        icon_label.setText(icon_text)
        icon_label.setStyleSheet(f"""
            QLabel {{
                background-color: {bg_color};
                color: white;
                border-radius: 16px;
                font-size: 18px;
                font-weight: bold;
            }}
        """)
        
        layout.addWidget(icon_label)
        
        # 内容区域
        content_layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel(self.title)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {text_color};
                font-weight: bold;
                font-size: 14px;
                margin: 0px;
            }}
        """)
        content_layout.addWidget(title_label)
        
        # 消息
        message_label = QLabel(self.message)
        message_label.setWordWrap(True)
        message_label.setStyleSheet(f"""
            QLabel {{
                color: {text_color};
                font-size: 12px;
                margin: 0px;
            }}
        """)
        content_layout.addWidget(message_label)
        
        layout.addLayout(content_layout)
        
        # 关闭按钮
        close_button = QPushButton("×")
        close_button.setFixedSize(20, 20)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                color: #666;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #f0f0f0;
                border-radius: 10px;
            }
        """)
        close_button.clicked.connect(self.hide_notification)
        layout.addWidget(close_button)
        
        # 设置整体样式
        self.setStyleSheet(f"""
            SmartNotification {{
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                border-left: 4px solid {bg_color};
            }}
        """)
    
    def get_style_for_type(self):
        """根据类型获取样式"""
        styles = {
            NotificationType.INFO: ("ℹ️", "#2196F3", "#333"),
            NotificationType.SUCCESS: ("✅", "#4CAF50", "#333"),
            NotificationType.WARNING: ("⚠️", "#FF9800", "#333"),
            NotificationType.ERROR: ("❌", "#F44336", "#333"),
            NotificationType.TIP: ("💡", "#9C27B0", "#333"),
            NotificationType.PROGRESS: ("⏳", "#607D8B", "#333")
        }
        return styles.get(self.notification_type, styles[NotificationType.INFO])
    
    def setup_animations(self):
        """设置动画"""
        # 透明度动画
        self.opacity_effect = QGraphicsOpacityEffect()
        self.setGraphicsEffect(self.opacity_effect)
        
        self.fade_in_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.fade_in_animation.setDuration(300)
        self.fade_in_animation.setStartValue(0.0)
        self.fade_in_animation.setEndValue(1.0)
        self.fade_in_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        self.fade_out_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.fade_out_animation.setDuration(300)
        self.fade_out_animation.setStartValue(1.0)
        self.fade_out_animation.setEndValue(0.0)
        self.fade_out_animation.setEasingCurve(QEasingCurve.Type.InCubic)
        self.fade_out_animation.finished.connect(self.close)
    
    def show_notification(self):
        """显示通知"""
        self.show()
        self.fade_in_animation.start()
    
    def hide_notification(self):
        """隐藏通知"""
        self.fade_out_animation.start()
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)


class SmartNotificationManager:
    """智能通知管理器"""
    
    def __init__(self, parent_widget=None):
        self.parent_widget = parent_widget
        self.notifications = []
        self.logger = get_logger('notification_manager')
        
        # 通知位置管理
        self.notification_spacing = 10
        self.notification_margin = 20
    
    def show_notification(self, notification_type: NotificationType, title: str,
                         message: str, duration: int = 5000, 
                         action_text: str = None, action_callback: Callable = None):
        """显示通知"""
        try:
            notification = SmartNotification(
                notification_type, title, message, duration, self.parent_widget
            )
            
            # 连接信号
            if action_callback:
                notification.clicked.connect(action_callback)
            
            # 计算位置
            self.position_notification(notification)
            
            # 显示通知
            notification.show_notification()
            
            # 添加到管理列表
            self.notifications.append(notification)
            
            # 清理已关闭的通知
            self.cleanup_notifications()
            
            self.logger.info(f"Notification shown: {title}")
            
        except Exception as e:
            self.logger.error(f"Failed to show notification: {str(e)}")
    
    def position_notification(self, notification):
        """定位通知"""
        if not self.parent_widget:
            return
        
        # 获取父窗口的几何信息
        parent_rect = self.parent_widget.geometry()
        
        # 计算通知位置（右上角）
        x = parent_rect.right() - notification.width() - self.notification_margin
        y = parent_rect.top() + self.notification_margin
        
        # 如果有其他通知，向下偏移
        for existing_notification in self.notifications:
            if existing_notification.isVisible():
                y += notification.height() + self.notification_spacing
        
        notification.move(x, y)
    
    def cleanup_notifications(self):
        """清理已关闭的通知"""
        self.notifications = [n for n in self.notifications if n.isVisible()]
    
    def show_success(self, title: str, message: str, duration: int = 3000):
        """显示成功通知"""
        self.show_notification(NotificationType.SUCCESS, title, message, duration)
    
    def show_error(self, title: str, message: str, duration: int = 8000):
        """显示错误通知"""
        self.show_notification(NotificationType.ERROR, title, message, duration)
    
    def show_warning(self, title: str, message: str, duration: int = 5000):
        """显示警告通知"""
        self.show_notification(NotificationType.WARNING, title, message, duration)
    
    def show_info(self, title: str, message: str, duration: int = 4000):
        """显示信息通知"""
        self.show_notification(NotificationType.INFO, title, message, duration)
    
    def show_tip(self, title: str, message: str, duration: int = 6000):
        """显示提示通知"""
        self.show_notification(NotificationType.TIP, title, message, duration)
    
    def show_progress(self, title: str, message: str, duration: int = 0):
        """显示进度通知"""
        self.show_notification(NotificationType.PROGRESS, title, message, duration)


class SmartStatusBar:
    """智能状态栏"""
    
    def __init__(self, status_bar):
        self.status_bar = status_bar
        self.logger = get_logger('smart_status_bar')
        
        # 状态信息
        self.current_operation = None
        self.progress_info = None
        
        # 创建状态栏组件
        self.setup_status_widgets()
    
    def setup_status_widgets(self):
        """设置状态栏组件"""
        # 操作状态标签
        self.operation_label = QLabel("就绪")
        self.operation_label.setStyleSheet("QLabel { margin: 2px 10px; }")
        self.status_bar.addWidget(self.operation_label)
        
        # 分隔符
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.VLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        self.status_bar.addWidget(separator)
        
        # 智能提示标签
        self.tip_label = QLabel()
        self.tip_label.setStyleSheet("QLabel { color: #666; margin: 2px 10px; }")
        self.status_bar.addWidget(self.tip_label)
        
        # 右侧永久组件
        self.status_bar.addPermanentWidget(QLabel("SWANKSALON v1.0"))
    
    def set_operation_status(self, status: str, details: str = ""):
        """设置操作状态"""
        self.current_operation = status
        
        if details:
            self.operation_label.setText(f"{status} - {details}")
        else:
            self.operation_label.setText(status)
        
        self.logger.info(f"Status updated: {status}")
    
    def set_smart_tip(self, tip: str, duration: int = 10000):
        """设置智能提示"""
        self.tip_label.setText(f"💡 {tip}")
        
        if duration > 0:
            QTimer.singleShot(duration, lambda: self.tip_label.setText(""))
    
    def show_progress(self, operation: str, progress: int, total: int = 100):
        """显示进度"""
        percentage = int((progress / total) * 100) if total > 0 else 0
        self.set_operation_status(f"{operation} ({percentage}%)")
    
    def clear_status(self):
        """清空状态"""
        self.operation_label.setText("就绪")
        self.tip_label.setText("")
        self.current_operation = None


class SmartGuidanceSystem:
    """智能引导系统"""
    
    def __init__(self, main_window, notification_manager):
        self.main_window = main_window
        self.notification_manager = notification_manager
        self.logger = get_logger('guidance_system')
        
        # 引导状态
        self.guidance_enabled = True
        self.shown_tips = set()
    
    def check_and_show_guidance(self, context: str):
        """检查并显示引导"""
        if not self.guidance_enabled:
            return
        
        guidance_tips = {
            'first_launch': {
                'title': '🎉 欢迎使用智能视频编辑器',
                'message': '点击工具栏的"🚀 智能批量处理"开始制作客户视频',
                'duration': 8000
            },
            'empty_timeline': {
                'title': '💡 开始制作视频',
                'message': '拖拽视频文件到时间轴，或使用批量处理功能',
                'duration': 5000
            },
            'batch_processing_available': {
                'title': '⚡ 提高效率',
                'message': '使用智能批量处理可以同时处理多个客户视频',
                'duration': 6000
            },
            'template_suggestion': {
                'title': '🎨 使用模板',
                'message': '理发店模板可以让您的视频更专业',
                'duration': 5000
            }
        }
        
        if context in guidance_tips and context not in self.shown_tips:
            tip = guidance_tips[context]
            self.notification_manager.show_tip(
                tip['title'], tip['message'], tip['duration']
            )
            self.shown_tips.add(context)
    
    def show_contextual_help(self, feature: str):
        """显示上下文帮助"""
        help_content = {
            'smart_batch': {
                'title': '🚀 智能批量处理帮助',
                'message': '1. 选择模板\n2. 添加客户视频\n3. 配置智能功能\n4. 开始处理',
                'duration': 10000
            },
            'template_manager': {
                'title': '🎨 模板管理帮助',
                'message': '创建和编辑视频模板，设置片段时长和效果',
                'duration': 8000
            },
            'enhanced_features': {
                'title': '⚡ 增强功能帮助',
                'message': '使用AI功能：自动踩点、文字识别、音频分析',
                'duration': 8000
            }
        }
        
        if feature in help_content:
            help_info = help_content[feature]
            self.notification_manager.show_info(
                help_info['title'], help_info['message'], help_info['duration']
            )
    
    def disable_guidance(self):
        """禁用引导"""
        self.guidance_enabled = False
    
    def enable_guidance(self):
        """启用引导"""
        self.guidance_enabled = True
