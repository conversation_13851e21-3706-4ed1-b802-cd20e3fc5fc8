#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一常量管理器 - 管理所有频繁获取的变量和配置
"""

import threading
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass, field
from pathlib import Path
from enum import Enum
from contextlib import contextmanager

class RWLock:
    """读写锁实现"""
    def __init__(self):
        self._read_ready = threading.Condition(threading.RLock())
        self._readers = 0

    @contextmanager
    def r_locked(self):
        """读锁上下文管理器"""
        self.acquire_read()
        try:
            yield
        finally:
            self.release_read()

    @contextmanager
    def w_locked(self):
        """写锁上下文管理器"""
        self.acquire_write()
        try:
            yield
        finally:
            self.release_write()

    def acquire_read(self):
        """获取读锁"""
        self._read_ready.acquire()
        try:
            self._readers += 1
        finally:
            self._read_ready.release()

    def release_read(self):
        """释放读锁"""
        self._read_ready.acquire()
        try:
            self._readers -= 1
            if self._readers == 0:
                self._read_ready.notifyAll()
        finally:
            self._read_ready.release()

    def acquire_write(self):
        """获取写锁"""
        self._read_ready.acquire()
        while self._readers > 0:
            self._read_ready.wait()

    def release_write(self):
        """释放写锁"""
        self._read_ready.release()

class ThemeType(Enum):
    """主题类型"""
    DARK = "dark"
    LIGHT = "light"
    AUTO = "auto"

class QualityPreset(Enum):
    """质量预设"""
    SMOOTH = "smooth"      # 流畅模式
    HIGH = "high"          # 高清模式  
    ORIGINAL = "original"  # 原画模式

@dataclass
class UIColors:
    """UI颜色配置"""
    # 主要背景色
    primary_bg: str = "#161616"
    secondary_bg: str = "#333333"
    tertiary_bg: str = "#4B4D52"
    
    # 文本颜色
    primary_text: str = "#F2ECFF"
    secondary_text: str = "#B6ECFF"
    disabled_text: str = "#666666"
    
    # 强调色
    accent_color: str = "#00C896"
    warning_color: str = "#FF9800"
    error_color: str = "#F44336"
    success_color: str = "#4CAF50"
    
    # 边框和分割线
    border_color: str = "#666666"
    separator_color: str = "#2A2A2A"
    
    # 按钮颜色
    button_bg: str = "#555555"
    button_hover: str = "#666666"
    button_pressed: str = "#444444"
    
    # 轨道颜色
    track_bg: str = "#333333"
    timeline_bg: str = "#505C70"
    media_block_bg: str = "#4A4857"

@dataclass
class UIDimensions:
    """UI尺寸配置"""
    # 窗口尺寸
    min_window_width: int = 1400
    min_window_height: int = 900
    default_window_width: int = 1920
    default_window_height: int = 1080
    
    # 面板尺寸
    media_library_width: int = 528
    video_preview_width: int = 1024
    info_panel_width: int = 368
    
    # 轨道尺寸
    track_height: int = 64
    track_spacing: int = 12
    track_label_width: int = 128
    track_content_width: int = 1792
    timeline_ruler_height: int = 48
    
    # 控制栏尺寸
    top_button_bar_height: int = 48
    bottom_control_height: int = 48
    
    # 媒体库尺寸
    media_library_height: int = 676
    video_window_height: int = 628
    
    # 边距和圆角
    default_margin: int = 12
    small_margin: int = 6
    large_margin: int = 24
    border_radius: int = 5
    
    # 字体大小
    default_font_size: int = 12
    small_font_size: int = 10
    large_font_size: int = 14
    title_font_size: int = 16

@dataclass
class TimelineConfig:
    """时间轴配置"""
    # 缩放配置
    zoom_levels: list = field(default_factory=lambda: [1, 2, 5, 10, 25, 50, 100, 200, 400, 800])
    default_zoom_index: int = 6  # 100像素/秒
    min_pixels_per_second: int = 1
    max_pixels_per_second: int = 1000
    
    # 播放速度配置
    playback_speeds: list = field(default_factory=lambda: [0.25, 0.5, 0.75, 1.0, 1.25, 1.5, 2.0, 4.0])
    default_speed_index: int = 3  # 1.0x
    
    # 磁性吸附配置
    snap_enabled: bool = True
    snap_threshold: int = 10  # 像素
    grid_snap_interval: float = 0.5  # 秒
    
    # 缩略图配置
    thumbnail_width: int = 120
    thumbnail_height: int = 80
    min_thumbnail_width: int = 80
    max_thumbnail_width: int = 160
    thumbnail_cache_size: int = 100

@dataclass
class MediaConfig:
    """媒体配置"""
    # 支持的格式
    video_formats: list = field(default_factory=lambda: ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'])
    audio_formats: list = field(default_factory=lambda: ['.mp3', '.wav', '.aac', '.m4a', '.flac', '.ogg', '.wma'])
    image_formats: list = field(default_factory=lambda: ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff'])
    
    # 默认参数
    default_fps: float = 30.0
    default_sample_rate: int = 44100
    default_bitrate: str = "192k"
    
    # 缓存配置
    cache_size: str = "1GB"
    temp_directory: str = "./temp"
    max_cache_files: int = 1000
    
    # 预览质量配置
    preview_quality_smooth: float = 0.5    # 50%缩放
    preview_quality_high: float = 0.75     # 75%缩放
    preview_quality_original: float = 1.0  # 原始大小

@dataclass
class PerformanceConfig:
    """性能配置"""
    # 线程配置
    max_worker_threads: int = 4
    max_render_threads: int = 2
    io_thread_count: int = 2
    
    # 内存配置
    max_memory_usage: str = "2GB"
    frame_cache_size: int = 50
    thumbnail_cache_size: int = 200
    
    # 渲染配置
    hardware_acceleration: bool = True
    gpu_memory_fraction: float = 0.8
    render_ahead_frames: int = 10

class UnifiedConstantsManager:
    """
    统一常量管理器
    - 管理所有UI常量、配置参数
    - 提供线程安全的访问
    - 支持运行时配置更新
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self._config_lock = threading.RWLock()
        
        # 初始化配置
        self.ui_colors = UIColors()
        self.ui_dimensions = UIDimensions()
        self.timeline_config = TimelineConfig()
        self.media_config = MediaConfig()
        self.performance_config = PerformanceConfig()
        
        # 当前主题
        self.current_theme = ThemeType.DARK
        
        # 动态计算的值缓存
        self._calculated_cache: Dict[str, Any] = {}
        self._cache_lock = threading.Lock()
        
        print("✅ 统一常量管理器初始化完成")
    
    # ==================== 颜色管理 ====================
    
    def get_color(self, color_name: str) -> str:
        """获取颜色值"""
        with self._config_lock.r_locked():
            return getattr(self.ui_colors, color_name, "#FFFFFF")
    
    def set_color(self, color_name: str, color_value: str):
        """设置颜色值"""
        with self._config_lock.w_locked():
            if hasattr(self.ui_colors, color_name):
                setattr(self.ui_colors, color_name, color_value)
                self._clear_cache()
    
    def get_theme_colors(self) -> Dict[str, str]:
        """获取当前主题的所有颜色"""
        with self._config_lock.r_locked():
            return {
                'primary_bg': self.ui_colors.primary_bg,
                'secondary_bg': self.ui_colors.secondary_bg,
                'tertiary_bg': self.ui_colors.tertiary_bg,
                'primary_text': self.ui_colors.primary_text,
                'secondary_text': self.ui_colors.secondary_text,
                'accent_color': self.ui_colors.accent_color,
                'border_color': self.ui_colors.border_color,
                'track_bg': self.ui_colors.track_bg,
                'timeline_bg': self.ui_colors.timeline_bg,
            }
    
    # ==================== 尺寸管理 ====================
    
    def get_dimension(self, dimension_name: str) -> int:
        """获取尺寸值"""
        with self._config_lock.r_locked():
            return getattr(self.ui_dimensions, dimension_name, 0)
    
    def set_dimension(self, dimension_name: str, value: int):
        """设置尺寸值"""
        with self._config_lock.w_locked():
            if hasattr(self.ui_dimensions, dimension_name):
                setattr(self.ui_dimensions, dimension_name, value)
                self._clear_cache()
    
    def get_window_size(self) -> Tuple[int, int]:
        """获取默认窗口尺寸"""
        with self._config_lock.r_locked():
            return (self.ui_dimensions.default_window_width, 
                   self.ui_dimensions.default_window_height)
    
    def get_panel_sizes(self) -> Tuple[int, int, int]:
        """获取面板尺寸 (媒体库, 预览, 信息面板)"""
        with self._config_lock.r_locked():
            return (self.ui_dimensions.media_library_width,
                   self.ui_dimensions.video_preview_width,
                   self.ui_dimensions.info_panel_width)
    
    # ==================== 时间轴配置 ====================
    
    def get_zoom_levels(self) -> list:
        """获取缩放级别"""
        with self._config_lock.r_locked():
            return self.timeline_config.zoom_levels.copy()
    
    def get_playback_speeds(self) -> list:
        """获取播放速度选项"""
        with self._config_lock.r_locked():
            return self.timeline_config.playback_speeds.copy()
    
    def get_default_pixels_per_second(self) -> int:
        """获取默认像素比例"""
        with self._config_lock.r_locked():
            zoom_levels = self.timeline_config.zoom_levels
            default_index = self.timeline_config.default_zoom_index
            return zoom_levels[default_index] if default_index < len(zoom_levels) else 100
    
    def get_snap_config(self) -> Dict[str, Any]:
        """获取磁性吸附配置"""
        with self._config_lock.r_locked():
            return {
                'enabled': self.timeline_config.snap_enabled,
                'threshold': self.timeline_config.snap_threshold,
                'interval': self.timeline_config.grid_snap_interval
            }
    
    # ==================== 媒体配置 ====================
    
    def get_supported_formats(self, media_type: str) -> list:
        """获取支持的格式"""
        with self._config_lock.r_locked():
            if media_type == 'video':
                return self.media_config.video_formats.copy()
            elif media_type == 'audio':
                return self.media_config.audio_formats.copy()
            elif media_type == 'image':
                return self.media_config.image_formats.copy()
            return []
    
    def get_default_media_params(self) -> Dict[str, Any]:
        """获取默认媒体参数"""
        with self._config_lock.r_locked():
            return {
                'fps': self.media_config.default_fps,
                'sample_rate': self.media_config.default_sample_rate,
                'bitrate': self.media_config.default_bitrate
            }
    
    def get_quality_scale(self, quality: QualityPreset) -> float:
        """获取质量缩放比例"""
        with self._config_lock.r_locked():
            if quality == QualityPreset.SMOOTH:
                return self.media_config.preview_quality_smooth
            elif quality == QualityPreset.HIGH:
                return self.media_config.preview_quality_high
            else:
                return self.media_config.preview_quality_original
    
    # ==================== 性能配置 ====================
    
    def get_thread_config(self) -> Dict[str, int]:
        """获取线程配置"""
        with self._config_lock.r_locked():
            return {
                'max_workers': self.performance_config.max_worker_threads,
                'max_render': self.performance_config.max_render_threads,
                'io_threads': self.performance_config.io_thread_count
            }
    
    def get_cache_config(self) -> Dict[str, Any]:
        """获取缓存配置"""
        with self._config_lock.r_locked():
            return {
                'frame_cache_size': self.performance_config.frame_cache_size,
                'thumbnail_cache_size': self.performance_config.thumbnail_cache_size,
                'max_memory': self.performance_config.max_memory_usage
            }
    
    # ==================== 动态计算值 ====================
    
    def get_timeline_width(self, duration: float, pixels_per_second: int) -> int:
        """计算时间轴宽度"""
        cache_key = f"timeline_width_{duration}_{pixels_per_second}"
        
        with self._cache_lock:
            if cache_key in self._calculated_cache:
                return self._calculated_cache[cache_key]
        
        width = max(3000, int(duration * pixels_per_second))
        
        with self._cache_lock:
            self._calculated_cache[cache_key] = width
        
        return width
    
    def get_track_total_height(self, track_count: int) -> int:
        """计算轨道总高度"""
        cache_key = f"track_total_height_{track_count}"
        
        with self._cache_lock:
            if cache_key in self._calculated_cache:
                return self._calculated_cache[cache_key]
        
        with self._config_lock.r_locked():
            height = (self.ui_dimensions.timeline_ruler_height + 
                     track_count * (self.ui_dimensions.track_height + self.ui_dimensions.track_spacing))
        
        with self._cache_lock:
            self._calculated_cache[cache_key] = height
        
        return height
    
    def get_media_block_width(self, duration: float, pixels_per_second: int) -> int:
        """计算媒体块宽度"""
        return max(10, int(duration * pixels_per_second))
    
    # ==================== 样式生成 ====================
    
    def generate_widget_style(self, widget_type: str) -> str:
        """生成组件样式"""
        cache_key = f"style_{widget_type}_{self.current_theme.value}"
        
        with self._cache_lock:
            if cache_key in self._calculated_cache:
                return self._calculated_cache[cache_key]
        
        style = self._create_widget_style(widget_type)
        
        with self._cache_lock:
            self._calculated_cache[cache_key] = style
        
        return style
    
    def _create_widget_style(self, widget_type: str) -> str:
        """创建组件样式"""
        colors = self.get_theme_colors()
        
        if widget_type == "media_library":
            return f"""
                QWidget {{
                    background-color: {colors['tertiary_bg']};
                    color: {colors['primary_text']};
                }}
                QTabBar::tab {{
                    background-color: {colors['secondary_bg']};
                    color: {colors['primary_text']};
                    padding: 8px 16px;
                }}
                QTabBar::tab:selected {{
                    background-color: {colors['accent_color']};
                }}
            """
        elif widget_type == "timeline":
            return f"""
                QWidget {{
                    background-color: {colors['timeline_bg']};
                    border: none;
                }}
            """
        elif widget_type == "track":
            return f"""
                QWidget {{
                    background-color: {colors['track_bg']};
                    border: 1px solid {colors['border_color']};
                }}
            """
        
        return ""
    
    # ==================== 缓存管理 ====================
    
    def _clear_cache(self):
        """清空缓存"""
        with self._cache_lock:
            self._calculated_cache.clear()
    
    def update_theme(self, theme: ThemeType):
        """更新主题"""
        with self._config_lock.w_locked():
            self.current_theme = theme
            if theme == ThemeType.LIGHT:
                self._apply_light_theme()
            else:
                self._apply_dark_theme()
            self._clear_cache()
    
    def _apply_dark_theme(self):
        """应用深色主题"""
        self.ui_colors = UIColors()  # 默认就是深色主题
    
    def _apply_light_theme(self):
        """应用浅色主题"""
        self.ui_colors.primary_bg = "#FFFFFF"
        self.ui_colors.secondary_bg = "#F0F0F0"
        self.ui_colors.tertiary_bg = "#E0E0E0"
        self.ui_colors.primary_text = "#000000"
        self.ui_colors.secondary_text = "#333333"
        self.ui_colors.border_color = "#CCCCCC"
    
    # ==================== 配置持久化 ====================
    
    def save_config(self, config_path: Optional[Path] = None):
        """保存配置到文件"""
        # TODO: 实现配置保存
        pass
    
    def load_config(self, config_path: Optional[Path] = None):
        """从文件加载配置"""
        # TODO: 实现配置加载
        pass

# 全局实例
constants = UnifiedConstantsManager()
