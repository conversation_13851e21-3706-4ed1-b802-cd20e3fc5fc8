# 视频编辑器

## 功能特性

- 🎬 剪映风格界面布局
- 📁 缩略图素材库
- 🎵 完整音视频同步
- ⏱️ 多轨道时间轴
- ✂️ 精确剪辑功能
- **🖼️ 智能视频缩略图显示**

## 新增功能：智能视频缩略图

### 功能描述
轨道上的视频素材现在会自动显示视频帧的缩略图，而不是单一颜色块。缩略图会根据轨道的缩放级别和长度智能调整显示的帧数。

### 特性
- **无限制帧数**：完全取消帧数限制，根据轨道宽度自动计算能放下多少帧（每帧120px宽）
  - 1帧：显示视频中间位置的帧
  - 多帧：严格按照时间轴比例分布，每帧精确对应其时间位置
- **精确时间轴对齐**：缩略图位置严格按照时间戳在轨道中的比例位置绘制
  - 最后一帧精确对应视频结尾时间
  - 每帧的中心点对齐其代表的时间点
- **连贯性保证**：时间轴越细（缩放越大），显示的帧数越多，提供更连贯的预览
- **无拉伸显示**：使用固定尺寸（120x68像素）的缩略图，保持16:9比例
- **更大轨道高度**：轨道高度从60px增加到100px，提供更好的视觉效果
- **彻底防残留**：多重清理机制，确保缩放时完全清除旧组件，无重影
- **异步加载优化**：缩略图采用渐进式加载，50ms间隔，避免界面卡顿
- **位置精确保持**：缩放时自动规范化媒体位置，防止浮点精度导致的偏移
- **智能缓存**：缩略图会被缓存以提高性能，轨道变化时自动更新
- **降级显示**：如果无法获取缩略图，会降级到原有的单色显示
- **文本叠加**：在缩略图上显示视频名称和时长信息

### 实现细节
- 使用 `VideoProcessor.get_thumbnail_frames()` 方法提取视频帧
- 新增 `VideoThumbnailBlock` 组件来替代原有的 `DraggableMediaBlock`
- 支持所有原有的拖拽和交互功能
- 在 `Timeline` 组件中集成缩略图显示

### 帧选择算法示例
```
视频时长: 10秒
轨道宽度: 480px → 能放下 4帧 (480÷120=4)

时间戳计算（精确对齐版本）:
帧1: 1.25秒  (第1个缩略图的中心位置)
帧2: 3.75秒  (第2个缩略图的中心位置)
帧3: 6.25秒  (第3个缩略图的中心位置)
帧4: 8.75秒  (第4个缩略图的中心位置)

公式: timestamp = (i + 0.5) / count * duration
位置: center_x = timestamp / duration * track_width
```

### 修复的问题
- ✅ **取消30帧限制**：现在可以无限制显示帧数
- ✅ **彻底解决残留**：多重清理机制，强制处理删除操作
- ✅ **时间轴精确对齐**：最后一帧准确对应视频结尾时间
- ✅ **异步渐进式加载**：缩略图逐帧加载，避免一次性加载卡顿
- ✅ **修复缩放偏移**：缩放时保持素材位置不偏移，左对齐精确

### 使用方法
1. 导入视频文件到素材库
2. 将视频拖拽到时间轴轨道上
3. 视频素材会自动显示缩略图（固定120x68像素，保持16:9比例）
4. 使用时间轴上的+/-按钮调整缩放级别：
   - **放大时间轴（+）**：轨道变宽，能放下更多帧，连贯预览视频内容
   - **缩小时间轴（-）**：轨道变窄，显示更少帧，快速概览
5. 帧选择逻辑：
   - **1帧时**：显示视频中间的关键帧
   - **2帧时**：显示开头和结尾，了解视频起止
   - **多帧时**：从开头到结尾均匀分布，完整覆盖视频时间线

### 技术要求
- OpenCV (cv2)
- PySide6
- FFmpeg（可选，用于更好的视频信息获取）

## 安装和运行

```bash
pip install -r requirements.txt
python main.py
```

## 配置
在 `core/config.py` 中可以调整视频处理相关设置。
