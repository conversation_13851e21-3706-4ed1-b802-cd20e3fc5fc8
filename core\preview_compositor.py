"""
预览合成器 - 负责合成轨道上的视频、音频和效果，生成预览内容
"""

import cv2
import numpy as np
import os
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from PySide6.QtCore import QObject, Signal
import json

class PreviewCompositor(QObject):
    """预览合成器"""
    
    frame_ready = Signal(np.ndarray)  # 合成帧准备就绪
    audio_ready = Signal(np.ndarray)  # 合成音频准备就绪
    
    def __init__(self):
        super().__init__()
        self.video_caps = {}  # 缓存视频捕获对象
        self.current_frame_cache = {}  # 当前帧缓存
        self.default_resolution = (1920, 1080)
        # 🔧 新增：预览设置
        self.preview_quality = 'original'  # 'original', 'high', 'smooth'
        self.preview_aspect_ratio = '9:16'  # 默认9:16
        self.aspect_ratios = {
            '16:9': (1920, 1080),
            '4:3': (1440, 1080),
            '9:16': (1080, 1920),
            '1:1': (1080, 1080),
            '21:9': (2560, 1080)
        }
        
    def compose_frame_at_position(self, tracks: List[Dict], position: float) -> Optional[np.ndarray]:
        """在指定位置合成帧"""
        try:
            # 🔧 修改：根据预览比例创建画布
            canvas_resolution = self.aspect_ratios.get(self.preview_aspect_ratio, self.default_resolution)
            canvas = np.zeros((canvas_resolution[1], canvas_resolution[0], 3), dtype=np.uint8)
            
            # 按轨道顺序合成（从下到上）
            for track_index, track in enumerate(tracks):
                if not track.get('enabled', True):
                    continue
                    
                track_type = track.get('type', 'video')
                if track_type != 'video':
                    continue  # 暂时只处理视频轨道
                    
                # 查找当前位置的媒体项
                media_item = self.get_media_at_position(track, position)
                if not media_item:
                    continue
                    
                # 获取媒体帧
                frame = self.get_media_frame(media_item, position)
                if frame is not None:
                    # 🔧 新增：根据预览质量调整帧分辨率
                    frame = self.apply_quality_scaling(frame)

                    # 应用效果
                    frame = self.apply_effects(frame, media_item)

                    # 合成到画布上
                    canvas = self.composite_frame(canvas, frame, media_item, track_index)
            
            return canvas
            
        except Exception as e:
            print(f"❌ 合成帧失败: {e}")
            return None
    
    def get_media_at_position(self, track: Dict, position: float) -> Optional[Dict]:
        """获取指定位置的媒体项"""
        for media_item in track.get('media_files', []):
            if not isinstance(media_item, dict):
                continue
                
            start_time = media_item.get('start_time', 0.0)
            duration = media_item.get('duration', 0.0)
            end_time = start_time + duration
            
            if start_time <= position < end_time:
                return media_item
                
        return None
    
    def get_media_frame(self, media_item: Dict, timeline_position: float) -> Optional[np.ndarray]:
        """获取媒体项在指定时间的帧"""
        try:
            file_path = media_item.get('file_path', '')
            
            # 检查是否是占位符
            if file_path.startswith('placeholder_') or not os.path.exists(file_path):
                return self.create_placeholder_frame(media_item)
            
            # 计算在媒体文件内的相对位置
            start_time = media_item.get('start_time', 0.0)
            relative_position = timeline_position - start_time
            trim_start = media_item.get('trim_start', 0.0)
            actual_position = trim_start + relative_position
            
            # 获取或创建视频捕获对象
            if file_path not in self.video_caps:
                cap = cv2.VideoCapture(file_path)
                if not cap.isOpened():
                    print(f"❌ 无法打开视频文件: {file_path}")
                    return None
                self.video_caps[file_path] = cap
            else:
                cap = self.video_caps[file_path]
            
            # 跳转到指定位置
            fps = cap.get(cv2.CAP_PROP_FPS) or 30.0
            frame_number = int(actual_position * fps)
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            
            # 读取帧
            ret, frame = cap.read()
            if ret:
                return frame
            else:
                print(f"❌ 无法读取帧: {file_path} at {actual_position:.2f}s")
                return None
                
        except Exception as e:
            print(f"❌ 获取媒体帧失败: {e}")
            return None
    
    def create_placeholder_frame(self, media_item: Dict) -> np.ndarray:
        """创建占位符帧"""
        frame = np.zeros((self.default_resolution[1], self.default_resolution[0], 3), dtype=np.uint8)
        
        # 绘制占位符背景
        cv2.rectangle(frame, (0, 0), (self.default_resolution[0], self.default_resolution[1]), (64, 64, 64), -1)
        
        # 绘制占位符文本
        placeholder_text = media_item.get('placeholder_text', '占位符')
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 2
        color = (255, 255, 255)
        thickness = 3
        
        # 计算文本位置（居中）
        text_size = cv2.getTextSize(placeholder_text, font, font_scale, thickness)[0]
        text_x = (self.default_resolution[0] - text_size[0]) // 2
        text_y = (self.default_resolution[1] + text_size[1]) // 2
        
        cv2.putText(frame, placeholder_text, (text_x, text_y), font, font_scale, color, thickness)
        
        return frame
    
    def apply_effects(self, frame: np.ndarray, media_item: Dict) -> np.ndarray:
        """应用效果到帧"""
        try:
            effects = media_item.get('effects', [])
            
            for effect in effects:
                effect_type = effect.get('type', '')
                
                if effect_type == 'brightness':
                    frame = self.apply_brightness(frame, effect.get('value', 0))
                elif effect_type == 'contrast':
                    frame = self.apply_contrast(frame, effect.get('value', 1.0))
                elif effect_type == 'saturation':
                    frame = self.apply_saturation(frame, effect.get('value', 1.0))
                elif effect_type == 'blur':
                    frame = self.apply_blur(frame, effect.get('value', 0))
                elif effect_type == 'crop':
                    frame = self.apply_crop(frame, effect.get('params', {}))
                elif effect_type == 'scale':
                    frame = self.apply_scale(frame, effect.get('params', {}))
                elif effect_type == 'rotate':
                    frame = self.apply_rotate(frame, effect.get('value', 0))
                # 可以继续添加更多效果
            
            return frame
            
        except Exception as e:
            print(f"❌ 应用效果失败: {e}")
            return frame
    
    def apply_brightness(self, frame: np.ndarray, brightness: float) -> np.ndarray:
        """应用亮度调整"""
        return cv2.convertScaleAbs(frame, alpha=1.0, beta=brightness)
    
    def apply_contrast(self, frame: np.ndarray, contrast: float) -> np.ndarray:
        """应用对比度调整"""
        return cv2.convertScaleAbs(frame, alpha=contrast, beta=0)
    
    def apply_saturation(self, frame: np.ndarray, saturation: float) -> np.ndarray:
        """应用饱和度调整"""
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        hsv[:, :, 1] = cv2.multiply(hsv[:, :, 1], saturation)
        return cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)
    
    def apply_blur(self, frame: np.ndarray, blur_amount: float) -> np.ndarray:
        """应用模糊效果"""
        if blur_amount > 0:
            kernel_size = int(blur_amount * 10) * 2 + 1  # 确保是奇数
            return cv2.GaussianBlur(frame, (kernel_size, kernel_size), 0)
        return frame
    
    def apply_crop(self, frame: np.ndarray, crop_params: Dict) -> np.ndarray:
        """应用裁剪"""
        x = crop_params.get('x', 0)
        y = crop_params.get('y', 0)
        width = crop_params.get('width', frame.shape[1])
        height = crop_params.get('height', frame.shape[0])
        
        return frame[y:y+height, x:x+width]
    
    def apply_scale(self, frame: np.ndarray, scale_params: Dict) -> np.ndarray:
        """应用缩放"""
        scale_x = scale_params.get('scale_x', 1.0)
        scale_y = scale_params.get('scale_y', 1.0)
        
        height, width = frame.shape[:2]
        new_width = int(width * scale_x)
        new_height = int(height * scale_y)
        
        return cv2.resize(frame, (new_width, new_height))
    
    def apply_rotate(self, frame: np.ndarray, angle: float) -> np.ndarray:
        """应用旋转"""
        if angle == 0:
            return frame
            
        height, width = frame.shape[:2]
        center = (width // 2, height // 2)
        
        rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
        return cv2.warpAffine(frame, rotation_matrix, (width, height))
    
    def composite_frame(self, canvas: np.ndarray, frame: np.ndarray, media_item: Dict, track_index: int) -> np.ndarray:
        """将帧合成到画布上"""
        try:
            # 获取合成参数
            opacity = media_item.get('opacity', 1.0)
            blend_mode = media_item.get('blend_mode', 'normal')

            # 🔧 修改：根据预览比例裁剪帧，而不是简单缩放
            frame = self.crop_frame_to_aspect_ratio(frame)

            # 调整帧大小以匹配画布
            if frame.shape[:2] != canvas.shape[:2]:
                frame = cv2.resize(frame, (canvas.shape[1], canvas.shape[0]))

            # 应用透明度
            if opacity < 1.0:
                frame = cv2.addWeighted(canvas, 1.0 - opacity, frame, opacity, 0)

            # 应用混合模式
            if blend_mode == 'normal':
                return frame
            elif blend_mode == 'multiply':
                return cv2.multiply(canvas.astype(np.float32) / 255.0, frame.astype(np.float32) / 255.0) * 255.0
            elif blend_mode == 'screen':
                return (1.0 - (1.0 - canvas.astype(np.float32) / 255.0) * (1.0 - frame.astype(np.float32) / 255.0)) * 255.0
            elif blend_mode == 'overlay':
                return cv2.addWeighted(canvas, 0.5, frame, 0.5, 0)
            else:
                return frame

        except Exception as e:
            print(f"❌ 合成帧失败: {e}")
            return canvas

    def crop_frame_to_aspect_ratio(self, frame: np.ndarray) -> np.ndarray:
        """根据预览比例裁剪帧"""
        try:
            height, width = frame.shape[:2]

            if self.preview_aspect_ratio not in self.aspect_ratios:
                return frame

            target_w, target_h = self.aspect_ratios[self.preview_aspect_ratio][:2]
            target_ratio = target_w / target_h
            current_ratio = width / height

            if abs(current_ratio - target_ratio) < 0.01:
                # 比例已经接近，不需要裁剪
                return frame

            if current_ratio > target_ratio:
                # 当前视频更宽，需要裁剪宽度
                new_width = int(height * target_ratio)
                x_offset = (width - new_width) // 2
                cropped_frame = frame[:, x_offset:x_offset + new_width]
            else:
                # 当前视频更高，需要裁剪高度
                new_height = int(width / target_ratio)
                y_offset = (height - new_height) // 2
                cropped_frame = frame[y_offset:y_offset + new_height, :]

            return cropped_frame

        except Exception as e:
            print(f"❌ 比例裁剪失败: {e}")
            return frame
    
    def cleanup(self):
        """清理资源"""
        for cap in self.video_caps.values():
            if cap:
                cap.release()
        self.video_caps.clear()
        self.current_frame_cache.clear()
    
    def apply_quality_scaling(self, frame: np.ndarray) -> np.ndarray:
        """根据预览质量缩放帧"""
        try:
            if self.preview_quality == 'smooth':
                # 流畅模式：缩放到50%
                height, width = frame.shape[:2]
                new_width = int(width * 0.5)
                new_height = int(height * 0.5)
                return cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_LINEAR)
            elif self.preview_quality == 'high':
                # 高清模式：缩放到75%
                height, width = frame.shape[:2]
                new_width = int(width * 0.75)
                new_height = int(height * 0.75)
                return cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            else:
                # 原画模式：保持原始尺寸
                return frame
        except Exception as e:
            print(f"❌ 质量缩放失败: {e}")
            return frame

    def set_preview_quality(self, quality: str):
        """设置预览质量"""
        if quality in ['original', 'high', 'smooth']:
            self.preview_quality = quality
            print(f"🎬 预览质量已设置为: {quality}")

    def set_preview_aspect_ratio(self, aspect_ratio: str):
        """设置预览比例"""
        if aspect_ratio in self.aspect_ratios:
            self.preview_aspect_ratio = aspect_ratio
            print(f"📐 预览比例已设置为: {aspect_ratio}")

    def get_current_resolution(self):
        """获取当前预览分辨率"""
        return self.aspect_ratios.get(self.preview_aspect_ratio, self.default_resolution)

    def set_resolution(self, width: int, height: int):
        """设置输出分辨率"""
        self.default_resolution = (width, height)

    def get_audio_at_position(self, tracks: List[Dict], position: float) -> Optional[np.ndarray]:
        """获取指定位置的音频数据（预留接口）"""
        # TODO: 实现音频合成
        return None
