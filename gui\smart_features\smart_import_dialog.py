#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能导入对话框 - 智能识别和分组客户视频文件
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                               QPushButton, QTableWidget, QTableWidgetItem,
                               QHeaderView, QGroupBox, QCheckBox, QComboBox,
                               QLineEdit, QTextEdit, QProgressBar, QMessageBox,
                               QSplitter, QFrame, QScrollArea, QWidget,
                               QGridLayout, QButtonGroup, QRadioButton)
from PySide6.QtCore import Qt, QThread, Signal, QTimer
from PySide6.QtGui import QFont, QIcon, QPixmap

import re
import os
from pathlib import Path
from typing import List, Dict, Any

from core.common.logger import get_logger


class VideoGroupingThread(QThread):
    """视频分组线程"""
    
    progress_updated = Signal(int)  # 进度更新
    grouping_completed = Signal(dict)  # 分组完成
    
    def __init__(self, video_files):
        super().__init__()
        self.video_files = video_files
        self.logger = get_logger('video_grouping')
    
    def run(self):
        """执行视频分组"""
        try:
            grouped_videos = self.smart_group_videos(self.video_files)
            self.grouping_completed.emit(grouped_videos)
        except Exception as e:
            self.logger.error(f"Video grouping failed: {str(e)}")
            self.grouping_completed.emit({})
    
    def smart_group_videos(self, video_files):
        """智能分组视频文件"""
        groups = {}
        total_files = len(video_files)
        
        for i, video_file in enumerate(video_files):
            # 更新进度
            progress = int((i + 1) / total_files * 100)
            self.progress_updated.emit(progress)
            
            # 提取客户名称
            customer_name = self.extract_customer_name(video_file['name'])
            
            # 识别视频类型
            video_type = self.identify_video_type(video_file['name'])
            
            # 分组
            if customer_name not in groups:
                groups[customer_name] = {
                    'customer_name': customer_name,
                    'videos': {},
                    'folder': video_file['folder'],
                    'total_size': 0
                }
            
            groups[customer_name]['videos'][video_type] = video_file
            groups[customer_name]['total_size'] += video_file['size']
        
        return groups
    
    def extract_customer_name(self, filename):
        """从文件名提取客户名称"""
        # 移除扩展名
        name_without_ext = Path(filename).stem
        
        # 常见的分隔符和关键词
        separators = ['_', '-', ' ', '．', '。']
        keywords = ['before', 'after', 'process', '前', '后', '过程', '中', 
                   'during', 'final', '最终', '完成', '改造']
        
        # 尝试不同的提取策略
        for sep in separators:
            parts = name_without_ext.split(sep)
            if len(parts) >= 2:
                # 检查第一部分是否是客户名
                first_part = parts[0].strip()
                if first_part and not any(keyword in first_part.lower() for keyword in keywords):
                    return first_part
        
        # 如果无法提取，使用文件名的前半部分
        if len(name_without_ext) > 10:
            return name_without_ext[:10]
        
        return name_without_ext
    
    def identify_video_type(self, filename):
        """识别视频类型"""
        filename_lower = filename.lower()
        
        # 定义关键词映射
        type_keywords = {
            'before': ['before', '前', '改造前', 'pre', 'original'],
            'process': ['process', '过程', '中', 'during', 'cutting', 'styling'],
            'after': ['after', '后', '改造后', 'post', 'final', '最终', '完成']
        }
        
        # 检查文件名中的关键词
        for video_type, keywords in type_keywords.items():
            for keyword in keywords:
                if keyword in filename_lower:
                    return video_type
        
        # 如果无法识别，根据文件名的位置推测
        if 'before' in filename_lower or '前' in filename_lower:
            return 'before'
        elif 'after' in filename_lower or '后' in filename_lower:
            return 'after'
        else:
            return 'process'  # 默认为过程视频


class SmartImportDialog(QDialog):
    """智能导入对话框"""
    
    def __init__(self, video_files: List[Dict], parent=None):
        super().__init__(parent)
        self.video_files = video_files
        self.grouped_videos = {}
        self.selected_batches = []
        self.logger = get_logger('smart_import_dialog')
        
        self.setWindowTitle("📁 智能视频导入")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)
        
        self.init_ui()
        self.start_grouping()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("🧠 智能视频文件分析和分组")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(True)
        layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("正在分析视频文件...")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.status_label)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：分组结果
        left_widget = self.create_grouping_panel()
        splitter.addWidget(left_widget)
        
        # 右侧：批次配置
        right_widget = self.create_batch_config_panel()
        splitter.addWidget(right_widget)
        
        # 设置分割比例
        splitter.setStretchFactor(0, 2)
        splitter.setStretchFactor(1, 1)
        
        layout.addWidget(splitter)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.select_all_button = QPushButton("✅ 全选")
        self.select_all_button.clicked.connect(self.select_all_groups)
        self.select_all_button.setEnabled(False)
        button_layout.addWidget(self.select_all_button)
        
        self.clear_selection_button = QPushButton("❌ 清空选择")
        self.clear_selection_button.clicked.connect(self.clear_selection)
        self.clear_selection_button.setEnabled(False)
        button_layout.addWidget(self.clear_selection_button)
        
        button_layout.addStretch()
        
        self.import_button = QPushButton("🚀 导入到智能批量处理")
        self.import_button.clicked.connect(self.accept)
        self.import_button.setEnabled(False)
        button_layout.addWidget(self.import_button)
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
    
    def create_grouping_panel(self):
        """创建分组面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 分组结果标题
        group_title = QLabel("📊 智能分组结果")
        group_title.setStyleSheet("font-weight: bold; font-size: 14px; margin: 10px;")
        layout.addWidget(group_title)
        
        # 分组表格
        self.group_table = QTableWidget()
        self.group_table.setColumnCount(6)
        self.group_table.setHorizontalHeaderLabels([
            "选择", "客户名称", "改造前", "过程", "改造后", "文件大小"
        ])
        
        # 设置表格属性
        header = self.group_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        
        self.group_table.setAlternatingRowColors(True)
        self.group_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        layout.addWidget(self.group_table)
        
        return widget
    
    def create_batch_config_panel(self):
        """创建批次配置面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 配置标题
        config_title = QLabel("⚙️ 批次配置")
        config_title.setStyleSheet("font-weight: bold; font-size: 14px; margin: 10px;")
        layout.addWidget(config_title)
        
        # 全局设置
        global_group = QGroupBox("🌐 全局设置")
        global_layout = QGridLayout(global_group)
        
        global_layout.addWidget(QLabel("默认水印前缀:"), 0, 0)
        self.watermark_prefix_edit = QLineEdit("高端理发店")
        global_layout.addWidget(self.watermark_prefix_edit, 0, 1)
        
        global_layout.addWidget(QLabel("音乐音量:"), 1, 0)
        self.music_volume_combo = QComboBox()
        self.music_volume_combo.addItems(["0.5", "0.6", "0.7", "0.8", "0.9"])
        self.music_volume_combo.setCurrentText("0.7")
        global_layout.addWidget(self.music_volume_combo, 1, 1)
        
        layout.addWidget(global_group)
        
        # 命名规则
        naming_group = QGroupBox("📝 命名规则")
        naming_layout = QVBoxLayout(naming_group)
        
        self.naming_pattern_combo = QComboBox()
        self.naming_pattern_combo.addItems([
            "{客户名}_理发变身.mp4",
            "{客户名}_{日期}_作品.mp4",
            "{客户名}_专属视频.mp4",
            "自定义..."
        ])
        naming_layout.addWidget(self.naming_pattern_combo)
        
        self.custom_naming_edit = QLineEdit()
        self.custom_naming_edit.setPlaceholderText("自定义命名格式...")
        self.custom_naming_edit.setVisible(False)
        naming_layout.addWidget(self.custom_naming_edit)
        
        # 连接信号
        self.naming_pattern_combo.currentTextChanged.connect(self.on_naming_pattern_changed)
        
        layout.addWidget(naming_group)
        
        # 质量设置
        quality_group = QGroupBox("🎬 质量设置")
        quality_layout = QVBoxLayout(quality_group)
        
        self.quality_combo = QComboBox()
        self.quality_combo.addItems([
            "高质量 (较慢)",
            "标准质量 (推荐)",
            "快速处理 (较快)"
        ])
        self.quality_combo.setCurrentIndex(1)
        quality_layout.addWidget(self.quality_combo)
        
        layout.addWidget(quality_group)
        
        # 预览信息
        preview_group = QGroupBox("👁️ 预览信息")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_text = QTextEdit()
        self.preview_text.setMaximumHeight(150)
        self.preview_text.setReadOnly(True)
        self.preview_text.setPlaceholderText("选择分组后显示预览信息...")
        preview_layout.addWidget(self.preview_text)
        
        layout.addWidget(preview_group)
        
        layout.addStretch()
        
        return widget
    
    def start_grouping(self):
        """开始视频分组"""
        self.grouping_thread = VideoGroupingThread(self.video_files)
        self.grouping_thread.progress_updated.connect(self.on_progress_updated)
        self.grouping_thread.grouping_completed.connect(self.on_grouping_completed)
        self.grouping_thread.start()
    
    def on_progress_updated(self, progress):
        """进度更新"""
        self.progress_bar.setValue(progress)
        self.status_label.setText(f"正在分析视频文件... {progress}%")
    
    def on_grouping_completed(self, grouped_videos):
        """分组完成"""
        self.grouped_videos = grouped_videos
        
        # 隐藏进度条
        self.progress_bar.setVisible(False)
        self.status_label.setText(f"✅ 分析完成！找到 {len(grouped_videos)} 个客户分组")
        
        # 填充表格
        self.populate_group_table()
        
        # 启用按钮
        self.select_all_button.setEnabled(True)
        self.clear_selection_button.setEnabled(True)
        self.import_button.setEnabled(True)
    
    def populate_group_table(self):
        """填充分组表格"""
        self.group_table.setRowCount(len(self.grouped_videos))
        
        for row, (customer_name, group_data) in enumerate(self.grouped_videos.items()):
            # 选择复选框
            checkbox = QCheckBox()
            checkbox.setChecked(True)  # 默认选中
            checkbox.stateChanged.connect(self.on_selection_changed)
            self.group_table.setCellWidget(row, 0, checkbox)
            
            # 客户名称
            self.group_table.setItem(row, 1, QTableWidgetItem(customer_name))
            
            # 视频文件状态
            videos = group_data['videos']
            self.group_table.setItem(row, 2, QTableWidgetItem(
                "✅" if 'before' in videos else "❌"
            ))
            self.group_table.setItem(row, 3, QTableWidgetItem(
                "✅" if 'process' in videos else "❌"
            ))
            self.group_table.setItem(row, 4, QTableWidgetItem(
                "✅" if 'after' in videos else "❌"
            ))
            
            # 文件大小
            total_size = group_data['total_size']
            size_mb = total_size / (1024 * 1024)
            self.group_table.setItem(row, 5, QTableWidgetItem(f"{size_mb:.1f} MB"))
        
        # 更新预览
        self.update_preview()
    
    def on_selection_changed(self):
        """选择变化处理"""
        self.update_preview()
    
    def update_preview(self):
        """更新预览信息"""
        selected_count = 0
        total_size = 0
        complete_groups = 0
        
        for row in range(self.group_table.rowCount()):
            checkbox = self.group_table.cellWidget(row, 0)
            if checkbox.isChecked():
                selected_count += 1
                
                # 获取分组数据
                customer_name = self.group_table.item(row, 1).text()
                group_data = self.grouped_videos[customer_name]
                total_size += group_data['total_size']
                
                # 检查是否完整
                videos = group_data['videos']
                if all(vtype in videos for vtype in ['before', 'process', 'after']):
                    complete_groups += 1
        
        # 更新预览文本
        preview_text = f"""
📊 选择统计:
• 已选择分组: {selected_count}
• 完整分组: {complete_groups}
• 总文件大小: {total_size / (1024 * 1024):.1f} MB

🎬 将生成视频:
• 预计输出: {complete_groups} 个客户视频
• 命名格式: {self.naming_pattern_combo.currentText()}
• 水印前缀: {self.watermark_prefix_edit.text()}
        """
        
        self.preview_text.setText(preview_text.strip())
    
    def select_all_groups(self):
        """全选分组"""
        for row in range(self.group_table.rowCount()):
            checkbox = self.group_table.cellWidget(row, 0)
            checkbox.setChecked(True)
    
    def clear_selection(self):
        """清空选择"""
        for row in range(self.group_table.rowCount()):
            checkbox = self.group_table.cellWidget(row, 0)
            checkbox.setChecked(False)
    
    def on_naming_pattern_changed(self, pattern):
        """命名模式变化"""
        if pattern == "自定义...":
            self.custom_naming_edit.setVisible(True)
        else:
            self.custom_naming_edit.setVisible(False)
        
        self.update_preview()
    
    def get_selected_batches(self):
        """获取选中的批次数据"""
        selected_batches = []
        
        for row in range(self.group_table.rowCount()):
            checkbox = self.group_table.cellWidget(row, 0)
            if checkbox.isChecked():
                customer_name = self.group_table.item(row, 1).text()
                group_data = self.grouped_videos[customer_name]
                
                # 检查是否有足够的视频文件
                videos = group_data['videos']
                if len(videos) >= 2:  # 至少需要2个视频
                    # 构建批次数据
                    batch_data = {
                        'batch_name': customer_name,
                        'output_name': self.get_output_name(customer_name),
                        'videos': {
                            vtype: video_info['path'] 
                            for vtype, video_info in videos.items()
                        },
                        'custom_settings': {
                            'watermark_text': f"{customer_name} - {self.watermark_prefix_edit.text()}",
                            'music_volume': float(self.music_volume_combo.currentText())
                        }
                    }
                    
                    selected_batches.append(batch_data)
        
        return selected_batches
    
    def get_output_name(self, customer_name):
        """获取输出文件名"""
        pattern = self.naming_pattern_combo.currentText()
        
        if pattern == "自定义...":
            pattern = self.custom_naming_edit.text()
        
        # 替换占位符
        from datetime import datetime
        output_name = pattern.replace("{客户名}", customer_name)
        output_name = output_name.replace("{日期}", datetime.now().strftime("%Y%m%d"))
        
        return output_name
