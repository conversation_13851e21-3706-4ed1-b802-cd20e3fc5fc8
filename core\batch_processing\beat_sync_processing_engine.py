#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
踩点转场全流程处理引擎
整合节拍分析、素材适配、转场选择、美颜渲染的完整处理流程
"""

import cv2
import numpy as np
from typing import Dict, List, Any, Tuple, Optional, Callable
from dataclasses import dataclass
from pathlib import Path
import json
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from core.logger import get_logger

# 导入核心模块
from core.beat_detection_engine import HighPrecisionBeatDetector, BeatAnalysisResult, BeatPoint
from core.material_adaptation_engine import MaterialAdaptationEngine, VideoSegment, AdaptationPlan
from core.intelligent_transition_matrix import IntelligentTransitionMatrix, TransitionPlan, TransitionConfig, BeautyFilterConfig

try:
    from moviepy.editor import VideoFileClip, AudioFileClip, CompositeVideoClip, concatenate_videoclips
    MOVIEPY_AVAILABLE = True
except ImportError:
    MOVIEPY_AVAILABLE = False
    print("⚠️ moviepy未安装，视频合成功能将受限")


@dataclass
class ProcessingConfig:
    """处理配置"""
    target_accuracy_ms: float = 50.0  # 目标精度（毫秒）
    enable_beauty_filters: bool = True  # 启用美颜滤镜
    enable_smart_transitions: bool = True  # 启用智能转场
    output_resolution: Tuple[int, int] = (1920, 1080)  # 输出分辨率
    output_fps: int = 30  # 输出帧率
    quality_preset: str = "high"  # 质量预设: low, medium, high, ultra
    parallel_processing: bool = True  # 并行处理
    max_workers: int = 4  # 最大工作线程数


@dataclass
class ProcessingResult:
    """处理结果"""
    success: bool
    output_path: str
    processing_time: float
    beat_analysis: Optional[BeatAnalysisResult]
    adaptation_plan: Optional[AdaptationPlan]
    transition_plans: List[TransitionPlan]
    quality_metrics: Dict[str, float]
    error_message: Optional[str] = None


class BeatSyncProcessingEngine:
    """踩点转场全流程处理引擎"""
    
    def __init__(self, config: ProcessingConfig = None):
        """
        初始化处理引擎
        
        Args:
            config: 处理配置
        """
        self.config = config or ProcessingConfig()
        self.logger = get_logger('beat_sync_engine')
        
        # 初始化核心组件
        self.beat_detector = HighPrecisionBeatDetector(self.config.target_accuracy_ms)
        self.material_adapter = MaterialAdaptationEngine()
        self.transition_matrix = IntelligentTransitionMatrix()
        
        # 处理状态
        self.is_processing = False
        self.current_progress = 0.0
        self.progress_callback: Optional[Callable[[float, str], None]] = None
        
        # 缓存和临时文件
        self._temp_files = []
        self._processing_cache = {}
        
        self.logger.info("踩点转场处理引擎初始化完成")
    
    def set_progress_callback(self, callback: Callable[[float, str], None]):
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def _update_progress(self, progress: float, message: str):
        """更新处理进度"""
        self.current_progress = progress
        if self.progress_callback:
            self.progress_callback(progress, message)
        self.logger.info(f"处理进度: {progress:.1%} - {message}")
    
    def process_video_with_music(self, video_path: str, music_path: str, 
                                output_path: str) -> ProcessingResult:
        """
        处理视频与音乐的踩点同步
        
        Args:
            video_path: 视频文件路径
            music_path: 音乐文件路径
            output_path: 输出文件路径
            
        Returns:
            处理结果
        """
        start_time = time.time()
        self.is_processing = True
        
        try:
            self.logger.info(f"开始踩点转场处理: {Path(video_path).name} + {Path(music_path).name}")
            
            # 阶段1: 音乐节拍分析 (0-20%)
            self._update_progress(0.0, "开始音乐节拍分析...")
            beat_analysis = self.beat_detector.analyze_music(music_path)
            self._update_progress(0.2, f"节拍分析完成: BPM={beat_analysis.bpm:.1f}")
            
            # 阶段2: 视频素材分析 (20-40%)
            self._update_progress(0.2, "开始视频素材分析...")
            video_segments = self.material_adapter.analyze_video_material(video_path)
            self._update_progress(0.4, f"素材分析完成: {len(video_segments)}个片段")
            
            # 阶段3: 素材动态适配 (40-50%)
            self._update_progress(0.4, "创建素材适配计划...")
            adaptation_plan = self.material_adapter.create_adaptation_plan(video_segments, beat_analysis)
            self._update_progress(0.5, f"适配计划完成: 适配比例={adaptation_plan.adaptation_ratio:.2f}")
            
            # 阶段4: 智能转场规划 (50-60%)
            self._update_progress(0.5, "生成智能转场计划...")
            transition_plans = self.transition_matrix.generate_transition_plan(beat_analysis, video_segments)
            self._update_progress(0.6, f"转场规划完成: {len(transition_plans)}个转场")
            
            # 阶段5: 视频渲染合成 (60-95%)
            self._update_progress(0.6, "开始视频渲染合成...")
            success = self._render_final_video(
                video_path, music_path, output_path,
                beat_analysis, adaptation_plan, transition_plans
            )
            self._update_progress(0.95, "视频渲染完成")
            
            # 阶段6: 质量评估 (95-100%)
            self._update_progress(0.95, "进行质量评估...")
            quality_metrics = self._assess_output_quality(output_path, beat_analysis)
            self._update_progress(1.0, "处理完成")
            
            processing_time = time.time() - start_time
            
            result = ProcessingResult(
                success=success,
                output_path=output_path if success else "",
                processing_time=processing_time,
                beat_analysis=beat_analysis,
                adaptation_plan=adaptation_plan,
                transition_plans=transition_plans,
                quality_metrics=quality_metrics
            )
            
            self.logger.info(f"踩点转场处理完成: 用时={processing_time:.2f}s, 成功={success}")
            return result
            
        except Exception as e:
            self.logger.error(f"踩点转场处理失败: {e}")
            processing_time = time.time() - start_time
            
            return ProcessingResult(
                success=False,
                output_path="",
                processing_time=processing_time,
                beat_analysis=None,
                adaptation_plan=None,
                transition_plans=[],
                quality_metrics={},
                error_message=str(e)
            )
        
        finally:
            self.is_processing = False
            self._cleanup_temp_files()
    
    def _render_final_video(self, video_path: str, music_path: str, output_path: str,
                           beat_analysis: BeatAnalysisResult, adaptation_plan: AdaptationPlan,
                           transition_plans: List[TransitionPlan]) -> bool:
        """渲染最终视频"""
        try:
            if not MOVIEPY_AVAILABLE:
                self.logger.error("moviepy不可用，无法进行视频合成")
                return False
            
            # 加载原始视频和音频
            video_clip = VideoFileClip(video_path)
            audio_clip = AudioFileClip(music_path)
            
            # 创建视频片段列表
            video_segments = []
            
            # 处理每个节拍段
            for i, beat_segment in enumerate(adaptation_plan.beat_segments):
                progress = 0.6 + (i / len(adaptation_plan.beat_segments)) * 0.3
                self._update_progress(progress, f"处理节拍段 {i+1}/{len(adaptation_plan.beat_segments)}")
                
                # 提取源片段
                source_segment = beat_segment['source_segment']
                start_time = source_segment.start_time
                duration = beat_segment['duration']
                
                # 创建视频片段
                segment_clip = video_clip.subclip(start_time, start_time + min(duration, source_segment.duration))
                
                # 应用适配策略
                segment_clip = self._apply_adaptation_strategy(segment_clip, beat_segment)
                
                # 应用美颜滤镜
                if self.config.enable_beauty_filters and i < len(transition_plans):
                    segment_clip = self._apply_beauty_filters(segment_clip, transition_plans[i].beauty_filters)
                
                # 应用转场效果
                if self.config.enable_smart_transitions and i < len(transition_plans):
                    segment_clip = self._apply_transition_effect(segment_clip, transition_plans[i].transition_config)
                
                video_segments.append(segment_clip)
            
            # 合并视频片段
            if video_segments:
                final_video = concatenate_videoclips(video_segments, method="compose")
                
                # 设置音频
                final_video = final_video.set_audio(audio_clip.subclip(0, final_video.duration))
                
                # 设置输出参数
                final_video = final_video.resize(self.config.output_resolution)
                
                # 导出视频
                codec = self._get_codec_settings()
                final_video.write_videofile(
                    output_path,
                    fps=self.config.output_fps,
                    codec=codec['video_codec'],
                    audio_codec=codec['audio_codec'],
                    bitrate=codec['bitrate'],
                    verbose=False,
                    logger=None
                )
                
                # 清理资源
                final_video.close()
                for segment in video_segments:
                    segment.close()
            
            video_clip.close()
            audio_clip.close()
            
            return True
            
        except Exception as e:
            self.logger.error(f"视频渲染失败: {e}")
            return False
    
    def _apply_adaptation_strategy(self, clip, beat_segment: Dict[str, Any]):
        """应用适配策略"""
        try:
            strategy = beat_segment.get('adaptation_strategy', 'NORMAL')
            
            if strategy == "EXTEND":
                # 延长片段：降低播放速度
                speed_factor = beat_segment.get('speed_factor', 0.8)
                return clip.fx(lambda c: c.speedx(speed_factor))
                
            elif strategy == "COMPRESS":
                # 压缩片段：提高播放速度
                speed_factor = beat_segment.get('speed_factor', 1.2)
                return clip.fx(lambda c: c.speedx(speed_factor))
                
            elif strategy == "SPLIT":
                # 拆分片段：使用指定的起始时间和时长
                split_start = beat_segment.get('split_start', 0.0)
                split_duration = beat_segment.get('split_duration', clip.duration)
                return clip.subclip(split_start, split_start + split_duration)
            
            return clip
            
        except Exception as e:
            self.logger.error(f"适配策略应用失败: {e}")
            return clip
    
    def _apply_beauty_filters(self, clip, beauty_filters: List[BeautyFilterConfig]):
        """应用美颜滤镜"""
        try:
            if not beauty_filters:
                return clip
            
            def beauty_filter_func(get_frame, t):
                frame = get_frame(t)
                
                # 应用每个美颜滤镜
                for filter_config in beauty_filters:
                    frame = self._apply_single_beauty_filter(frame, filter_config)
                
                return frame
            
            return clip.fl(beauty_filter_func)
            
        except Exception as e:
            self.logger.error(f"美颜滤镜应用失败: {e}")
            return clip
    
    def _apply_single_beauty_filter(self, frame: np.ndarray, filter_config: BeautyFilterConfig) -> np.ndarray:
        """应用单个美颜滤镜"""
        try:
            from core.salon_effects import SalonEffects
            
            # 这里可以调用SWANKSALON的美颜处理功能
            # 简化实现：基本的图像处理
            
            if filter_config.filter_type.value == "skin_smooth":
                # 磨皮处理
                kernel_size = int(15 * filter_config.intensity)
                if kernel_size % 2 == 0:
                    kernel_size += 1
                frame = cv2.GaussianBlur(frame, (kernel_size, kernel_size), 0)
                
            elif filter_config.filter_type.value == "glow_effect":
                # 光晕效果
                glow = cv2.GaussianBlur(frame, (21, 21), 0)
                frame = cv2.addWeighted(frame, 1.0, glow, filter_config.intensity * 0.3, 0)
            
            return frame
            
        except Exception as e:
            self.logger.error(f"单个美颜滤镜应用失败: {e}")
            return frame
    
    def _apply_transition_effect(self, clip, transition_config: TransitionConfig):
        """应用转场效果"""
        try:
            # 简化的转场实现
            # 在实际应用中，这里会调用更复杂的转场效果
            
            if transition_config.transition_type.value == "fade":
                return clip.fadein(transition_config.duration).fadeout(transition_config.duration)
            
            elif transition_config.transition_type.value == "zoom":
                zoom_factor = transition_config.parameters.get("zoom_factor", 1.2)
                
                def zoom_effect(get_frame, t):
                    frame = get_frame(t)
                    h, w = frame.shape[:2]
                    
                    # 计算缩放
                    progress = min(1.0, t / transition_config.duration)
                    current_zoom = 1.0 + (zoom_factor - 1.0) * progress
                    
                    # 应用缩放
                    new_h, new_w = int(h * current_zoom), int(w * current_zoom)
                    resized = cv2.resize(frame, (new_w, new_h))
                    
                    # 裁剪到原始尺寸
                    start_y = (new_h - h) // 2
                    start_x = (new_w - w) // 2
                    
                    if start_y >= 0 and start_x >= 0:
                        return resized[start_y:start_y+h, start_x:start_x+w]
                    else:
                        return cv2.resize(resized, (w, h))
                
                return clip.fl(zoom_effect)
            
            return clip
            
        except Exception as e:
            self.logger.error(f"转场效果应用失败: {e}")
            return clip
    
    def _get_codec_settings(self) -> Dict[str, str]:
        """获取编码设置"""
        quality_settings = {
            "low": {
                "video_codec": "libx264",
                "audio_codec": "aac",
                "bitrate": "1000k"
            },
            "medium": {
                "video_codec": "libx264",
                "audio_codec": "aac",
                "bitrate": "2000k"
            },
            "high": {
                "video_codec": "libx264",
                "audio_codec": "aac",
                "bitrate": "4000k"
            },
            "ultra": {
                "video_codec": "libx264",
                "audio_codec": "aac",
                "bitrate": "8000k"
            }
        }
        
        return quality_settings.get(self.config.quality_preset, quality_settings["high"])
    
    def _assess_output_quality(self, output_path: str, beat_analysis: BeatAnalysisResult) -> Dict[str, float]:
        """评估输出质量"""
        try:
            metrics = {}
            
            if Path(output_path).exists():
                # 文件大小
                file_size = Path(output_path).stat().st_size / (1024 * 1024)  # MB
                metrics["file_size_mb"] = file_size
                
                # 节拍同步精度（基于分析结果）
                metrics["beat_sync_accuracy"] = beat_analysis.analysis_accuracy
                
                # 节拍稳定性
                metrics["tempo_stability"] = beat_analysis.tempo_stability
                
                # 处理成功率
                metrics["processing_success_rate"] = 1.0
                
                # 综合质量评分
                quality_score = (
                    metrics["beat_sync_accuracy"] * 0.4 +
                    metrics["tempo_stability"] * 0.3 +
                    metrics["processing_success_rate"] * 0.3
                )
                metrics["overall_quality"] = quality_score
            else:
                metrics = {
                    "file_size_mb": 0.0,
                    "beat_sync_accuracy": 0.0,
                    "tempo_stability": 0.0,
                    "processing_success_rate": 0.0,
                    "overall_quality": 0.0
                }
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"质量评估失败: {e}")
            return {"overall_quality": 0.0}
    
    def _cleanup_temp_files(self):
        """清理临时文件"""
        try:
            for temp_file in self._temp_files:
                if Path(temp_file).exists():
                    Path(temp_file).unlink()
            self._temp_files.clear()
            
        except Exception as e:
            self.logger.error(f"清理临时文件失败: {e}")
    
    def batch_process(self, tasks: List[Dict[str, str]], output_dir: str) -> List[ProcessingResult]:
        """批量处理多个任务"""
        try:
            self.logger.info(f"开始批量处理: {len(tasks)}个任务")
            
            results = []
            
            if self.config.parallel_processing and len(tasks) > 1:
                # 并行处理
                with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
                    futures = []
                    
                    for i, task in enumerate(tasks):
                        output_path = Path(output_dir) / f"output_{i+1:03d}.mp4"
                        future = executor.submit(
                            self.process_video_with_music,
                            task["video_path"],
                            task["music_path"],
                            str(output_path)
                        )
                        futures.append(future)
                    
                    for future in as_completed(futures):
                        result = future.result()
                        results.append(result)
            else:
                # 串行处理
                for i, task in enumerate(tasks):
                    output_path = Path(output_dir) / f"output_{i+1:03d}.mp4"
                    result = self.process_video_with_music(
                        task["video_path"],
                        task["music_path"],
                        str(output_path)
                    )
                    results.append(result)
            
            success_count = sum(1 for r in results if r.success)
            self.logger.info(f"批量处理完成: {success_count}/{len(tasks)} 成功")
            
            return results
            
        except Exception as e:
            self.logger.error(f"批量处理失败: {e}")
            return []
    
    def export_processing_report(self, results: List[ProcessingResult], output_path: str) -> bool:
        """导出处理报告"""
        try:
            report = {
                "timestamp": time.time(),
                "total_tasks": len(results),
                "successful_tasks": sum(1 for r in results if r.success),
                "failed_tasks": sum(1 for r in results if not r.success),
                "average_processing_time": np.mean([r.processing_time for r in results]),
                "average_quality_score": np.mean([
                    r.quality_metrics.get("overall_quality", 0.0) for r in results if r.success
                ]),
                "results": [
                    {
                        "success": r.success,
                        "output_path": r.output_path,
                        "processing_time": r.processing_time,
                        "quality_metrics": r.quality_metrics,
                        "error_message": r.error_message
                    }
                    for r in results
                ]
            }
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=4, ensure_ascii=False)
            
            self.logger.info(f"处理报告已导出: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出处理报告失败: {e}")
            return False
