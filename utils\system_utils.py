#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统工具模块
"""

import os
import sys
import platform
import psutil
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Union


def get_system_info() -> Dict:
    """
    获取系统信息
    
    Returns:
        系统信息字典
    """
    return {
        'platform': platform.platform(),
        'system': platform.system(),
        'release': platform.release(),
        'version': platform.version(),
        'machine': platform.machine(),
        'processor': platform.processor(),
        'python_version': platform.python_version(),
        'python_implementation': platform.python_implementation(),
        'cpu_count': os.cpu_count(),
        'memory_total': psutil.virtual_memory().total,
        'memory_available': psutil.virtual_memory().available,
        'disk_usage': {
            'total': psutil.disk_usage('/').total if platform.system() != 'Windows' else psutil.disk_usage('C:').total,
            'free': psutil.disk_usage('/').free if platform.system() != 'Windows' else psutil.disk_usage('C:').free
        }
    }


def check_dependencies(dependencies: List[str]) -> Dict[str, bool]:
    """
    检查依赖是否已安装
    
    Args:
        dependencies: 依赖包列表
        
    Returns:
        依赖检查结果字典
    """
    results = {}
    
    for dep in dependencies:
        try:
            __import__(dep)
            results[dep] = True
        except ImportError:
            results[dep] = False
    
    return results


def check_executable(executable: str) -> bool:
    """
    检查可执行文件是否存在
    
    Args:
        executable: 可执行文件名
        
    Returns:
        是否存在
    """
    try:
        subprocess.run([executable, '--version'], 
                      capture_output=True, 
                      check=True, 
                      timeout=5)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
        return False


def get_memory_usage() -> Dict:
    """
    获取内存使用情况
    
    Returns:
        内存使用信息
    """
    memory = psutil.virtual_memory()
    
    return {
        'total': memory.total,
        'available': memory.available,
        'used': memory.used,
        'percentage': memory.percent,
        'free': memory.free
    }


def get_cpu_usage(interval: float = 1.0) -> float:
    """
    获取CPU使用率
    
    Args:
        interval: 采样间隔
        
    Returns:
        CPU使用率百分比
    """
    return psutil.cpu_percent(interval=interval)


def get_disk_usage(path: Union[str, Path] = None) -> Dict:
    """
    获取磁盘使用情况
    
    Args:
        path: 路径，默认为根目录
        
    Returns:
        磁盘使用信息
    """
    if path is None:
        path = '/' if platform.system() != 'Windows' else 'C:'
    
    usage = psutil.disk_usage(str(path))
    
    return {
        'total': usage.total,
        'used': usage.used,
        'free': usage.free,
        'percentage': (usage.used / usage.total) * 100
    }


def get_process_info(pid: Optional[int] = None) -> Dict:
    """
    获取进程信息
    
    Args:
        pid: 进程ID，默认为当前进程
        
    Returns:
        进程信息
    """
    if pid is None:
        process = psutil.Process()
    else:
        process = psutil.Process(pid)
    
    try:
        return {
            'pid': process.pid,
            'name': process.name(),
            'status': process.status(),
            'cpu_percent': process.cpu_percent(),
            'memory_info': process.memory_info()._asdict(),
            'memory_percent': process.memory_percent(),
            'create_time': process.create_time(),
            'num_threads': process.num_threads()
        }
    except psutil.NoSuchProcess:
        return {}


def kill_process_by_name(process_name: str) -> int:
    """
    根据进程名终止进程
    
    Args:
        process_name: 进程名
        
    Returns:
        终止的进程数量
    """
    killed_count = 0
    
    for process in psutil.process_iter(['pid', 'name']):
        try:
            if process.info['name'] == process_name:
                process.terminate()
                killed_count += 1
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    return killed_count


def get_network_info() -> Dict:
    """
    获取网络信息
    
    Returns:
        网络信息字典
    """
    network_stats = psutil.net_io_counters()
    
    return {
        'bytes_sent': network_stats.bytes_sent,
        'bytes_recv': network_stats.bytes_recv,
        'packets_sent': network_stats.packets_sent,
        'packets_recv': network_stats.packets_recv,
        'errin': network_stats.errin,
        'errout': network_stats.errout,
        'dropin': network_stats.dropin,
        'dropout': network_stats.dropout
    }


def create_temp_directory(prefix: str = 'temp_') -> Path:
    """
    创建临时目录
    
    Args:
        prefix: 目录名前缀
        
    Returns:
        临时目录路径
    """
    import tempfile
    temp_dir = Path(tempfile.mkdtemp(prefix=prefix))
    return temp_dir


def cleanup_temp_files(temp_dir: Union[str, Path], 
                      max_age_hours: int = 24) -> int:
    """
    清理临时文件
    
    Args:
        temp_dir: 临时目录路径
        max_age_hours: 最大保留时间（小时）
        
    Returns:
        清理的文件数量
    """
    import time
    
    temp_path = Path(temp_dir)
    if not temp_path.exists():
        return 0
    
    current_time = time.time()
    max_age_seconds = max_age_hours * 3600
    cleaned_count = 0
    
    for file_path in temp_path.rglob('*'):
        if file_path.is_file():
            try:
                file_age = current_time - file_path.stat().st_mtime
                if file_age > max_age_seconds:
                    file_path.unlink()
                    cleaned_count += 1
            except (OSError, FileNotFoundError):
                continue
    
    return cleaned_count


def get_environment_variables() -> Dict[str, str]:
    """
    获取环境变量
    
    Returns:
        环境变量字典
    """
    return dict(os.environ)


def set_environment_variable(name: str, value: str) -> bool:
    """
    设置环境变量
    
    Args:
        name: 变量名
        value: 变量值
        
    Returns:
        是否成功设置
    """
    try:
        os.environ[name] = value
        return True
    except Exception:
        return False


def run_command(command: List[str], 
               cwd: Optional[Union[str, Path]] = None,
               timeout: Optional[int] = None) -> Dict:
    """
    运行系统命令
    
    Args:
        command: 命令列表
        cwd: 工作目录
        timeout: 超时时间（秒）
        
    Returns:
        命令执行结果
    """
    try:
        result = subprocess.run(
            command,
            cwd=str(cwd) if cwd else None,
            capture_output=True,
            text=True,
            timeout=timeout,
            check=False
        )
        
        return {
            'returncode': result.returncode,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'success': result.returncode == 0
        }
    except subprocess.TimeoutExpired:
        return {
            'returncode': -1,
            'stdout': '',
            'stderr': 'Command timed out',
            'success': False
        }
    except Exception as e:
        return {
            'returncode': -1,
            'stdout': '',
            'stderr': str(e),
            'success': False
        }
