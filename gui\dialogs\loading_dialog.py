"""
加载进度对话框
"""

from PySide6.QtWidgets import QDialog, QVBoxLayout, QLabel, QProgressBar, QWidget
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import <PERSON>Font, QPainter, QColor, QPixmap

class LoadingDialog(QDialog):
    """加载进度对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("SWANKSALON")
        self.setFixedSize(400, 200)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建主容器
        main_container = QWidget()
        main_container.setStyleSheet("""
            QWidget {
                background-color: #1A1A1A;
                border-radius: 12px;
                border: 2px solid #333333;
            }
        """)
        container_layout = QVBoxLayout(main_container)
        container_layout.setContentsMargins(30, 30, 30, 30)
        container_layout.setSpacing(20)
        
        # Logo/标题
        title_label = QLabel("SWANKSALON")
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 24px;
                font-weight: bold;
                padding: 10px 0px;
            }
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        container_layout.addWidget(title_label)
        
        # 副标题
        subtitle_label = QLabel("专业视频编辑软件")
        subtitle_label.setStyleSheet("""
            QLabel {
                color: #CCCCCC;
                font-size: 14px;
                padding: 0px 0px 10px 0px;
            }
        """)
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        container_layout.addWidget(subtitle_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #333333;
                border-radius: 8px;
                background-color: #2A2A2A;
                text-align: center;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                height: 20px;
            }
            QProgressBar::chunk {
                background-color: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #00C896,
                    stop: 1 #00E8A8
                );
                border-radius: 6px;
                margin: 2px;
            }
        """)
        container_layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("正在启动...")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #CCCCCC;
                font-size: 12px;
                padding: 5px 0px;
            }
        """)
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        container_layout.addWidget(self.status_label)
        
        main_layout.addWidget(main_container)
        
    def update_progress(self, value: int, status: str = ""):
        """更新进度"""
        self.progress_bar.setValue(value)
        if status:
            self.status_label.setText(status)
            
    def set_status(self, status: str):
        """设置状态文本"""
        self.status_label.setText(status)
        
    def paintEvent(self, event):
        """绘制半透明背景"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)
        
        # 绘制半透明背景
        painter.fillRect(self.rect(), QColor(0, 0, 0, 150))
        
        super().paintEvent(event)
