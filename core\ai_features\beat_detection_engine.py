#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高精度音乐节拍检测引擎
基于踩点转场技术方案实现毫秒级精度的节拍检测
"""

import numpy as np
import cv2
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path
import json
import time
from core.logger import get_logger

try:
    import librosa
    import scipy.signal
    LIBROSA_AVAILABLE = True
except ImportError:
    LIBROSA_AVAILABLE = False
    print("⚠️ librosa未安装，将使用简化的节拍检测")

try:
    from sklearn.cluster import KMeans
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("⚠️ sklearn未安装，将使用基础聚类算法")


@dataclass
class BeatPoint:
    """节拍点数据结构"""
    timestamp: float  # 时间戳（秒）
    confidence: float  # 置信度 (0-1)
    beat_type: str  # 节拍类型: STRONG, MEDIUM, WEAK
    energy: float  # 能量值
    is_onset: bool = False  # 是否为音符起始点
    is_corrected: bool = False  # 是否为手动修正


@dataclass
class BeatAnalysisResult:
    """节拍分析结果"""
    bpm: float  # 每分钟节拍数
    beat_points: List[BeatPoint]  # 节拍点列表
    tempo_stability: float  # 节拍稳定性 (0-1)
    music_emotion: str  # 音乐情感: JOY, CALM, EXCITEMENT
    time_signature: str  # 拍号: 4/4, 3/4, etc.
    analysis_accuracy: float  # 分析精度评估


class HighPrecisionBeatDetector:
    """高精度节拍检测器"""
    
    def __init__(self, target_accuracy_ms: float = 50.0):
        """
        初始化节拍检测器
        
        Args:
            target_accuracy_ms: 目标精度（毫秒），默认50ms
        """
        self.logger = get_logger('beat_detector')
        self.target_accuracy_ms = target_accuracy_ms
        self.sample_rate = 22050
        self.hop_length = 512
        self.frame_length = 2048
        
        # 缓存
        self._analysis_cache = {}
        
        self.logger.info(f"高精度节拍检测器初始化，目标精度: ±{target_accuracy_ms}ms")
    
    def analyze_music(self, audio_path: str) -> BeatAnalysisResult:
        """
        分析音乐文件，返回完整的节拍分析结果
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            节拍分析结果
        """
        if audio_path in self._analysis_cache:
            return self._analysis_cache[audio_path]
        
        try:
            self.logger.info(f"开始高精度节拍分析: {Path(audio_path).name}")
            start_time = time.time()
            
            if not LIBROSA_AVAILABLE:
                return self._fallback_analysis(audio_path)
            
            # 1. 加载音频
            y, sr = librosa.load(audio_path, sr=self.sample_rate)
            duration = len(y) / sr
            
            # 2. 波形能量分析
            energy_profile = self._analyze_energy_profile(y, sr)
            
            # 3. 峰值检测：识别鼓点/重音
            onset_frames = self._detect_onsets(y, sr)
            onset_times = librosa.frames_to_time(onset_frames, sr=sr, hop_length=self.hop_length)
            
            # 4. 节拍类型判断和BPM计算
            tempo, beat_frames = librosa.beat.beat_track(
                y=y, sr=sr, hop_length=self.hop_length, units='frames'
            )
            beat_times = librosa.frames_to_time(beat_frames, sr=sr, hop_length=self.hop_length)
            
            # 5. 高精度节拍修正
            refined_beats = self._refine_beat_precision(
                beat_times, onset_times, energy_profile, y, sr
            )
            
            # 6. 节拍分类和置信度计算
            beat_points = self._classify_beats(refined_beats, energy_profile, tempo)
            
            # 7. 音乐情感分析
            emotion = self._analyze_music_emotion(y, sr, tempo)
            
            # 8. 节拍稳定性评估
            stability = self._calculate_tempo_stability(beat_points)
            
            # 9. 时间签名检测
            time_signature = self._detect_time_signature(beat_points, tempo)
            
            # 10. 精度评估
            accuracy = self._estimate_accuracy(beat_points, onset_times)
            
            result = BeatAnalysisResult(
                bpm=float(tempo),
                beat_points=beat_points,
                tempo_stability=stability,
                music_emotion=emotion,
                time_signature=time_signature,
                analysis_accuracy=accuracy
            )
            
            # 缓存结果
            self._analysis_cache[audio_path] = result
            
            analysis_time = time.time() - start_time
            self.logger.info(
                f"节拍分析完成: BPM={tempo:.1f}, "
                f"节拍点={len(beat_points)}, "
                f"精度={accuracy:.1%}, "
                f"用时={analysis_time:.2f}s"
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"节拍分析失败: {e}")
            return self._fallback_analysis(audio_path)
    
    def _analyze_energy_profile(self, y: np.ndarray, sr: int) -> np.ndarray:
        """分析波形能量分布"""
        try:
            # 计算短时能量
            frame_length = 2048
            hop_length = 512
            
            # RMS能量
            rms = librosa.feature.rms(y=y, frame_length=frame_length, hop_length=hop_length)[0]
            
            # 频谱质心（亮度指标）
            spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr, hop_length=hop_length)[0]
            
            # 零交叉率（节奏指标）
            zcr = librosa.feature.zero_crossing_rate(y, frame_length=frame_length, hop_length=hop_length)[0]
            
            # 综合能量特征
            energy_profile = rms * (1 + spectral_centroids / np.max(spectral_centroids) * 0.3)
            energy_profile *= (1 + zcr * 0.2)
            
            return energy_profile
            
        except Exception as e:
            self.logger.error(f"能量分析失败: {e}")
            return np.ones(len(y) // 512)
    
    def _detect_onsets(self, y: np.ndarray, sr: int) -> np.ndarray:
        """检测音符起始点"""
        try:
            # 使用多种onset检测方法
            onset_envelope = librosa.onset.onset_strength(y=y, sr=sr, hop_length=self.hop_length)
            
            # 动态阈值检测
            onset_frames = librosa.onset.onset_detect(
                onset_envelope=onset_envelope,
                sr=sr,
                hop_length=self.hop_length,
                delta=0.1,
                wait=10
            )
            
            return onset_frames
            
        except Exception as e:
            self.logger.error(f"Onset检测失败: {e}")
            return np.array([])
    
    def _refine_beat_precision(self, beat_times: np.ndarray, onset_times: np.ndarray, 
                             energy_profile: np.ndarray, y: np.ndarray, sr: int) -> List[float]:
        """高精度节拍修正"""
        try:
            refined_beats = []
            
            for beat_time in beat_times:
                # 在节拍点附近寻找最近的onset
                time_window = 0.1  # 100ms窗口
                nearby_onsets = onset_times[
                    (onset_times >= beat_time - time_window) & 
                    (onset_times <= beat_time + time_window)
                ]
                
                if len(nearby_onsets) > 0:
                    # 选择能量最高的onset作为精确节拍点
                    best_onset = beat_time
                    max_energy = 0
                    
                    for onset in nearby_onsets:
                        frame_idx = int(onset * sr / self.hop_length)
                        if frame_idx < len(energy_profile):
                            energy = energy_profile[frame_idx]
                            if energy > max_energy:
                                max_energy = energy
                                best_onset = onset
                    
                    refined_beats.append(best_onset)
                else:
                    # 没有nearby onset，保持原始节拍点
                    refined_beats.append(beat_time)
            
            return refined_beats
            
        except Exception as e:
            self.logger.error(f"节拍精修失败: {e}")
            return beat_times.tolist()
    
    def _classify_beats(self, beat_times: List[float], energy_profile: np.ndarray, 
                       tempo: float) -> List[BeatPoint]:
        """节拍分类和置信度计算"""
        try:
            beat_points = []
            
            for i, beat_time in enumerate(beat_times):
                # 计算能量值
                frame_idx = int(beat_time * self.sample_rate / self.hop_length)
                energy = energy_profile[frame_idx] if frame_idx < len(energy_profile) else 0.5
                
                # 节拍类型分类
                beat_type = self._classify_single_beat(i, len(beat_times), energy, tempo)
                
                # 置信度计算
                confidence = self._calculate_beat_confidence(beat_time, beat_times, energy)
                
                beat_point = BeatPoint(
                    timestamp=beat_time,
                    confidence=confidence,
                    beat_type=beat_type,
                    energy=float(energy),
                    is_onset=True
                )
                
                beat_points.append(beat_point)
            
            return beat_points
            
        except Exception as e:
            self.logger.error(f"节拍分类失败: {e}")
            return []
    
    def _classify_single_beat(self, index: int, total_beats: int, energy: float, tempo: float) -> str:
        """单个节拍分类"""
        # 基于位置的强弱拍判断
        if index % 4 == 0:  # 每4拍的第1拍
            base_strength = "STRONG"
        elif index % 2 == 0:  # 每2拍的第1拍
            base_strength = "MEDIUM"
        else:
            base_strength = "WEAK"
        
        # 基于能量的调整
        if energy > 0.8:
            if base_strength == "WEAK":
                return "MEDIUM"
            elif base_strength == "MEDIUM":
                return "STRONG"
        elif energy < 0.3:
            if base_strength == "STRONG":
                return "MEDIUM"
            elif base_strength == "MEDIUM":
                return "WEAK"
        
        return base_strength
    
    def _calculate_beat_confidence(self, beat_time: float, all_beats: List[float], energy: float) -> float:
        """计算节拍置信度"""
        try:
            # 基于能量的置信度
            energy_confidence = min(1.0, energy)
            
            # 基于时间间隔一致性的置信度
            if len(all_beats) < 3:
                interval_confidence = 0.5
            else:
                # 计算与相邻节拍的间隔一致性
                intervals = np.diff(all_beats)
                if len(intervals) > 0:
                    median_interval = np.median(intervals)
                    current_idx = all_beats.index(beat_time)
                    
                    if current_idx > 0:
                        current_interval = beat_time - all_beats[current_idx - 1]
                        interval_deviation = abs(current_interval - median_interval) / median_interval
                        interval_confidence = max(0.0, 1.0 - interval_deviation)
                    else:
                        interval_confidence = 0.8
                else:
                    interval_confidence = 0.5
            
            # 综合置信度
            confidence = (energy_confidence * 0.6 + interval_confidence * 0.4)
            return float(np.clip(confidence, 0.0, 1.0))
            
        except Exception as e:
            self.logger.error(f"置信度计算失败: {e}")
            return 0.5
    
    def _analyze_music_emotion(self, y: np.ndarray, sr: int, tempo: float) -> str:
        """音乐情感分析"""
        try:
            # 基于音频特征的情感分析
            
            # 1. 节奏特征
            if tempo > 130:
                tempo_emotion = "EXCITEMENT"
            elif tempo > 100:
                tempo_emotion = "JOY"
            else:
                tempo_emotion = "CALM"
            
            # 2. 频谱特征
            spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
            brightness = np.mean(spectral_centroids)
            
            # 3. 动态范围
            rms = librosa.feature.rms(y=y)[0]
            dynamic_range = np.max(rms) - np.min(rms)
            
            # 综合判断
            if tempo > 120 and brightness > 2000 and dynamic_range > 0.3:
                return "EXCITEMENT"
            elif tempo > 90 and brightness > 1500:
                return "JOY"
            else:
                return "CALM"
                
        except Exception as e:
            self.logger.error(f"情感分析失败: {e}")
            return "JOY"
    
    def _calculate_tempo_stability(self, beat_points: List[BeatPoint]) -> float:
        """计算节拍稳定性"""
        try:
            if len(beat_points) < 3:
                return 0.5
            
            timestamps = [bp.timestamp for bp in beat_points]
            intervals = np.diff(timestamps)
            
            if len(intervals) == 0:
                return 0.0
            
            # 计算间隔的变异系数
            mean_interval = np.mean(intervals)
            std_interval = np.std(intervals)
            
            if mean_interval == 0:
                return 0.0
            
            cv = std_interval / mean_interval
            stability = max(0.0, 1.0 - cv)
            
            return float(stability)
            
        except Exception as e:
            self.logger.error(f"稳定性计算失败: {e}")
            return 0.5
    
    def _detect_time_signature(self, beat_points: List[BeatPoint], tempo: float) -> str:
        """检测时间签名"""
        try:
            # 简化的时间签名检测
            strong_beats = [bp for bp in beat_points if bp.beat_type == "STRONG"]
            
            if len(strong_beats) < 2:
                return "4/4"
            
            # 计算强拍间隔
            strong_intervals = []
            for i in range(1, len(strong_beats)):
                interval = strong_beats[i].timestamp - strong_beats[i-1].timestamp
                strong_intervals.append(interval)
            
            if not strong_intervals:
                return "4/4"
            
            avg_strong_interval = np.mean(strong_intervals)
            beat_interval = 60.0 / tempo
            
            # 估算每小节的拍数
            beats_per_measure = round(avg_strong_interval / beat_interval)
            
            if beats_per_measure == 3:
                return "3/4"
            elif beats_per_measure == 6:
                return "6/8"
            else:
                return "4/4"
                
        except Exception as e:
            self.logger.error(f"时间签名检测失败: {e}")
            return "4/4"
    
    def _estimate_accuracy(self, beat_points: List[BeatPoint], onset_times: np.ndarray) -> float:
        """估算分析精度"""
        try:
            if len(beat_points) == 0 or len(onset_times) == 0:
                return 0.0
            
            # 计算节拍点与onset的匹配度
            matches = 0
            total_beats = len(beat_points)
            
            for beat_point in beat_points:
                # 在目标精度范围内寻找匹配的onset
                tolerance = self.target_accuracy_ms / 1000.0  # 转换为秒
                nearby_onsets = onset_times[
                    np.abs(onset_times - beat_point.timestamp) <= tolerance
                ]
                
                if len(nearby_onsets) > 0:
                    matches += 1
            
            accuracy = matches / total_beats if total_beats > 0 else 0.0
            return float(accuracy)
            
        except Exception as e:
            self.logger.error(f"精度估算失败: {e}")
            return 0.5
    
    def _fallback_analysis(self, audio_path: str) -> BeatAnalysisResult:
        """备选分析方案（当librosa不可用时）"""
        self.logger.warning("使用备选节拍分析方案")
        
        # 生成固定间隔的节拍点
        duration = 30.0  # 假设30秒
        bpm = 120.0  # 假设120 BPM
        beat_interval = 60.0 / bpm
        
        beat_points = []
        current_time = 0.0
        
        while current_time < duration:
            beat_type = "STRONG" if len(beat_points) % 4 == 0 else "WEAK"
            beat_point = BeatPoint(
                timestamp=current_time,
                confidence=0.7,
                beat_type=beat_type,
                energy=0.5,
                is_onset=True
            )
            beat_points.append(beat_point)
            current_time += beat_interval
        
        return BeatAnalysisResult(
            bpm=bpm,
            beat_points=beat_points,
            tempo_stability=0.8,
            music_emotion="JOY",
            time_signature="4/4",
            analysis_accuracy=0.7
        )
    
    def manual_correct_beat(self, beat_index: int, new_timestamp: float, 
                          analysis_result: BeatAnalysisResult) -> BeatAnalysisResult:
        """手动修正节拍点"""
        try:
            if 0 <= beat_index < len(analysis_result.beat_points):
                # 修正指定节拍点
                analysis_result.beat_points[beat_index].timestamp = new_timestamp
                analysis_result.beat_points[beat_index].is_corrected = True
                
                # 重新对齐后续节点
                self._realign_subsequent_beats(beat_index, analysis_result)
                
                self.logger.info(f"手动修正节拍点 {beat_index}: {new_timestamp:.3f}s")
            
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"手动修正失败: {e}")
            return analysis_result
    
    def _realign_subsequent_beats(self, start_index: int, analysis_result: BeatAnalysisResult):
        """重新对齐后续节拍点"""
        try:
            beat_points = analysis_result.beat_points
            
            if start_index >= len(beat_points) - 1:
                return
            
            # 计算修正后的节拍间隔
            if start_index > 0:
                interval = beat_points[start_index].timestamp - beat_points[start_index - 1].timestamp
            else:
                interval = 60.0 / analysis_result.bpm
            
            # 重新对齐后续节拍
            for i in range(start_index + 1, len(beat_points)):
                if not beat_points[i].is_corrected:  # 只调整未手动修正的节拍
                    expected_time = beat_points[i - 1].timestamp + interval
                    beat_points[i].timestamp = expected_time
            
        except Exception as e:
            self.logger.error(f"重新对齐失败: {e}")
    
    def export_beat_data(self, analysis_result: BeatAnalysisResult, output_path: str) -> bool:
        """导出节拍数据"""
        try:
            data = {
                'bpm': analysis_result.bpm,
                'tempo_stability': analysis_result.tempo_stability,
                'music_emotion': analysis_result.music_emotion,
                'time_signature': analysis_result.time_signature,
                'analysis_accuracy': analysis_result.analysis_accuracy,
                'beat_points': [
                    {
                        'timestamp': bp.timestamp,
                        'confidence': bp.confidence,
                        'beat_type': bp.beat_type,
                        'energy': bp.energy,
                        'is_onset': bp.is_onset,
                        'is_corrected': bp.is_corrected
                    }
                    for bp in analysis_result.beat_points
                ]
            }
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=4, ensure_ascii=False)
            
            self.logger.info(f"节拍数据已导出: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出节拍数据失败: {e}")
            return False
