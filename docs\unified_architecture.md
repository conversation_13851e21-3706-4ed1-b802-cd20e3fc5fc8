# 统一视频编辑架构设计

## 🎯 设计目标

基于你的需求，我们设计了一个**数据驱动的统一架构**，实现：

1. **统一参数管理** - 所有项目数据集中管理
2. **统一播放控制** - 所有播放触发方式使用同一套逻辑
3. **线程安全** - 读写锁保护数据一致性
4. **实时效果** - 统一的帧渲染管道

## 🏗️ 架构组件

### 1. 统一项目管理器 (`UnifiedProjectManager`)

**核心职责**：
- 管理所有轨道、素材、播放状态
- 提供线程安全的读写操作
- 统一的数据模型和状态管理

**主要功能**：
```python
# 轨道管理
track_id = manager.add_track(TrackType.VIDEO, "视频轨道1")
manager.remove_track(track_id)

# 媒体项管理
item_id = manager.add_media_item(track_id, file_path, start, duration)
manager.remove_media_item(track_id, item_id)

# 播放状态管理
manager.set_playhead_position(10.5)  # 跳转到10.5秒
manager.set_playback_state(PlaybackState.PLAYING)
```

**数据模型**：
```python
@dataclass
class MediaItem:
    # 基础信息
    item_id: str
    file_path: str
    track_id: str
    
    # 时间信息
    timeline_start: float      # 时间轴位置
    timeline_duration: float   # 时间轴时长
    
    # 裁剪信息
    source_start: float        # 源文件开始位置
    source_duration: float     # 源文件总时长
    trim_start: float          # 裁剪开始
    trim_end: float           # 裁剪结束
    
    # 效果参数
    effects: List[MediaEffect]
    opacity: float
    volume: float
```

### 2. 统一播放控制器 (`UnifiedPlaybackController`)

**核心职责**：
- 统一处理所有播放触发方式
- 同步视频和音频播放
- 管理播放状态和位置

**统一播放接口**：
```python
# 所有播放触发都使用同一个方法
controller.play(PlaybackTrigger.USER_PLAY_BUTTON)    # 用户点击播放
controller.play(PlaybackTrigger.TIMELINE_CLICK)      # 点击时间轴
controller.play(PlaybackTrigger.PLAYHEAD_DRAG)       # 拖拽播放头
controller.play(PlaybackTrigger.KEYBOARD_SPACE)      # 空格键

# 统一跳转接口
controller.seek(position, PlaybackTrigger.TIMELINE_CLICK)
```

**播放逻辑统一**：
```python
def play(self, trigger: PlaybackTrigger):
    current_state = self.project_manager.get_playback_state()
    
    if current_state == PlaybackState.PLAYING:
        if trigger == PlaybackTrigger.USER_PLAY_BUTTON:
            self.pause()  # 暂停
        elif trigger == PlaybackTrigger.TIMELINE_CLICK:
            self._start_playback_from_position(position)  # 跳转继续播放
    
    elif current_state == PlaybackState.PAUSED:
        self._resume_playback()  # 恢复播放
    
    elif current_state == PlaybackState.STOPPED:
        self._start_playback_from_position(position)  # 开始播放
```

### 3. 统一视频渲染器 (`UnifiedVideoRenderer`)

**核心职责**：
- 统一的帧渲染管道
- 实时效果处理
- 性能优化和缓存

**统一渲染流程**：
```python
def render_frame_at_position(self, request: RenderRequest):
    # 1. 获取原始帧
    ret, raw_frame = cap.read()
    
    # 2. 应用效果处理
    processed_frame = self._apply_effects(raw_frame, request.effects)
    
    # 3. 应用质量缩放
    final_frame = self._apply_quality_scaling(processed_frame, request.quality)
    
    # 4. 发射结果
    self.frame_ready.emit(request.video_path, final_frame)
```

## 🔒 线程安全设计

### 读写锁实现
```python
class RWLock:
    def __init__(self):
        self._read_ready = threading.Condition(threading.RLock())
        self._readers = 0
    
    @contextmanager
    def r_locked(self):  # 读锁
        self.acquire_read()
        try:
            yield
        finally:
            self.release_read()
    
    @contextmanager
    def w_locked(self):  # 写锁
        self.acquire_write()
        try:
            yield
        finally:
            self.release_write()
```

### 装饰器使用
```python
@_read_lock
def get_track(self, track_id: str) -> Optional[Track]:
    return self._tracks.get(track_id)

@_write_lock
def add_track(self, track_type: TrackType, name: str = None) -> str:
    # 修改数据的操作
    pass
```

## 🎮 统一控制流程

### 1. 播放触发统一处理

```mermaid
graph TD
    A[播放触发] --> B{当前状态?}
    B -->|播放中| C{触发类型?}
    B -->|暂停| D[恢复播放]
    B -->|停止| E[开始播放]
    
    C -->|播放按钮| F[暂停]
    C -->|时间轴点击| G[跳转继续播放]
    
    D --> H[统一播放逻辑]
    E --> H
    G --> H
    
    H --> I[获取当前位置媒体项]
    I --> J[启动视频渲染]
    I --> K[启动音频播放]
    J --> L[设置播放状态]
    K --> L
```

### 2. 参数变化响应

```mermaid
graph TD
    A[参数变化] --> B{变化类型?}
    B -->|播放头位置| C[更新时间轴显示]
    B -->|效果参数| D[实时重新渲染]
    B -->|播放状态| E[更新UI状态]
    
    C --> F[如果非播放状态]
    F --> G[渲染预览帧]
    
    D --> H[获取当前帧]
    H --> I[应用新效果]
    I --> J[更新显示]
```

## 📊 数据流向

### 1. 播放数据流
```
用户操作 → 播放控制器 → 项目管理器 → 渲染器 → 显示组件
    ↓           ↓            ↓          ↓        ↓
  触发类型    统一播放逻辑   状态管理    帧处理   UI更新
```

### 2. 参数数据流
```
UI控件 → 项目管理器 → 播放控制器 → 渲染器 → 实时预览
   ↓         ↓           ↓          ↓        ↓
 参数输入   数据存储    播放同步    效果应用   视觉反馈
```

## 🚀 关键优势

### 1. **真正的统一**
- 所有播放触发使用同一套逻辑
- 所有帧渲染使用同一个管道
- 所有参数使用统一的数据模型

### 2. **数据驱动**
- UI只是数据的视图
- 所有状态集中管理
- 数据变化自动同步到UI

### 3. **线程安全**
- 读写锁保护数据一致性
- 支持多线程并发访问
- 避免数据竞争问题

### 4. **高性能**
- 智能缓存机制
- 质量分级渲染
- 资源复用优化

### 5. **易扩展**
- 新的播放触发方式只需添加枚举
- 新的效果只需实现处理函数
- 新的媒体类型只需扩展数据模型

## 💡 使用示例

### 完整的播放控制
```python
# 创建核心组件
project_manager = UnifiedProjectManager()
playback_controller = UnifiedPlaybackController(project_manager)

# 初始化项目
project_manager.initialize_default_project()

# 添加轨道和媒体
video_track_id = project_manager.add_track(TrackType.VIDEO)
item_id = project_manager.add_media_item(
    video_track_id, "video.mp4", 0.0, 10.0
)

# 统一播放控制
playback_controller.play(PlaybackTrigger.USER_PLAY_BUTTON)  # 播放
playback_controller.seek(5.0, PlaybackTrigger.TIMELINE_CLICK)  # 跳转
playback_controller.pause()  # 暂停

# 实时效果调整
media_item = project_manager.get_media_item(video_track_id, item_id)
media_item.effects.append(MediaEffect("brightness", 50, True))
playback_controller.update_effects()  # 立即生效
```

这个架构完全符合你的需求：**统一参数管理、统一播放控制、线程安全、实时效果**。所有的播放触发、播放头移动、点击、视频播放都使用同一套逻辑，音频也是一样！
