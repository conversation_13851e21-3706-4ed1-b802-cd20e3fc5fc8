#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频块组件对比测试 - QWidget vs QLabel方案
"""

import sys
import time
from pathlib import Path
from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                               QWidget, QLabel, QPushButton, QScrollArea, QGroupBox)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

# 导入两种实现
try:
    from gui.main.video_thumbnail_block import VideoThumbnailBlock
    from gui.components.video_image_block import VideoImageBlock
    from core.media_processing.video_processor import VideoProcessor
except ImportError as e:
    print(f"导入失败: {e}")
    sys.exit(1)

class MockTimeline:
    """模拟时间轴对象"""
    def __init__(self):
        self.pixels_per_second = 100
        self.tracks = [{'type': 'video', 'media_files': []}]

class PerformanceMonitor:
    """性能监控器"""
    def __init__(self):
        self.start_time = 0
        self.frame_count = 0
        self.last_fps_time = 0
        self.fps = 0
    
    def start_monitoring(self):
        """开始监控"""
        self.start_time = time.time()
        self.last_fps_time = self.start_time
        self.frame_count = 0
    
    def update_frame(self):
        """更新帧计数"""
        self.frame_count += 1
        current_time = time.time()
        
        # 每秒计算一次FPS
        if current_time - self.last_fps_time >= 1.0:
            self.fps = self.frame_count / (current_time - self.last_fps_time)
            self.frame_count = 0
            self.last_fps_time = current_time
    
    def get_fps(self):
        """获取当前FPS"""
        return self.fps

class ComparisonWidget(QWidget):
    """对比测试组件"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_test_data()
        self.performance_monitor = PerformanceMonitor()
        
        # 性能监控定时器
        self.perf_timer = QTimer()
        self.perf_timer.timeout.connect(self.update_performance)
        self.perf_timer.start(100)  # 100ms更新一次
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("视频块组件性能对比测试")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 性能指标显示
        self.perf_label = QLabel("FPS: 0.0 | 内存使用: 监控中...")
        self.perf_label.setFont(QFont("Arial", 12))
        self.perf_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.perf_label)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.start_test_btn = QPushButton("开始拖动测试")
        self.start_test_btn.clicked.connect(self.start_drag_test)
        button_layout.addWidget(self.start_test_btn)
        
        self.stop_test_btn = QPushButton("停止测试")
        self.stop_test_btn.clicked.connect(self.stop_test)
        button_layout.addWidget(self.stop_test_btn)
        
        self.clear_btn = QPushButton("清空组件")
        self.clear_btn.clicked.connect(self.clear_components)
        button_layout.addWidget(self.clear_btn)
        
        layout.addLayout(button_layout)
        
        # 对比区域
        comparison_layout = QHBoxLayout()
        
        # QWidget方案
        widget_group = QGroupBox("QWidget + QPainter 方案（原版）")
        widget_layout = QVBoxLayout()
        
        self.widget_scroll = QScrollArea()
        self.widget_scroll.setMinimumHeight(200)
        self.widget_scroll.setWidgetResizable(True)
        self.widget_container = QWidget()
        self.widget_container_layout = QHBoxLayout(self.widget_container)
        self.widget_scroll.setWidget(self.widget_container)
        
        widget_layout.addWidget(QLabel("特点：每次移动都重绘，复杂计算"))
        widget_layout.addWidget(self.widget_scroll)
        widget_group.setLayout(widget_layout)
        comparison_layout.addWidget(widget_group)
        
        # QLabel方案
        label_group = QGroupBox("QLabel + QPixmap 方案（新版）")
        label_layout = QVBoxLayout()
        
        self.label_scroll = QScrollArea()
        self.label_scroll.setMinimumHeight(200)
        self.label_scroll.setWidgetResizable(True)
        self.label_container = QWidget()
        self.label_container_layout = QHBoxLayout(self.label_container)
        self.label_scroll.setWidget(self.label_container)
        
        label_layout.addWidget(QLabel("特点：预生成图片，裁剪操作图片，流畅拖动"))
        label_layout.addWidget(self.label_scroll)
        label_group.setLayout(label_layout)
        comparison_layout.addWidget(label_group)
        
        layout.addLayout(comparison_layout)
        
        # 测试结果
        self.result_label = QLabel("等待测试...")
        self.result_label.setFont(QFont("Arial", 10))
        self.result_label.setWordWrap(True)
        layout.addWidget(self.result_label)
        
        self.setLayout(layout)
    
    def setup_test_data(self):
        """设置测试数据"""
        self.mock_timeline = MockTimeline()
        self.video_processor = VideoProcessor()
        
        # 模拟媒体项
        self.test_media_items = []
        for i in range(5):
            media_item = {
                'file_path': f'test_video_{i}.mp4',
                'name': f'测试视频{i+1}',
                'duration': 10.0 + i * 2,
                'start_time': i * 12.0,
                'trim_start': 0.0,
                'is_placeholder': True  # 使用占位符避免实际文件依赖
            }
            self.test_media_items.append(media_item)
    
    def create_widget_blocks(self):
        """创建QWidget版本的块"""
        self.widget_blocks = []
        for i, media_item in enumerate(self.test_media_items):
            block = VideoThumbnailBlock(
                media_item, 0, i, self.mock_timeline, self.video_processor
            )
            block.setFixedSize(200, 64)
            self.widget_container_layout.addWidget(block)
            self.widget_blocks.append(block)
    
    def create_label_blocks(self):
        """创建QLabel版本的块"""
        self.label_blocks = []
        for i, media_item in enumerate(self.test_media_items):
            block = VideoImageBlock(
                media_item, 0, i, self.mock_timeline, self.video_processor
            )
            block.setFixedSize(200, 64)
            self.label_container_layout.addWidget(block)
            self.label_blocks.append(block)
    
    def start_drag_test(self):
        """开始拖动测试"""
        # 清空现有组件
        self.clear_components()
        
        # 创建测试组件
        self.create_widget_blocks()
        self.create_label_blocks()
        
        # 开始性能监控
        self.performance_monitor.start_monitoring()
        
        # 开始自动拖动测试
        self.drag_test_active = True
        self.drag_test_timer = QTimer()
        self.drag_test_timer.timeout.connect(self.simulate_drag)
        self.drag_test_timer.start(50)  # 50ms间隔，模拟快速拖动
        
        self.start_test_btn.setEnabled(False)
        self.result_label.setText("测试进行中... 模拟拖动操作")
    
    def simulate_drag(self):
        """模拟拖动操作"""
        if not hasattr(self, 'drag_test_active') or not self.drag_test_active:
            return
        
        import random
        
        # 随机移动QWidget块
        if hasattr(self, 'widget_blocks'):
            for block in self.widget_blocks:
                if random.random() < 0.3:  # 30%概率移动
                    new_x = random.randint(0, 100)
                    new_y = random.randint(0, 20)
                    block.move(block.x() + new_x - 50, block.y() + new_y - 10)
        
        # 随机移动QLabel块
        if hasattr(self, 'label_blocks'):
            for block in self.label_blocks:
                if random.random() < 0.3:  # 30%概率移动
                    new_x = random.randint(0, 100)
                    new_y = random.randint(0, 20)
                    block.move(block.x() + new_x - 50, block.y() + new_y - 10)
        
        # 更新性能计数
        self.performance_monitor.update_frame()
    
    def stop_test(self):
        """停止测试"""
        if hasattr(self, 'drag_test_timer'):
            self.drag_test_timer.stop()
        
        self.drag_test_active = False
        self.start_test_btn.setEnabled(True)
        
        # 显示测试结果
        fps = self.performance_monitor.get_fps()
        self.result_label.setText(
            f"测试完成！\n"
            f"平均FPS: {fps:.1f}\n"
            f"QWidget方案：每次移动都调用paintEvent，重新计算和绘制缩略图\n"
            f"QLabel方案：预生成图片，移动时不重绘，裁剪直接操作图片\n"
            f"理论上QLabel方案应该更流畅，特别是在复杂缩略图场景下"
        )
    
    def clear_components(self):
        """清空所有组件"""
        # 清空QWidget容器
        while self.widget_container_layout.count():
            child = self.widget_container_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        
        # 清空QLabel容器
        while self.label_container_layout.count():
            child = self.label_container_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        
        # 清空引用
        if hasattr(self, 'widget_blocks'):
            del self.widget_blocks
        if hasattr(self, 'label_blocks'):
            del self.label_blocks
    
    def update_performance(self):
        """更新性能显示"""
        fps = self.performance_monitor.get_fps()
        
        # 简单的内存使用估算
        widget_count = len(getattr(self, 'widget_blocks', [])) + len(getattr(self, 'label_blocks', []))
        memory_est = widget_count * 0.5  # 每个组件约0.5MB
        
        self.perf_label.setText(f"FPS: {fps:.1f} | 组件数量: {widget_count} | 估算内存: {memory_est:.1f}MB")

class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("视频块组件性能对比测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置中央组件
        comparison_widget = ComparisonWidget()
        self.setCentralWidget(comparison_widget)

def main():
    app = QApplication(sys.argv)
    
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
