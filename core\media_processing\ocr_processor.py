#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR文字识别模块
"""

import cv2
import numpy as np
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
import tempfile
import os

from ..common.logger import get_logger
from ..common.exceptions import OCRError, handle_exception


@dataclass
class TextRegion:
    """文字区域"""
    text: str
    confidence: float
    bbox: Tuple[int, int, int, int]  # (x, y, width, height)
    language: str = 'auto'


@dataclass
class VideoTextFrame:
    """视频帧中的文字信息"""
    frame_time: float
    frame_number: int
    text_regions: List[TextRegion]
    frame_path: Optional[str] = None


class OCRProcessor:
    """OCR文字识别处理器"""
    
    def __init__(self, config):
        self.config = config
        self.logger = get_logger('ocr_processor')
        
        # OCR引擎配置
        self.ocr_engines = {
            'easyocr': None,
            'tesseract': None
        }
        
        # 初始化OCR引擎
        self._init_ocr_engines()
    
    def _init_ocr_engines(self):
        """初始化OCR引擎"""
        # 初始化EasyOCR
        try:
            import easyocr
            self.ocr_engines['easyocr'] = easyocr.Reader(['ch_sim', 'en'], gpu=False)
            self.logger.info("EasyOCR initialized successfully")
        except ImportError:
            self.logger.warning("EasyOCR not available")
        except Exception as e:
            self.logger.error(f"Failed to initialize EasyOCR: {str(e)}")
        
        # 初始化Tesseract
        try:
            import pytesseract
            # 测试Tesseract是否可用
            pytesseract.get_tesseract_version()
            self.ocr_engines['tesseract'] = pytesseract
            self.logger.info("Tesseract initialized successfully")
        except ImportError:
            self.logger.warning("pytesseract not available")
        except Exception as e:
            self.logger.error(f"Failed to initialize Tesseract: {str(e)}")
    
    @handle_exception
    def extract_text_from_image(self, image: np.ndarray, engine: str = 'auto') -> List[TextRegion]:
        """从图像中提取文字"""
        try:
            if engine == 'auto':
                # 自动选择可用的引擎
                if self.ocr_engines['easyocr'] is not None:
                    engine = 'easyocr'
                elif self.ocr_engines['tesseract'] is not None:
                    engine = 'tesseract'
                else:
                    raise OCRError("No OCR engine available")
            
            if engine == 'easyocr':
                return self._extract_with_easyocr(image)
            elif engine == 'tesseract':
                return self._extract_with_tesseract(image)
            else:
                raise OCRError(f"Unknown OCR engine: {engine}")
                
        except Exception as e:
            raise OCRError(f"Text extraction failed: {str(e)}")
    
    def _extract_with_easyocr(self, image: np.ndarray) -> List[TextRegion]:
        """使用EasyOCR提取文字"""
        if self.ocr_engines['easyocr'] is None:
            raise OCRError("EasyOCR not available")
        
        try:
            results = self.ocr_engines['easyocr'].readtext(image)
            text_regions = []
            
            for (bbox, text, confidence) in results:
                # 转换边界框格式
                x_coords = [point[0] for point in bbox]
                y_coords = [point[1] for point in bbox]
                x, y = int(min(x_coords)), int(min(y_coords))
                width = int(max(x_coords) - min(x_coords))
                height = int(max(y_coords) - min(y_coords))
                
                text_regions.append(TextRegion(
                    text=text.strip(),
                    confidence=float(confidence),
                    bbox=(x, y, width, height),
                    language='auto'
                ))
            
            return text_regions
            
        except Exception as e:
            raise OCRError(f"EasyOCR extraction failed: {str(e)}")
    
    def _extract_with_tesseract(self, image: np.ndarray) -> List[TextRegion]:
        """使用Tesseract提取文字"""
        if self.ocr_engines['tesseract'] is None:
            raise OCRError("Tesseract not available")
        
        try:
            import pytesseract
            
            # 配置Tesseract
            config = '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz中文'
            
            # 获取详细信息
            data = pytesseract.image_to_data(image, config=config, output_type=pytesseract.Output.DICT, lang='chi_sim+eng')
            
            text_regions = []
            n_boxes = len(data['level'])
            
            for i in range(n_boxes):
                confidence = int(data['conf'][i])
                text = data['text'][i].strip()
                
                if confidence > 30 and text:  # 过滤低置信度和空文本
                    x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                    
                    text_regions.append(TextRegion(
                        text=text,
                        confidence=confidence / 100.0,  # 转换为0-1范围
                        bbox=(x, y, w, h),
                        language='auto'
                    ))
            
            return text_regions
            
        except Exception as e:
            raise OCRError(f"Tesseract extraction failed: {str(e)}")
    
    @handle_exception
    def preprocess_image_for_ocr(self, image: np.ndarray) -> np.ndarray:
        """预处理图像以提高OCR准确性"""
        try:
            # 转换为灰度图
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # 去噪
            denoised = cv2.medianBlur(gray, 3)
            
            # 增强对比度
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            enhanced = clahe.apply(denoised)
            
            # 二值化
            _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # 形态学操作去除噪点
            kernel = np.ones((2, 2), np.uint8)
            cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            
            return cleaned
            
        except Exception as e:
            raise OCRError(f"Image preprocessing failed: {str(e)}")
    
    @handle_exception
    def extract_text_from_video(self, video_path: str, sample_interval: float = 1.0, 
                               confidence_threshold: float = 0.5) -> List[VideoTextFrame]:
        """从视频中提取文字"""
        try:
            self.logger.info(f"Starting text extraction from video: {video_path}")
            
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise OCRError(f"Cannot open video file: {video_path}")
            
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = total_frames / fps
            
            frame_interval = int(fps * sample_interval)
            video_text_frames = []
            
            frame_number = 0
            processed_frames = 0
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 按间隔采样帧
                if frame_number % frame_interval == 0:
                    frame_time = frame_number / fps
                    
                    # 预处理图像
                    processed_frame = self.preprocess_image_for_ocr(frame)
                    
                    # 提取文字
                    text_regions = self.extract_text_from_image(processed_frame)
                    
                    # 过滤低置信度结果
                    filtered_regions = [
                        region for region in text_regions 
                        if region.confidence >= confidence_threshold
                    ]
                    
                    if filtered_regions:
                        video_text_frames.append(VideoTextFrame(
                            frame_time=frame_time,
                            frame_number=frame_number,
                            text_regions=filtered_regions
                        ))
                        
                        self.logger.debug(f"Found {len(filtered_regions)} text regions at {frame_time:.2f}s")
                    
                    processed_frames += 1
                    
                    # 进度报告
                    if processed_frames % 10 == 0:
                        progress = (frame_number / total_frames) * 100
                        self.logger.info(f"OCR progress: {progress:.1f}%")
                
                frame_number += 1
            
            cap.release()
            
            self.logger.info(f"Text extraction completed. Found text in {len(video_text_frames)} frames")
            return video_text_frames
            
        except Exception as e:
            raise OCRError(f"Video text extraction failed: {str(e)}")
    
    @handle_exception
    def merge_similar_text_regions(self, video_text_frames: List[VideoTextFrame], 
                                  similarity_threshold: float = 0.8,
                                  time_window: float = 2.0) -> List[Dict[str, Any]]:
        """合并相似的文字区域"""
        try:
            from difflib import SequenceMatcher
            
            merged_texts = []
            
            for i, frame in enumerate(video_text_frames):
                for region in frame.text_regions:
                    # 检查是否与已有文本相似
                    merged = False
                    
                    for existing in merged_texts:
                        # 时间窗口检查
                        time_diff = abs(frame.frame_time - existing['last_seen'])
                        if time_diff > time_window:
                            continue
                        
                        # 文本相似度检查
                        similarity = SequenceMatcher(None, region.text, existing['text']).ratio()
                        if similarity >= similarity_threshold:
                            # 更新现有记录
                            existing['last_seen'] = frame.frame_time
                            existing['occurrences'] += 1
                            existing['confidence'] = max(existing['confidence'], region.confidence)
                            merged = True
                            break
                    
                    if not merged:
                        # 创建新记录
                        merged_texts.append({
                            'text': region.text,
                            'first_seen': frame.frame_time,
                            'last_seen': frame.frame_time,
                            'occurrences': 1,
                            'confidence': region.confidence,
                            'bbox': region.bbox
                        })
            
            # 按出现次数和置信度排序
            merged_texts.sort(key=lambda x: (x['occurrences'], x['confidence']), reverse=True)
            
            self.logger.info(f"Merged {len(merged_texts)} unique text regions")
            return merged_texts
            
        except Exception as e:
            raise OCRError(f"Text merging failed: {str(e)}")
