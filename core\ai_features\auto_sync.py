#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动踩点同步模块
"""

import numpy as np
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
import math

from ..common.logger import get_logger
from ..common.exceptions import AudioProcessingError, VideoProcessingError, handle_exception
from ..media_processing.audio_processor import AudioProcessor, BeatPoint


@dataclass
class SyncPoint:
    """同步点"""
    timestamp: float
    beat_timestamp: float
    confidence: float
    sync_type: str  # 'beat', 'downbeat', 'phrase', 'section'
    strength: float


@dataclass
class VideoSegment:
    """视频片段"""
    id: str
    start_time: float
    duration: float
    end_time: float
    file_path: str
    segment_type: str = 'normal'  # 'intro', 'verse', 'chorus', 'bridge', 'outro'
    priority: int = 1  # 1-5, 5为最高优先级


class AutoSyncProcessor:
    """自动踩点同步处理器"""
    
    def __init__(self, config):
        self.config = config
        self.logger = get_logger('auto_sync')
        self.audio_processor = AudioProcessor(config)
        
        # 同步参数
        self.sync_tolerance = 0.3  # 同步容差（秒）
        self.min_segment_duration = 1.0  # 最小片段时长
        self.max_segment_duration = 8.0  # 最大片段时长
    
    @handle_exception
    def analyze_music_structure(self, audio_path: str) -> Dict[str, Any]:
        """分析音乐结构"""
        try:
            self.logger.info(f"Analyzing music structure: {audio_path}")
            
            # 获取音频特征
            features = self.audio_processor.analyze_audio_features(audio_path)
            
            # 检测节拍点
            beat_points = self.audio_processor.detect_beats_advanced(audio_path)
            
            # 检测音乐段落
            music_segments = self.audio_processor.detect_music_segments(audio_path)
            
            # 分析节拍模式
            beat_pattern = self._analyze_beat_pattern(beat_points)
            
            # 识别强拍和弱拍
            strong_beats = self._identify_strong_beats(beat_points, features['tempo'])
            
            structure = {
                'features': features,
                'beat_points': beat_points,
                'music_segments': music_segments,
                'beat_pattern': beat_pattern,
                'strong_beats': strong_beats,
                'downbeats': self._detect_downbeats(beat_points, features['tempo'])
            }
            
            self.logger.info(f"Music structure analysis completed: {len(beat_points)} beats, {len(music_segments)} segments")
            return structure
            
        except Exception as e:
            raise AudioProcessingError(f"Music structure analysis failed: {str(e)}")
    
    def _analyze_beat_pattern(self, beat_points: List[BeatPoint]) -> Dict[str, Any]:
        """分析节拍模式"""
        if len(beat_points) < 4:
            return {'time_signature': '4/4', 'pattern_confidence': 0.0}
        
        # 计算节拍间隔
        intervals = []
        for i in range(1, len(beat_points)):
            interval = beat_points[i].timestamp - beat_points[i-1].timestamp
            intervals.append(interval)
        
        # 分析节拍规律性
        mean_interval = np.mean(intervals)
        std_interval = np.std(intervals)
        regularity = 1.0 - min(1.0, std_interval / mean_interval)
        
        # 简单的拍号检测（基于强度模式）
        strengths = [bp.strength for bp in beat_points]
        
        # 检测4/4拍模式
        pattern_4_4 = self._check_time_signature_pattern(strengths, 4)
        pattern_3_4 = self._check_time_signature_pattern(strengths, 3)
        
        if pattern_4_4 > pattern_3_4:
            time_signature = '4/4'
            confidence = pattern_4_4
        else:
            time_signature = '3/4'
            confidence = pattern_3_4
        
        return {
            'time_signature': time_signature,
            'pattern_confidence': confidence,
            'regularity': regularity,
            'mean_interval': mean_interval
        }
    
    def _check_time_signature_pattern(self, strengths: List[float], beats_per_measure: int) -> float:
        """检查特定拍号的模式匹配度"""
        if len(strengths) < beats_per_measure * 2:
            return 0.0
        
        # 将强度按拍号分组
        measures = []
        for i in range(0, len(strengths) - beats_per_measure + 1, beats_per_measure):
            measure = strengths[i:i + beats_per_measure]
            if len(measure) == beats_per_measure:
                measures.append(measure)
        
        if not measures:
            return 0.0
        
        # 计算每个拍位的平均强度
        avg_strengths = np.mean(measures, axis=0)
        
        # 检查第一拍是否最强（下拍特征）
        first_beat_strength = avg_strengths[0]
        other_beats_strength = np.mean(avg_strengths[1:])
        
        # 计算模式匹配度
        if first_beat_strength > other_beats_strength:
            confidence = min(1.0, (first_beat_strength - other_beats_strength) / first_beat_strength)
        else:
            confidence = 0.0
        
        return confidence
    
    def _identify_strong_beats(self, beat_points: List[BeatPoint], tempo: float) -> List[BeatPoint]:
        """识别强拍"""
        if not beat_points:
            return []
        
        # 基于强度阈值识别强拍
        strengths = [bp.strength for bp in beat_points]
        mean_strength = np.mean(strengths)
        std_strength = np.std(strengths)
        threshold = mean_strength + 0.5 * std_strength
        
        strong_beats = [bp for bp in beat_points if bp.strength >= threshold]
        
        self.logger.debug(f"Identified {len(strong_beats)} strong beats out of {len(beat_points)}")
        return strong_beats
    
    def _detect_downbeats(self, beat_points: List[BeatPoint], tempo: float) -> List[BeatPoint]:
        """检测下拍（小节第一拍）"""
        if len(beat_points) < 4:
            return []
        
        # 估算每小节的节拍数（假设4/4拍）
        beats_per_measure = 4
        beat_interval = 60.0 / tempo
        measure_duration = beat_interval * beats_per_measure
        
        downbeats = []
        current_time = 0.0
        
        for bp in beat_points:
            # 检查是否接近小节开始
            measure_position = bp.timestamp % measure_duration
            if measure_position < beat_interval * 0.5:  # 在第一拍附近
                downbeats.append(bp)
                current_time = bp.timestamp + measure_duration
        
        self.logger.debug(f"Detected {len(downbeats)} downbeats")
        return downbeats
    
    @handle_exception
    def generate_sync_points(self, music_structure: Dict[str, Any], 
                           video_segments: List[VideoSegment]) -> List[SyncPoint]:
        """生成同步点"""
        try:
            sync_points = []
            beat_points = music_structure['beat_points']
            strong_beats = music_structure['strong_beats']
            downbeats = music_structure['downbeats']
            
            # 为每个视频片段生成同步点
            for segment in video_segments:
                # 根据片段类型选择合适的同步策略
                if segment.segment_type in ['intro', 'outro']:
                    # 开场和结尾：同步到下拍
                    target_beats = downbeats
                    sync_type = 'downbeat'
                elif segment.segment_type == 'chorus':
                    # 副歌：同步到强拍
                    target_beats = strong_beats
                    sync_type = 'strong_beat'
                else:
                    # 其他：同步到普通节拍
                    target_beats = beat_points
                    sync_type = 'beat'
                
                # 找到最佳同步点
                best_sync = self._find_best_sync_point(
                    segment, target_beats, sync_type
                )
                
                if best_sync:
                    sync_points.append(best_sync)
            
            # 按时间排序
            sync_points.sort(key=lambda sp: sp.timestamp)
            
            self.logger.info(f"Generated {len(sync_points)} sync points")
            return sync_points
            
        except Exception as e:
            raise AudioProcessingError(f"Sync point generation failed: {str(e)}")
    
    def _find_best_sync_point(self, segment: VideoSegment, 
                             target_beats: List[BeatPoint], 
                             sync_type: str) -> Optional[SyncPoint]:
        """为片段找到最佳同步点"""
        if not target_beats:
            return None
        
        # 在片段时间范围内查找节拍点
        candidate_beats = [
            bp for bp in target_beats
            if segment.start_time - self.sync_tolerance <= bp.timestamp <= segment.end_time + self.sync_tolerance
        ]
        
        if not candidate_beats:
            # 如果没有在范围内的节拍，找最近的
            distances = [abs(bp.timestamp - segment.start_time) for bp in target_beats]
            min_idx = np.argmin(distances)
            best_beat = target_beats[min_idx]
        else:
            # 选择置信度最高的节拍点
            best_beat = max(candidate_beats, key=lambda bp: bp.confidence)
        
        # 计算同步置信度
        time_diff = abs(best_beat.timestamp - segment.start_time)
        time_confidence = max(0.0, 1.0 - time_diff / self.sync_tolerance)
        overall_confidence = (best_beat.confidence + time_confidence) / 2.0
        
        return SyncPoint(
            timestamp=segment.start_time,
            beat_timestamp=best_beat.timestamp,
            confidence=overall_confidence,
            sync_type=sync_type,
            strength=best_beat.strength
        )
    
    @handle_exception
    def apply_auto_sync(self, video_segments: List[VideoSegment], 
                       sync_points: List[SyncPoint],
                       min_confidence: float = 0.5) -> List[VideoSegment]:
        """应用自动同步"""
        try:
            synced_segments = []
            
            for i, segment in enumerate(video_segments):
                # 找到对应的同步点
                matching_sync = None
                for sync_point in sync_points:
                    if abs(sync_point.timestamp - segment.start_time) < self.sync_tolerance:
                        matching_sync = sync_point
                        break
                
                if matching_sync and matching_sync.confidence >= min_confidence:
                    # 应用同步
                    time_adjustment = matching_sync.beat_timestamp - segment.start_time
                    
                    synced_segment = VideoSegment(
                        id=segment.id,
                        start_time=matching_sync.beat_timestamp,
                        duration=segment.duration,
                        end_time=matching_sync.beat_timestamp + segment.duration,
                        file_path=segment.file_path,
                        segment_type=segment.segment_type,
                        priority=segment.priority
                    )
                    
                    self.logger.debug(f"Synced segment {segment.id}: {time_adjustment:+.3f}s adjustment")
                else:
                    # 保持原始时间
                    synced_segment = segment
                    self.logger.debug(f"Segment {segment.id} not synced (low confidence or no match)")
                
                synced_segments.append(synced_segment)
            
            synced_count = sum(1 for i, (orig, synced) in enumerate(zip(video_segments, synced_segments))
                             if abs(orig.start_time - synced.start_time) > 0.01)
            
            self.logger.info(f"Applied auto-sync to {synced_count}/{len(video_segments)} segments")
            return synced_segments
            
        except Exception as e:
            raise VideoProcessingError(f"Auto-sync application failed: {str(e)}")
    
    @handle_exception
    def optimize_segment_timing(self, segments: List[VideoSegment], 
                               music_structure: Dict[str, Any]) -> List[VideoSegment]:
        """优化片段时长以匹配音乐结构"""
        try:
            optimized_segments = []
            beat_points = music_structure['beat_points']
            
            for segment in segments:
                # 根据音乐结构调整片段时长
                optimal_duration = self._calculate_optimal_duration(
                    segment, beat_points, music_structure['beat_pattern']
                )
                
                optimized_segment = VideoSegment(
                    id=segment.id,
                    start_time=segment.start_time,
                    duration=optimal_duration,
                    end_time=segment.start_time + optimal_duration,
                    file_path=segment.file_path,
                    segment_type=segment.segment_type,
                    priority=segment.priority
                )
                
                optimized_segments.append(optimized_segment)
            
            self.logger.info(f"Optimized timing for {len(segments)} segments")
            return optimized_segments
            
        except Exception as e:
            raise VideoProcessingError(f"Segment timing optimization failed: {str(e)}")
    
    def _calculate_optimal_duration(self, segment: VideoSegment, 
                                   beat_points: List[BeatPoint],
                                   beat_pattern: Dict[str, Any]) -> float:
        """计算片段的最优时长"""
        # 获取节拍间隔
        mean_interval = beat_pattern.get('mean_interval', 0.5)
        
        # 根据片段类型设置目标时长
        if segment.segment_type == 'intro':
            target_beats = 8  # 2小节
        elif segment.segment_type == 'outro':
            target_beats = 4  # 1小节
        elif segment.segment_type == 'chorus':
            target_beats = 16  # 4小节
        else:
            target_beats = 8  # 2小节
        
        optimal_duration = target_beats * mean_interval
        
        # 限制在合理范围内
        optimal_duration = max(self.min_segment_duration, 
                             min(self.max_segment_duration, optimal_duration))
        
        return optimal_duration
