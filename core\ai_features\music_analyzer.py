#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音乐分析模块
专门用于SWANKSALON的音乐节拍检测和情感分析
"""

import numpy as np
import librosa
import librosa.display
from typing import Dict, List, Any, Tuple, Optional
from pathlib import Path
import json
from core.logger import get_logger
from core.exceptions import AudioProcessingError

try:
    from scipy import signal
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    print("⚠️ scipy未安装，高级音频分析功能将被限制")


class MusicAnalyzer:
    """音乐分析器 - 专门用于SWANKSALON"""
    
    def __init__(self):
        self.logger = get_logger('music_analyzer')
        self.sample_rate = 22050  # 标准采样率
        self.hop_length = 512
        self.frame_length = 2048
        
        # 缓存分析结果
        self._analysis_cache = {}
    
    def analyze_music_structure(self, audio_path: str) -> Dict[str, Any]:
        """
        分析音乐结构
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            音乐结构分析结果
        """
        if audio_path in self._analysis_cache:
            return self._analysis_cache[audio_path]
        
        try:
            self.logger.info(f"开始分析音乐结构: {Path(audio_path).name}")
            
            # 加载音频
            y, sr = librosa.load(audio_path, sr=self.sample_rate)
            
            # 基础特征提取
            features = self._extract_audio_features(y, sr)
            
            # 节拍检测
            beat_info = self._detect_beats_advanced(y, sr)
            
            # 音乐分段
            segments = self._segment_music(y, sr, beat_info['beat_frames'])
            
            # 情感分析
            emotion_info = self._analyze_emotion(y, sr, features)
            
            # 构建完整结构
            structure = {
                'file_path': audio_path,
                'duration': len(y) / sr,
                'features': features,
                'beat_info': beat_info,
                'segments': segments,
                'emotion': emotion_info,
                'recommendations': self._generate_recommendations(features, beat_info, emotion_info)
            }
            
            # 缓存结果
            self._analysis_cache[audio_path] = structure
            
            self.logger.info(f"音乐结构分析完成: BPM={features['tempo']:.1f}, 情感={emotion_info['primary_emotion']}")
            return structure
            
        except Exception as e:
            self.logger.error(f"音乐结构分析失败: {e}")
            raise AudioProcessingError(f"音乐分析失败: {str(e)}")
    
    def _extract_audio_features(self, y: np.ndarray, sr: int) -> Dict[str, Any]:
        """提取音频特征"""
        try:
            # 节拍和节奏特征
            tempo, beat_frames = librosa.beat.beat_track(y=y, sr=sr, hop_length=self.hop_length)
            
            # 频谱特征
            spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
            spectral_rolloff = librosa.feature.spectral_rolloff(y=y, sr=sr)[0]
            spectral_bandwidth = librosa.feature.spectral_bandwidth(y=y, sr=sr)[0]
            
            # 零交叉率
            zcr = librosa.feature.zero_crossing_rate(y)[0]
            
            # MFCC特征
            mfccs = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13)
            
            # 色度特征
            chroma = librosa.feature.chroma_stft(y=y, sr=sr)
            
            # RMS能量
            rms = librosa.feature.rms(y=y)[0]
            
            # 计算统计特征
            features = {
                'tempo': float(tempo),
                'beat_count': len(beat_frames),
                'duration': len(y) / sr,
                
                # 频谱特征统计
                'spectral_centroid_mean': float(np.mean(spectral_centroids)),
                'spectral_centroid_std': float(np.std(spectral_centroids)),
                'spectral_rolloff_mean': float(np.mean(spectral_rolloff)),
                'spectral_bandwidth_mean': float(np.mean(spectral_bandwidth)),
                
                # 节奏特征
                'zcr_mean': float(np.mean(zcr)),
                'zcr_std': float(np.std(zcr)),
                
                # 能量特征
                'rms_mean': float(np.mean(rms)),
                'rms_std': float(np.std(rms)),
                'dynamic_range': float(np.max(rms) - np.min(rms)),
                
                # MFCC特征
                'mfcc_means': [float(np.mean(mfcc)) for mfcc in mfccs],
                'mfcc_stds': [float(np.std(mfcc)) for mfcc in mfccs],
                
                # 色度特征
                'chroma_means': [float(np.mean(chroma[i])) for i in range(12)],
                
                # 音调估计
                'key': self._estimate_key(chroma),
                'mode': self._estimate_mode(chroma)
            }
            
            return features
            
        except Exception as e:
            self.logger.error(f"音频特征提取失败: {e}")
            return {'tempo': 120.0, 'beat_count': 0, 'duration': 0.0}
    
    def _detect_beats_advanced(self, y: np.ndarray, sr: int) -> Dict[str, Any]:
        """高级节拍检测"""
        try:
            # 基础节拍检测
            tempo, beat_frames = librosa.beat.beat_track(
                y=y, sr=sr, hop_length=self.hop_length, units='frames'
            )
            
            # 转换为时间
            beat_times = librosa.frames_to_time(beat_frames, sr=sr, hop_length=self.hop_length)
            
            # 检测强拍
            strong_beats = self._detect_strong_beats(y, sr, beat_frames)
            
            # 节拍模式分析
            beat_pattern = self._analyze_beat_pattern(beat_times)
            
            # 节拍稳定性分析
            stability = self._analyze_beat_stability(beat_times)
            
            return {
                'tempo': float(tempo),
                'beat_frames': beat_frames.tolist(),
                'beat_times': beat_times.tolist(),
                'strong_beats': strong_beats,
                'beat_pattern': beat_pattern,
                'stability': stability
            }
            
        except Exception as e:
            self.logger.error(f"节拍检测失败: {e}")
            return {
                'tempo': 120.0,
                'beat_frames': [],
                'beat_times': [],
                'strong_beats': [],
                'beat_pattern': {'time_signature': '4/4'},
                'stability': 0.5
            }
    
    def _detect_strong_beats(self, y: np.ndarray, sr: int, beat_frames: np.ndarray) -> List[float]:
        """检测强拍"""
        try:
            # 计算每个节拍的能量
            hop_length = self.hop_length
            beat_energies = []
            
            for i, frame in enumerate(beat_frames):
                start_sample = frame * hop_length
                end_sample = min(start_sample + hop_length * 4, len(y))  # 4帧的窗口
                
                if start_sample < len(y):
                    segment = y[start_sample:end_sample]
                    energy = np.sum(segment ** 2)
                    beat_energies.append(energy)
                else:
                    beat_energies.append(0)
            
            if not beat_energies:
                return []
            
            # 找到能量峰值
            energy_threshold = np.mean(beat_energies) + np.std(beat_energies)
            strong_beat_indices = [i for i, energy in enumerate(beat_energies) if energy > energy_threshold]
            
            # 转换为时间
            beat_times = librosa.frames_to_time(beat_frames, sr=sr, hop_length=hop_length)
            strong_beat_times = [beat_times[i] for i in strong_beat_indices if i < len(beat_times)]
            
            return strong_beat_times
            
        except Exception as e:
            self.logger.error(f"强拍检测失败: {e}")
            return []
    
    def _analyze_beat_pattern(self, beat_times: np.ndarray) -> Dict[str, Any]:
        """分析节拍模式"""
        try:
            if len(beat_times) < 4:
                return {'time_signature': '4/4', 'pattern_strength': 0.0}
            
            # 计算节拍间隔
            intervals = np.diff(beat_times)
            
            # 分析间隔模式
            median_interval = np.median(intervals)
            interval_std = np.std(intervals)
            
            # 简单的拍号检测
            # 基于间隔的一致性来判断
            if interval_std < median_interval * 0.1:
                time_signature = '4/4'  # 稳定的四拍
                pattern_strength = 1.0 - (interval_std / median_interval)
            else:
                time_signature = '4/4'  # 默认
                pattern_strength = 0.5
            
            return {
                'time_signature': time_signature,
                'pattern_strength': float(pattern_strength),
                'median_interval': float(median_interval),
                'interval_stability': float(1.0 - min(1.0, interval_std / median_interval))
            }
            
        except Exception as e:
            self.logger.error(f"节拍模式分析失败: {e}")
            return {'time_signature': '4/4', 'pattern_strength': 0.5}
    
    def _analyze_beat_stability(self, beat_times: np.ndarray) -> float:
        """分析节拍稳定性"""
        try:
            if len(beat_times) < 3:
                return 0.0
            
            intervals = np.diff(beat_times)
            if len(intervals) == 0:
                return 0.0
            
            # 计算间隔的变异系数
            mean_interval = np.mean(intervals)
            std_interval = np.std(intervals)
            
            if mean_interval == 0:
                return 0.0
            
            coefficient_of_variation = std_interval / mean_interval
            stability = max(0.0, 1.0 - coefficient_of_variation)
            
            return float(stability)
            
        except Exception as e:
            self.logger.error(f"节拍稳定性分析失败: {e}")
            return 0.5
    
    def _segment_music(self, y: np.ndarray, sr: int, beat_frames: np.ndarray) -> List[Dict[str, Any]]:
        """音乐分段"""
        try:
            # 简单的基于节拍的分段
            segments = []
            
            if len(beat_frames) < 2:
                return segments
            
            # 每8拍为一个段落
            segment_length = 8
            
            for i in range(0, len(beat_frames), segment_length):
                start_frame = beat_frames[i]
                end_idx = min(i + segment_length, len(beat_frames) - 1)
                end_frame = beat_frames[end_idx]
                
                start_time = librosa.frames_to_time(start_frame, sr=sr, hop_length=self.hop_length)
                end_time = librosa.frames_to_time(end_frame, sr=sr, hop_length=self.hop_length)
                
                # 计算段落特征
                start_sample = int(start_time * sr)
                end_sample = int(end_time * sr)
                segment_audio = y[start_sample:end_sample]
                
                if len(segment_audio) > 0:
                    energy = float(np.mean(segment_audio ** 2))
                    segments.append({
                        'start_time': float(start_time),
                        'end_time': float(end_time),
                        'duration': float(end_time - start_time),
                        'energy': energy,
                        'beat_count': min(segment_length, len(beat_frames) - i)
                    })
            
            return segments
            
        except Exception as e:
            self.logger.error(f"音乐分段失败: {e}")
            return []
    
    def _analyze_emotion(self, y: np.ndarray, sr: int, features: Dict[str, Any]) -> Dict[str, Any]:
        """情感分析"""
        try:
            tempo = features.get('tempo', 120)
            energy = features.get('rms_mean', 0.1)
            brightness = features.get('spectral_centroid_mean', 1000)
            
            # 基于音乐特征的简单情感分类
            if tempo > 130 and energy > 0.15:
                primary_emotion = "EXCITEMENT"
                energy_level = "HIGH"
            elif tempo > 100 and brightness > 2000:
                primary_emotion = "JOY"
                energy_level = "MEDIUM"
            elif tempo < 80:
                primary_emotion = "CALM"
                energy_level = "LOW"
            else:
                primary_emotion = "NEUTRAL"
                energy_level = "MEDIUM"
            
            # 情感强度
            intensity = min(1.0, (tempo / 140.0 + energy / 0.2) / 2.0)
            
            return {
                'primary_emotion': primary_emotion,
                'energy_level': energy_level,
                'intensity': float(intensity),
                'valence': float(0.5 + (tempo - 100) / 200),  # 正负情感
                'arousal': float(energy * 5)  # 激活度
            }
            
        except Exception as e:
            self.logger.error(f"情感分析失败: {e}")
            return {
                'primary_emotion': 'NEUTRAL',
                'energy_level': 'MEDIUM',
                'intensity': 0.5,
                'valence': 0.5,
                'arousal': 0.5
            }
    
    def _estimate_key(self, chroma: np.ndarray) -> str:
        """估计音调"""
        try:
            # 简化的音调估计
            chroma_mean = np.mean(chroma, axis=1)
            key_idx = np.argmax(chroma_mean)
            
            keys = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']
            return keys[key_idx]
            
        except:
            return 'C'
    
    def _estimate_mode(self, chroma: np.ndarray) -> str:
        """估计调式"""
        try:
            # 简化的调式估计
            chroma_mean = np.mean(chroma, axis=1)
            
            # 大调和小调的简单判断
            major_profile = np.array([1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1])
            minor_profile = np.array([1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0])
            
            major_correlation = np.corrcoef(chroma_mean, major_profile)[0, 1]
            minor_correlation = np.corrcoef(chroma_mean, minor_profile)[0, 1]
            
            return 'major' if major_correlation > minor_correlation else 'minor'
            
        except:
            return 'major'
    
    def _generate_recommendations(self, features: Dict[str, Any], 
                                beat_info: Dict[str, Any], 
                                emotion_info: Dict[str, Any]) -> Dict[str, Any]:
        """生成剪辑建议"""
        try:
            tempo = features.get('tempo', 120)
            emotion = emotion_info.get('primary_emotion', 'NEUTRAL')
            stability = beat_info.get('stability', 0.5)
            
            # 基于分析结果生成建议
            recommendations = {
                'optimal_segment_length': max(2.0, 60.0 / tempo * 4),  # 4拍的时长
                'transition_style': self._recommend_transition_style(emotion, tempo),
                'cut_frequency': self._recommend_cut_frequency(tempo, stability),
                'effect_intensity': emotion_info.get('intensity', 0.5),
                'sync_precision': 'high' if stability > 0.8 else 'medium'
            }
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"生成建议失败: {e}")
            return {
                'optimal_segment_length': 2.0,
                'transition_style': 'fade',
                'cut_frequency': 'medium',
                'effect_intensity': 0.5,
                'sync_precision': 'medium'
            }
    
    def _recommend_transition_style(self, emotion: str, tempo: float) -> str:
        """推荐转场风格"""
        if emotion == "EXCITEMENT" and tempo > 130:
            return "dynamic"
        elif emotion == "JOY":
            return "smooth"
        elif emotion == "CALM":
            return "gentle"
        else:
            return "standard"
    
    def _recommend_cut_frequency(self, tempo: float, stability: float) -> str:
        """推荐剪切频率"""
        if tempo > 140 and stability > 0.8:
            return "high"
        elif tempo > 100:
            return "medium"
        else:
            return "low"
