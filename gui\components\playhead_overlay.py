"""
播放头覆盖层组件
独立于时间轴和轨道的播放头指示线，确保显示在最上层
"""

from PySide6.QtWidgets import QWidget
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QPainter, QPen, QColor
import logging

logger = logging.getLogger(__name__)


class PlayheadOverlay(QWidget):
    """播放头覆盖层 - 独立绘制播放头指示线"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 设置为透明背景，鼠标事件穿透
        self.setAttribute(Qt.WA_TransparentForMouseEvents)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 播放头位置（像素）
        self.playhead_x = 0
        
        # 播放头样式
        self.playhead_color = QColor("#00C896")  # 绿色播放头
        self.playhead_width = 2
        
        # 时间轴区域高度（用于绘制三角形）
        self.timeline_height = 48
        
        # 是否显示播放头
        self.visible = True
        
        print("✅ 播放头覆盖层初始化完成")
    
    def set_playhead_position(self, x_position: float):
        """设置播放头位置（像素坐标）"""
        self.playhead_x = x_position
        self.update()  # 触发重绘
    
    def set_timeline_height(self, height: int):
        """设置时间轴高度"""
        self.timeline_height = height
    
    def set_visible(self, visible: bool):
        """设置播放头是否可见"""
        self.visible = visible
        self.update()
    
    def paintEvent(self, event):
        """绘制播放头 - 贯穿整个时间轴和轨道区域"""
        if not self.visible or self.playhead_x <= 0:
            return

        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # 设置播放头颜色和宽度
        pen = QPen(self.playhead_color, self.playhead_width)
        painter.setPen(pen)

        # 绘制垂直线（从顶部到底部，贯穿整个区域）
        line_start_y = 0
        line_end_y = self.height()

        painter.drawLine(int(self.playhead_x), line_start_y, int(self.playhead_x), line_end_y)

        # 在时间轴区域绘制三角形指示器
        if self.timeline_height > 0:
            triangle_size = 6  # 稍微小一点的三角形
            triangle_y = self.timeline_height - triangle_size - 2  # 稍微上移一点

            # 绘制向下的三角形
            triangle_points = [
                (int(self.playhead_x), triangle_y),
                (int(self.playhead_x - triangle_size/2), triangle_y + triangle_size),
                (int(self.playhead_x + triangle_size/2), triangle_y + triangle_size)
            ]

            # 填充三角形
            painter.setBrush(self.playhead_color)
            painter.setPen(Qt.NoPen)

            from PySide6.QtGui import QPolygon
            from PySide6.QtCore import QPoint

            polygon = QPolygon([QPoint(x, y) for x, y in triangle_points])
            painter.drawPolygon(polygon)

        painter.end()

        # 调试输出
        # print(f"🎯 播放头绘制: x={self.playhead_x:.1f}, height={self.height()}, timeline_height={self.timeline_height}")


class PlayheadController:
    """播放头控制器 - 管理播放头位置和显示"""
    
    def __init__(self, overlay: PlayheadOverlay, timeline_widget):
        self.overlay = overlay
        self.timeline_widget = timeline_widget
        
        # 当前时间位置（秒）
        self.current_time = 0.0
        
        # 时间轴参数
        self.timeline_start_x = 128  # 时间轴开始的X坐标
        self.timeline_width = 1792   # 时间轴宽度
        self.total_duration = 60.0   # 总时长（秒）
        
        print("✅ 播放头控制器初始化完成")
    
    def set_timeline_params(self, start_x: int, width: int, duration: float):
        """设置时间轴参数"""
        self.timeline_start_x = start_x
        self.timeline_width = width
        self.total_duration = duration
        print(f"🎯 时间轴参数更新: start_x={start_x}, width={width}, duration={duration:.2f}s")
    
    def set_position(self, time_seconds: float):
        """设置播放头时间位置"""
        self.current_time = time_seconds
        
        # 计算像素位置
        if self.total_duration > 0:
            progress = time_seconds / self.total_duration
            x_position = self.timeline_start_x + (progress * self.timeline_width)
        else:
            x_position = self.timeline_start_x
        
        # 更新覆盖层
        self.overlay.set_playhead_position(x_position)
        
        # 调试输出
        # print(f"🎯 播放头位置更新: {time_seconds:.2f}s -> {x_position:.1f}px")
    
    def get_position(self) -> float:
        """获取当前时间位置"""
        return self.current_time
    
    def set_visible(self, visible: bool):
        """设置播放头是否可见"""
        self.overlay.set_visible(visible)
