#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
媒体浏览器组件
"""

import os
from pathlib import Path
from typing import List

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
    QListWidget, QListWidgetItem, QLabel, QGroupBox,
    QFileDialog, QMessageBox, QMenu
)
from PySide6.QtCore import Qt, Signal, QMimeData
from PySide6.QtGui import QDragEnterEvent, QDropEvent, QIcon, QPixmap

class MediaBrowser(QWidget):
    """媒体浏览器"""
    
    file_selected = Signal(str)
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        self.media_files = []
        
        self.init_ui()
        self.load_recent_files()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("媒体浏览器")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        layout.addWidget(title_label)
        
        # 导入按钮
        import_btn = QPushButton("导入媒体文件")
        import_btn.clicked.connect(self.import_media)
        layout.addWidget(import_btn)
        
        # 媒体列表
        self.media_list = QListWidget()
        self.media_list.setAcceptDrops(True)
        self.media_list.setDragDropMode(QListWidget.InternalMove)
        self.media_list.itemDoubleClicked.connect(self.on_item_double_clicked)
        self.media_list.setContextMenuPolicy(Qt.CustomContextMenu)
        self.media_list.customContextMenuRequested.connect(self.show_context_menu)
        
        # 启用拖拽
        self.setAcceptDrops(True)
        
        layout.addWidget(self.media_list)
        
        # 媒体信息
        info_group = QGroupBox("媒体信息")
        info_layout = QVBoxLayout(info_group)
        
        self.info_label = QLabel("请选择媒体文件")
        self.info_label.setWordWrap(True)
        info_layout.addWidget(self.info_label)
        
        layout.addWidget(info_group)
    
    def import_media(self):
        """导入媒体文件"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self,
            "选择媒体文件",
            self.config.get('paths.last_import_dir'),
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv);;音频文件 (*.mp3 *.wav *.aac);;图片文件 (*.jpg *.png *.bmp);;所有文件 (*.*)"
        )
        
        if file_paths:
            # 保存最后导入目录
            self.config.set('paths.last_import_dir', os.path.dirname(file_paths[0]))
            self.config.save_config()
            
            # 添加文件
            for file_path in file_paths:
                self.add_media_file(file_path)
    
    def add_media_file(self, file_path: str):
        """添加媒体文件"""
        if file_path not in self.media_files:
            self.media_files.append(file_path)
            
            # 创建列表项
            item = QListWidgetItem()
            item.setText(os.path.basename(file_path))
            item.setData(Qt.UserRole, file_path)
            item.setToolTip(file_path)
            
            # 设置图标（根据文件类型）
            icon = self.get_file_icon(file_path)
            if icon:
                item.setIcon(icon)
            
            self.media_list.addItem(item)
            
            # 保存到最近文件
            self.save_recent_files()
    
    def get_file_icon(self, file_path: str) -> QIcon:
        """获取文件图标"""
        ext = Path(file_path).suffix.lower()
        
        # 这里可以根据文件扩展名返回不同的图标
        # 暂时返回空图标
        return QIcon()
    
    def on_item_double_clicked(self, item: QListWidgetItem):
        """双击列表项"""
        file_path = item.data(Qt.UserRole)
        if file_path:
            self.file_selected.emit(file_path)
            self.update_media_info(file_path)
    
    def update_media_info(self, file_path: str):
        """更新媒体信息"""
        try:
            file_info = Path(file_path)
            if not file_info.exists():
                self.info_label.setText("文件不存在")
                return
            
            # 获取文件基本信息
            size = file_info.stat().st_size
            size_str = self.format_file_size(size)
            
            info_text = f"""
            文件名: {file_info.name}
            路径: {file_path}
            大小: {size_str}
            类型: {file_info.suffix.upper()}
            """
            
            self.info_label.setText(info_text.strip())
            
        except Exception as e:
            self.info_label.setText(f"获取文件信息失败: {str(e)}")
    
    def format_file_size(self, size: int) -> str:
        """格式化文件大小"""
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.1f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.1f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.1f} GB"
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        item = self.media_list.itemAt(position)
        if not item:
            return
        
        menu = QMenu(self)
        
        # 播放/加载
        load_action = menu.addAction("加载到时间轴")
        load_action.triggered.connect(lambda: self.load_media(item))
        
        menu.addSeparator()
        
        # 移除
        remove_action = menu.addAction("从列表中移除")
        remove_action.triggered.connect(lambda: self.remove_media(item))
        
        # 在文件管理器中显示
        show_action = menu.addAction("在文件管理器中显示")
        show_action.triggered.connect(lambda: self.show_in_explorer(item))
        
        menu.exec(self.media_list.mapToGlobal(position))
    
    def load_media(self, item: QListWidgetItem):
        """加载媒体到时间轴"""
        file_path = item.data(Qt.UserRole)
        if file_path:
            self.file_selected.emit(file_path)
    
    def remove_media(self, item: QListWidgetItem):
        """从列表中移除媒体"""
        file_path = item.data(Qt.UserRole)
        if file_path in self.media_files:
            self.media_files.remove(file_path)
        
        row = self.media_list.row(item)
        self.media_list.takeItem(row)
        
        self.save_recent_files()
    
    def show_in_explorer(self, item: QListWidgetItem):
        """在文件管理器中显示"""
        file_path = item.data(Qt.UserRole)
        if file_path and os.path.exists(file_path):
            # Windows
            if os.name == 'nt':
                os.startfile(os.path.dirname(file_path))
            # macOS
            elif os.name == 'posix' and os.uname().sysname == 'Darwin':
                os.system(f'open "{os.path.dirname(file_path)}"')
            # Linux
            else:
                os.system(f'xdg-open "{os.path.dirname(file_path)}"')
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
    
    def dropEvent(self, event: QDropEvent):
        """拖拽放置事件"""
        urls = event.mimeData().urls()
        for url in urls:
            if url.isLocalFile():
                file_path = url.toLocalFile()
                # 检查文件类型
                if self.is_supported_file(file_path):
                    self.add_media_file(file_path)
        
        event.acceptProposedAction()
    
    def is_supported_file(self, file_path: str) -> bool:
        """检查是否支持的文件类型"""
        supported_extensions = {
            '.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv',  # 视频
            '.mp3', '.wav', '.aac',  # 音频
            '.jpg', '.jpeg', '.png', '.bmp'  # 图片
        }
        
        ext = Path(file_path).suffix.lower()
        return ext in supported_extensions
    
    def load_recent_files(self):
        """加载最近文件"""
        recent_files = self.config.get('recent_files', [])
        for file_path in recent_files:
            if os.path.exists(file_path):
                self.add_media_file(file_path)
    
    def save_recent_files(self):
        """保存最近文件"""
        # 只保存存在的文件
        existing_files = [f for f in self.media_files if os.path.exists(f)]
        # 限制最多50个文件
        self.config.set('recent_files', existing_files[-50:])
        self.config.save_config()
    
    def clear_media_list(self):
        """清空媒体列表"""
        self.media_list.clear()
        self.media_files.clear()
        self.info_label.setText("请选择媒体文件")
        self.save_recent_files() 