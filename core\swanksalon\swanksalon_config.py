#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SWANKSALON 配置管理系统
专门用于管理理发店视频处理的配置文件
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from core.logger import get_logger


@dataclass
class BeautyParams:
    """美颜参数配置"""
    skin_smooth: float = 0.7
    face_slim: float = 0.5
    eye_brighten: float = 0.3
    teeth_whiten: float = 0.4
    hair_boost: List[str] = None
    min_face_confidence: float = 0.8
    
    def __post_init__(self):
        if self.hair_boost is None:
            self.hair_boost = ['red', 'blue', 'purple']


@dataclass
class TransitionConfig:
    """转场配置"""
    type: str = "scissor_light"
    position: float = 5.0
    duration: float = 0.5
    intensity: float = 1.0
    parameters: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}


@dataclass
class TemplateConfig:
    """模板配置"""
    id: str = "SALON_2024"
    name: str = "理发店时尚模板"
    duration: float = 30.0
    theme_colors: List[str] = None
    transitions: List[TransitionConfig] = None
    beauty_params: BeautyParams = None
    
    def __post_init__(self):
        if self.theme_colors is None:
            self.theme_colors = ["#FF3366", "#33CCFF"]
        if self.transitions is None:
            self.transitions = [
                TransitionConfig("scissor_light", 5.0),
                TransitionConfig("hair_particle", 15.0),
                TransitionConfig("color_dissolve", 25.0)
            ]
        if self.beauty_params is None:
            self.beauty_params = BeautyParams()


@dataclass
class MusicConfig:
    """音乐配置"""
    path: str = ""
    bpm: int = 120
    volume: float = 0.6
    fade_in: float = 1.0
    fade_out: float = 2.0
    beat_points: List[float] = None
    
    def __post_init__(self):
        if self.beat_points is None:
            self.beat_points = []


@dataclass
class VideoConfig:
    """视频配置"""
    path: str = ""
    priority: str = "normal"
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class WatermarkConfig:
    """水印配置"""
    enabled: bool = False
    path: str = ""
    position: str = "bottom-right"
    opacity: float = 0.7
    scale: float = 1.0


@dataclass
class OutputConfig:
    """输出配置"""
    resolution: str = "1080p"
    frame_rate: int = 30
    directory: str = "output_videos"
    format: str = "mp4"
    quality: str = "high"
    watermark: WatermarkConfig = None
    
    def __post_init__(self):
        if self.watermark is None:
            self.watermark = WatermarkConfig()


@dataclass
class SwankSalonConfig:
    """SWANKSALON完整配置"""
    project: str = "SwankSalon_Project"
    template: TemplateConfig = None
    music: MusicConfig = None
    videos: List[VideoConfig] = None
    output: OutputConfig = None
    
    def __post_init__(self):
        if self.template is None:
            self.template = TemplateConfig()
        if self.music is None:
            self.music = MusicConfig()
        if self.videos is None:
            self.videos = []
        if self.output is None:
            self.output = OutputConfig()


class SwankSalonConfigManager:
    """SWANKSALON配置管理器"""
    
    def __init__(self):
        self.logger = get_logger('swanksalon_config')
        self.config_dir = Path.home() / ".swanksalon"
        self.config_dir.mkdir(exist_ok=True)
        
        # 预设配置目录
        self.presets_dir = self.config_dir / "presets"
        self.presets_dir.mkdir(exist_ok=True)
        
        # 创建默认预设
        self._create_default_presets()
    
    def create_default_config(self) -> SwankSalonConfig:
        """创建默认配置"""
        return SwankSalonConfig()
    
    def load_config(self, config_path: str) -> Optional[SwankSalonConfig]:
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            配置对象，失败返回None
        """
        try:
            if not os.path.exists(config_path):
                self.logger.error(f"配置文件不存在: {config_path}")
                return None
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)
            
            # 转换为配置对象
            config = self._dict_to_config(config_dict)
            
            self.logger.info(f"配置文件加载成功: {config_path}")
            return config
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return None
    
    def save_config(self, config: SwankSalonConfig, config_path: str) -> bool:
        """
        保存配置文件
        
        Args:
            config: 配置对象
            config_path: 保存路径
            
        Returns:
            是否成功
        """
        try:
            # 转换为字典
            config_dict = self._config_to_dict(config)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            
            # 保存文件
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=4, ensure_ascii=False)
            
            self.logger.info(f"配置文件保存成功: {config_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            return False
    
    def validate_config(self, config: SwankSalonConfig) -> tuple[bool, List[str]]:
        """
        验证配置
        
        Args:
            config: 配置对象
            
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        # 验证音乐文件
        if not config.music.path:
            errors.append("未设置音乐文件")
        elif not os.path.exists(config.music.path):
            errors.append(f"音乐文件不存在: {config.music.path}")
        
        # 验证视频文件
        if not config.videos:
            errors.append("未添加视频文件")
        else:
            for i, video in enumerate(config.videos):
                if not video.path:
                    errors.append(f"视频 {i+1} 路径为空")
                elif not os.path.exists(video.path):
                    errors.append(f"视频文件不存在: {video.path}")
        
        # 验证输出目录
        if not config.output.directory:
            errors.append("未设置输出目录")
        
        # 验证模板参数
        if config.template.duration <= 0:
            errors.append("模板时长必须大于0")
        
        if not (0 <= config.template.beauty_params.skin_smooth <= 1):
            errors.append("磨皮强度必须在0-1之间")
        
        if not (0 <= config.template.beauty_params.face_slim <= 1):
            errors.append("瘦脸强度必须在0-1之间")
        
        if not (0 <= config.music.volume <= 1):
            errors.append("音乐音量必须在0-1之间")
        
        return len(errors) == 0, errors
    
    def get_preset_list(self) -> List[str]:
        """获取预设列表"""
        try:
            presets = []
            for file_path in self.presets_dir.glob("*.json"):
                presets.append(file_path.stem)
            return sorted(presets)
        except Exception as e:
            self.logger.error(f"获取预设列表失败: {e}")
            return []
    
    def load_preset(self, preset_name: str) -> Optional[SwankSalonConfig]:
        """加载预设配置"""
        preset_path = self.presets_dir / f"{preset_name}.json"
        return self.load_config(str(preset_path))
    
    def save_preset(self, config: SwankSalonConfig, preset_name: str) -> bool:
        """保存预设配置"""
        preset_path = self.presets_dir / f"{preset_name}.json"
        return self.save_config(config, str(preset_path))
    
    def delete_preset(self, preset_name: str) -> bool:
        """删除预设配置"""
        try:
            preset_path = self.presets_dir / f"{preset_name}.json"
            if preset_path.exists():
                preset_path.unlink()
                self.logger.info(f"预设删除成功: {preset_name}")
                return True
            else:
                self.logger.warning(f"预设不存在: {preset_name}")
                return False
        except Exception as e:
            self.logger.error(f"删除预设失败: {e}")
            return False
    
    def _create_default_presets(self):
        """创建默认预设"""
        presets = {
            "经典理发店": SwankSalonConfig(
                project="经典理发店风格",
                template=TemplateConfig(
                    name="经典理发店",
                    duration=30.0,
                    theme_colors=["#8B4513", "#D2691E"],
                    beauty_params=BeautyParams(
                        skin_smooth=0.6,
                        face_slim=0.4,
                        hair_boost=["red", "brown"]
                    )
                )
            ),
            "时尚沙龙": SwankSalonConfig(
                project="时尚沙龙风格",
                template=TemplateConfig(
                    name="时尚沙龙",
                    duration=45.0,
                    theme_colors=["#FF1493", "#00CED1"],
                    beauty_params=BeautyParams(
                        skin_smooth=0.8,
                        face_slim=0.6,
                        hair_boost=["blue", "purple", "pink"]
                    )
                )
            ),
            "简约风格": SwankSalonConfig(
                project="简约风格",
                template=TemplateConfig(
                    name="简约风格",
                    duration=20.0,
                    theme_colors=["#000000", "#FFFFFF"],
                    beauty_params=BeautyParams(
                        skin_smooth=0.5,
                        face_slim=0.3,
                        hair_boost=["black", "gray"]
                    )
                )
            )
        }
        
        for preset_name, config in presets.items():
            preset_path = self.presets_dir / f"{preset_name}.json"
            if not preset_path.exists():
                self.save_config(config, str(preset_path))
    
    def _dict_to_config(self, config_dict: Dict[str, Any]) -> SwankSalonConfig:
        """将字典转换为配置对象"""
        try:
            # 处理嵌套对象
            if 'template' in config_dict:
                template_dict = config_dict['template']
                
                # 处理美颜参数
                if 'beauty_params' in template_dict:
                    beauty_dict = template_dict['beauty_params']
                    template_dict['beauty_params'] = BeautyParams(**beauty_dict)
                
                # 处理转场配置
                if 'transitions' in template_dict:
                    transitions = []
                    for trans_dict in template_dict['transitions']:
                        transitions.append(TransitionConfig(**trans_dict))
                    template_dict['transitions'] = transitions
                
                config_dict['template'] = TemplateConfig(**template_dict)
            
            if 'music' in config_dict:
                config_dict['music'] = MusicConfig(**config_dict['music'])
            
            if 'videos' in config_dict:
                videos = []
                for video_dict in config_dict['videos']:
                    videos.append(VideoConfig(**video_dict))
                config_dict['videos'] = videos
            
            if 'output' in config_dict:
                output_dict = config_dict['output']
                if 'watermark' in output_dict:
                    output_dict['watermark'] = WatermarkConfig(**output_dict['watermark'])
                config_dict['output'] = OutputConfig(**output_dict)
            
            return SwankSalonConfig(**config_dict)
            
        except Exception as e:
            self.logger.error(f"配置转换失败: {e}")
            return self.create_default_config()
    
    def _config_to_dict(self, config: SwankSalonConfig) -> Dict[str, Any]:
        """将配置对象转换为字典"""
        try:
            # 使用dataclass的asdict功能
            config_dict = asdict(config)
            
            # 清理None值
            return self._clean_dict(config_dict)
            
        except Exception as e:
            self.logger.error(f"配置序列化失败: {e}")
            return {}
    
    def _clean_dict(self, d: Dict[str, Any]) -> Dict[str, Any]:
        """清理字典中的None值"""
        if isinstance(d, dict):
            return {k: self._clean_dict(v) for k, v in d.items() if v is not None}
        elif isinstance(d, list):
            return [self._clean_dict(item) for item in d if item is not None]
        else:
            return d
    
    def export_config_template(self, output_path: str) -> bool:
        """导出配置模板"""
        try:
            template_config = self.create_default_config()
            return self.save_config(template_config, output_path)
        except Exception as e:
            self.logger.error(f"导出配置模板失败: {e}")
            return False
    
    def get_config_info(self, config: SwankSalonConfig) -> Dict[str, Any]:
        """获取配置信息摘要"""
        try:
            return {
                'project_name': config.project,
                'template_name': config.template.name,
                'template_duration': config.template.duration,
                'video_count': len(config.videos),
                'music_file': os.path.basename(config.music.path) if config.music.path else "未设置",
                'output_resolution': config.output.resolution,
                'output_fps': config.output.frame_rate,
                'beauty_enabled': True,
                'transition_count': len(config.template.transitions)
            }
        except Exception as e:
            self.logger.error(f"获取配置信息失败: {e}")
            return {}
