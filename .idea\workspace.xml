<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="d7a62030-b969-42bb-add0-73ed53d57474" name="Changes" comment="feat: 暂存" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="dev-test" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="MIXED" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2zHBybmmt6y2oAk3PvSCXhHftgv" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;dev&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/Project/Code/Python/VIDEOCUT&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="VIDEOCUT" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="VIDEOCUT" />
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="d7a62030-b969-42bb-add0-73ed53d57474" name="Changes" comment="" />
      <created>1751378373450</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751378373450</updated>
      <workItem from="1751378374497" duration="146000" />
      <workItem from="1751379373469" duration="29000" />
      <workItem from="1751380082884" duration="684000" />
      <workItem from="1751384997592" duration="39000" />
      <workItem from="1751698738448" duration="790000" />
      <workItem from="1751713162601" duration="332000" />
      <workItem from="1751725305650" duration="164000" />
      <workItem from="1751736541846" duration="6247000" />
      <workItem from="1751747010284" duration="95000" />
      <workItem from="1751783936704" duration="2625000" />
      <workItem from="1751814042824" duration="1269000" />
      <workItem from="1751851296732" duration="16000" />
      <workItem from="1751991881834" duration="249000" />
      <workItem from="1752025097519" duration="5000" />
      <workItem from="1752317346180" duration="3565000" />
      <workItem from="1752332025931" duration="696000" />
      <workItem from="1752332739116" duration="71000" />
      <workItem from="1752332817899" duration="8530000" />
      <workItem from="1752495656739" duration="4573000" />
      <workItem from="1752596771385" duration="1310000" />
      <workItem from="1752629063068" duration="582000" />
      <workItem from="1752670467094" duration="598000" />
      <workItem from="1752675536621" duration="595000" />
      <workItem from="1752676765460" duration="596000" />
      <workItem from="1752716602151" duration="1761000" />
      <workItem from="1752853367209" duration="156000" />
      <workItem from="1753033675913" duration="41000" />
    </task>
    <task id="LOCAL-00001" summary="feat: 轨道帧预览,裁剪游标">
      <option name="closed" value="true" />
      <created>1751384786605</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1751384786605</updated>
    </task>
    <task id="LOCAL-00002" summary="feat: 轨道裁剪,初版暂存">
      <option name="closed" value="true" />
      <created>1751722594486</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1751722594486</updated>
    </task>
    <task id="LOCAL-00003" summary="feat: 轨道裁剪,初版暂存">
      <option name="closed" value="true" />
      <created>1751724307661</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1751724307661</updated>
    </task>
    <task id="LOCAL-00004" summary="feat: 拖放素材删除蓝色提示线">
      <option name="closed" value="true" />
      <created>1751737751585</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1751737751585</updated>
    </task>
    <task id="LOCAL-00005" summary="feat: 选项卡导入素材显示问题">
      <option name="closed" value="true" />
      <created>1751739154271</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1751739154271</updated>
    </task>
    <task id="LOCAL-00006" summary="feat: 按钮显示问题">
      <option name="closed" value="true" />
      <created>1751742754873</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1751742754873</updated>
    </task>
    <task id="LOCAL-00007" summary="feat: 按钮显示问题">
      <option name="closed" value="true" />
      <created>1751743135017</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1751743135017</updated>
    </task>
    <task id="LOCAL-00008" summary="feat: 时间轴,轨道素材,预览对应">
      <option name="closed" value="true" />
      <created>1751744990590</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1751744990590</updated>
    </task>
    <task id="LOCAL-00009" summary="feat: 时间轴,轨道素材,预览对应">
      <option name="closed" value="true" />
      <created>1751746530137</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1751746530137</updated>
    </task>
    <task id="LOCAL-00010" summary="feat: markdown提示修改版">
      <option name="closed" value="true" />
      <created>1752322668929</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1752322668929</updated>
    </task>
    <task id="LOCAL-00011" summary="feat: 拆分文件">
      <option name="closed" value="true" />
      <created>1752332110437</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1752332110437</updated>
    </task>
    <task id="LOCAL-00012" summary="feat: 拆分文件">
      <option name="closed" value="true" />
      <created>1752339397665</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1752339397665</updated>
    </task>
    <task id="LOCAL-00013" summary="feat: 清除无用文件">
      <option name="closed" value="true" />
      <created>1752340885831</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1752340885831</updated>
    </task>
    <task id="LOCAL-00014" summary="feat: 文件归类">
      <option name="closed" value="true" />
      <created>1752343773150</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1752343773150</updated>
    </task>
    <task id="LOCAL-00015" summary="feat: 样式优化">
      <option name="closed" value="true" />
      <created>1752396964280</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1752396964280</updated>
    </task>
    <task id="LOCAL-00016" summary="feat: 样式优化">
      <option name="closed" value="true" />
      <created>1752410473622</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1752410473622</updated>
    </task>
    <task id="LOCAL-00017" summary="feat: 拆分">
      <option name="closed" value="true" />
      <created>1752495759021</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1752495759021</updated>
    </task>
    <task id="LOCAL-00018" summary="feat: 拆分修复">
      <option name="closed" value="true" />
      <created>1752505220115</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1752505220115</updated>
    </task>
    <task id="LOCAL-00019" summary="feat: 轨道修复">
      <option name="closed" value="true" />
      <created>1752596822335</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1752596822335</updated>
    </task>
    <task id="LOCAL-00020" summary="feat: 轨道修复">
      <option name="closed" value="true" />
      <created>1752600314095</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1752600314095</updated>
    </task>
    <task id="LOCAL-00021" summary="feat: 暂存">
      <option name="closed" value="true" />
      <created>1753033707782</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1753033707782</updated>
    </task>
    <option name="localTasksCounter" value="22" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="dev" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="feat: 轨道帧预览,裁剪游标" />
    <MESSAGE value="feat: 轨道裁剪,初版暂存" />
    <MESSAGE value="feat: 拖放素材删除蓝色提示线" />
    <MESSAGE value="feat: 选项卡导入素材显示问题" />
    <MESSAGE value="feat: 按钮显示问题" />
    <MESSAGE value="feat: 时间轴,轨道素材,预览对应" />
    <MESSAGE value="feat: markdown提示修改版" />
    <MESSAGE value="feat: 拆分文件" />
    <MESSAGE value="feat: 清除无用文件" />
    <MESSAGE value="feat: 文件归类" />
    <MESSAGE value="feat: 样式优化" />
    <MESSAGE value="feat: 拆分" />
    <MESSAGE value="feat: 拆分修复" />
    <MESSAGE value="feat: 轨道修复" />
    <MESSAGE value="feat: 暂存" />
    <option name="LAST_COMMIT_MESSAGE" value="feat: 暂存" />
  </component>
</project>