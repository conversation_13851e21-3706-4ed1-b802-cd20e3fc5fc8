#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
弹窗样式管理器
统一管理所有弹窗的样式，确保视觉一致性
"""

from PySide6.QtWidgets import QDialog, QMessageBox, QFileDialog, QApplication, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QWidget
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QPalette, QColor, QFont

class DialogStyleManager:
    """弹窗样式管理器"""
    
    # 颜色定义
    HEADER_BG = "#333333"      # 51,51,51 - 头部背景色
    CONTENT_BG = "#4B4D52"     # 75,77,82 - 内容背景色  
    TEXT_COLOR = "#B6ECFF"     # 182,236,255 - 字体颜色
    BORDER_COLOR = "#666666"   # 边框颜色
    BUTTON_BG = "#555555"      # 按钮背景色
    BUTTON_HOVER = "#666666"   # 按钮悬停色
    BUTTON_PRESSED = "#444444" # 按钮按下色
    
    @classmethod
    def get_dialog_style(cls) -> str:
        """获取对话框样式"""
        return f"""
            QDialog {{
                background-color: {cls.CONTENT_BG};
                color: {cls.TEXT_COLOR};
                border: 1px solid {cls.BORDER_COLOR};
                border-radius: 8px;
            }}

            /* 注意：QDialog::title 在Qt中不起作用，需要使用自定义标题栏 */
            
            /* 标签页样式 */
            QTabWidget::pane {{
                border: 1px solid {cls.BORDER_COLOR};
                background-color: {cls.CONTENT_BG};
                border-radius: 4px;
            }}
            
            QTabBar::tab {{
                background-color: {cls.HEADER_BG};
                color: {cls.TEXT_COLOR};
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                border: 1px solid {cls.BORDER_COLOR};
            }}
            
            QTabBar::tab:selected {{
                background-color: {cls.CONTENT_BG};
                border-bottom: none;
            }}
            
            QTabBar::tab:hover {{
                background-color: {cls.BUTTON_HOVER};
            }}
            
            /* 按钮样式 - 排除主窗口控制按钮 */
            QDialog QPushButton, QMessageBox QPushButton, QFileDialog QPushButton {{
                background-color: {cls.BUTTON_BG};
                color: {cls.TEXT_COLOR};
                border: 1px solid {cls.BORDER_COLOR};
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }}

            QDialog QPushButton:hover, QMessageBox QPushButton:hover, QFileDialog QPushButton:hover {{
                background-color: {cls.BUTTON_HOVER};
            }}

            QDialog QPushButton:pressed, QMessageBox QPushButton:pressed, QFileDialog QPushButton:pressed {{
                background-color: {cls.BUTTON_PRESSED};
            }}

            QDialog QPushButton:default, QMessageBox QPushButton:default, QFileDialog QPushButton:default {{
                background-color: #00C896;
                border-color: #00C896;
            }}

            QDialog QPushButton:default:hover, QMessageBox QPushButton:default:hover, QFileDialog QPushButton:default:hover {{
                background-color: #00E8A8;
            }}
            
            /* 输入框样式 - 限制在对话框内 */
            QDialog QLineEdit, QDialog QTextEdit, QDialog QPlainTextEdit,
            QMessageBox QLineEdit, QMessageBox QTextEdit, QMessageBox QPlainTextEdit,
            QFileDialog QLineEdit, QFileDialog QTextEdit, QFileDialog QPlainTextEdit {{
                background-color: {cls.HEADER_BG};
                color: {cls.TEXT_COLOR};
                border: 1px solid {cls.BORDER_COLOR};
                border-radius: 4px;
                padding: 6px;
            }}

            QDialog QLineEdit:focus, QDialog QTextEdit:focus, QDialog QPlainTextEdit:focus,
            QMessageBox QLineEdit:focus, QMessageBox QTextEdit:focus, QMessageBox QPlainTextEdit:focus,
            QFileDialog QLineEdit:focus, QFileDialog QTextEdit:focus, QFileDialog QPlainTextEdit:focus {{
                border-color: #00C896;
            }}

            /* 下拉框样式 - 限制在对话框内 */
            QDialog QComboBox, QMessageBox QComboBox, QFileDialog QComboBox {{
                background-color: {cls.HEADER_BG};
                color: {cls.TEXT_COLOR};
                border: 1px solid {cls.BORDER_COLOR};
                border-radius: 4px;
                padding: 6px;
                min-width: 100px;
            }}

            QDialog QComboBox:hover, QMessageBox QComboBox:hover, QFileDialog QComboBox:hover {{
                border-color: #00C896;
            }}

            QDialog QComboBox::drop-down, QMessageBox QComboBox::drop-down, QFileDialog QComboBox::drop-down {{
                border: none;
                width: 20px;
            }}

            QDialog QComboBox::down-arrow, QMessageBox QComboBox::down-arrow, QFileDialog QComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid {cls.TEXT_COLOR};
                margin-right: 5px;
            }}

            QDialog QComboBox QAbstractItemView, QMessageBox QComboBox QAbstractItemView, QFileDialog QComboBox QAbstractItemView {{
                background-color: {cls.HEADER_BG};
                color: {cls.TEXT_COLOR};
                border: 1px solid {cls.BORDER_COLOR};
                selection-background-color: #00C896;
                selection-color: white;
            }}

            /* 标签样式 - 限制在对话框内 */
            QDialog QLabel, QMessageBox QLabel, QFileDialog QLabel {{
                color: {cls.TEXT_COLOR};
                background-color: transparent;
            }}
            
            /* 分组框样式 */
            QGroupBox {{
                color: {cls.TEXT_COLOR};
                border: 2px solid {cls.BORDER_COLOR};
                border-radius: 6px;
                margin-top: 8px;
                font-weight: bold;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 8px 0 8px;
                background-color: {cls.CONTENT_BG};
            }}
            
            /* 滑块样式 */
            QSlider::groove:horizontal {{
                border: 1px solid {cls.BORDER_COLOR};
                height: 4px;
                background: {cls.HEADER_BG};
                border-radius: 2px;
            }}
            
            QSlider::handle:horizontal {{
                background: #00C896;
                border: 1px solid #00C896;
                width: 16px;
                height: 16px;
                margin: -6px 0;
                border-radius: 8px;
            }}
            
            QSlider::handle:horizontal:hover {{
                background: #00E8A8;
                border-color: #00E8A8;
            }}
            
            /* 复选框样式 */
            QCheckBox {{
                color: {cls.TEXT_COLOR};
                spacing: 8px;
            }}
            
            QCheckBox::indicator {{
                width: 16px;
                height: 16px;
                border: 2px solid {cls.BORDER_COLOR};
                border-radius: 3px;
                background-color: {cls.HEADER_BG};
            }}
            
            QCheckBox::indicator:checked {{
                background-color: #00C896;
                border-color: #00C896;
            }}
            
            /* 数字输入框样式 */
            QSpinBox, QDoubleSpinBox {{
                background-color: {cls.HEADER_BG};
                color: {cls.TEXT_COLOR};
                border: 1px solid {cls.BORDER_COLOR};
                border-radius: 4px;
                padding: 6px;
            }}
            
            QSpinBox:focus, QDoubleSpinBox:focus {{
                border-color: #00C896;
            }}
            
            /* 列表样式 */
            QListWidget {{
                background-color: {cls.HEADER_BG};
                color: {cls.TEXT_COLOR};
                border: 1px solid {cls.BORDER_COLOR};
                border-radius: 4px;
            }}
            
            QListWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {cls.BORDER_COLOR};
            }}
            
            QListWidget::item:selected {{
                background-color: #00C896;
                color: white;
            }}
            
            QListWidget::item:hover {{
                background-color: {cls.BUTTON_HOVER};
            }}
            
            /* 进度条样式 */
            QProgressBar {{
                border: 1px solid {cls.BORDER_COLOR};
                border-radius: 4px;
                text-align: center;
                background-color: {cls.HEADER_BG};
                color: {cls.TEXT_COLOR};
            }}
            
            QProgressBar::chunk {{
                background-color: #00C896;
                border-radius: 3px;
            }}
            
            /* 滚动条样式 */
            QScrollBar:vertical {{
                background-color: {cls.HEADER_BG};
                width: 12px;
                border-radius: 6px;
            }}
            
            QScrollBar::handle:vertical {{
                background-color: {cls.BORDER_COLOR};
                border-radius: 6px;
                min-height: 20px;
            }}
            
            QScrollBar::handle:vertical:hover {{
                background-color: {cls.BUTTON_HOVER};
            }}
            
            QScrollBar:horizontal {{
                background-color: {cls.HEADER_BG};
                height: 12px;
                border-radius: 6px;
            }}
            
            QScrollBar::handle:horizontal {{
                background-color: {cls.BORDER_COLOR};
                border-radius: 6px;
                min-width: 20px;
            }}
            
            QScrollBar::handle:horizontal:hover {{
                background-color: {cls.BUTTON_HOVER};
            }}
        """
    
    @classmethod
    def apply_dialog_style(cls, dialog: QDialog):
        """应用对话框样式"""
        dialog.setStyleSheet(cls.get_dialog_style())
        
        # 设置窗口标志，去掉默认标题栏
        dialog.setWindowFlags(
            Qt.WindowType.Dialog | 
            Qt.WindowType.CustomizeWindowHint | 
            Qt.WindowType.WindowTitleHint |
            Qt.WindowType.WindowCloseButtonHint
        )
    
    @classmethod
    def setup_application_style(cls):
        """设置应用程序全局样式 - 仅设置调色板，不影响主窗口样式"""
        app = QApplication.instance()
        if app:
            # 🔧 修改：不设置全局样式表，避免影响主窗口控制按钮
            # app.setStyleSheet(cls.get_dialog_style())

            # 设置调色板 - 仅影响对话框
            palette = QPalette()
            palette.setColor(QPalette.ColorRole.Window, QColor(cls.CONTENT_BG))
            palette.setColor(QPalette.ColorRole.WindowText, QColor(cls.TEXT_COLOR))
            palette.setColor(QPalette.ColorRole.Base, QColor(cls.HEADER_BG))
            palette.setColor(QPalette.ColorRole.AlternateBase, QColor(cls.BUTTON_BG))
            palette.setColor(QPalette.ColorRole.ToolTipBase, QColor(cls.HEADER_BG))
            palette.setColor(QPalette.ColorRole.ToolTipText, QColor(cls.TEXT_COLOR))
            palette.setColor(QPalette.ColorRole.Text, QColor(cls.TEXT_COLOR))
            palette.setColor(QPalette.ColorRole.Button, QColor(cls.BUTTON_BG))
            palette.setColor(QPalette.ColorRole.ButtonText, QColor(cls.TEXT_COLOR))
            palette.setColor(QPalette.ColorRole.BrightText, QColor("#FFFFFF"))
            palette.setColor(QPalette.ColorRole.Link, QColor("#00C896"))
            palette.setColor(QPalette.ColorRole.Highlight, QColor("#00C896"))
            palette.setColor(QPalette.ColorRole.HighlightedText, QColor("#FFFFFF"))

            # 🔧 修改：不设置全局调色板，避免影响主窗口
            # app.setPalette(palette)


class CustomDialog(QDialog):
    """自定义对话框 - 带有深色标题栏"""

    def __init__(self, parent=None, title="对话框"):
        super().__init__(parent)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)

        # 设置基本样式
        self.setStyleSheet(f"""
            CustomDialog {{
                background-color: {DialogStyleManager.CONTENT_BG};
                border: 1px solid {DialogStyleManager.BORDER_COLOR};
                border-radius: 8px;
            }}
        """)

        # 创建主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)

        # 创建标题栏
        self.create_title_bar(title)

        # 创建内容区域
        self.content_widget = QWidget()
        self.content_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {DialogStyleManager.CONTENT_BG};
                border-bottom-left-radius: 8px;
                border-bottom-right-radius: 8px;
            }}
        """)
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(16, 16, 16, 16)

        self.main_layout.addWidget(self.content_widget)

        # 拖拽相关
        self.drag_position = None

    def create_title_bar(self, title):
        """创建自定义标题栏"""
        self.title_bar = QWidget()
        self.title_bar.setFixedHeight(40)
        self.title_bar.setStyleSheet(f"""
            QWidget {{
                background-color: {DialogStyleManager.HEADER_BG};
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                border-bottom: 1px solid {DialogStyleManager.BORDER_COLOR};
            }}
        """)

        title_layout = QHBoxLayout(self.title_bar)
        title_layout.setContentsMargins(16, 0, 8, 0)

        # 标题标签
        self.title_label = QLabel(title)
        self.title_label.setStyleSheet(f"""
            QLabel {{
                color: {DialogStyleManager.TEXT_COLOR};
                font-weight: bold;
                font-size: 14px;
                background-color: transparent;
            }}
        """)
        title_layout.addWidget(self.title_label)

        title_layout.addStretch()

        # 关闭按钮
        self.close_btn = QPushButton("×")
        self.close_btn.setFixedSize(32, 32)
        self.close_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                color: {DialogStyleManager.TEXT_COLOR};
                border: none;
                border-radius: 4px;
                font-size: 18px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #E81123;
                color: #FFFFFF;
            }}
            QPushButton:pressed {{
                background-color: #C50E1F;
                color: #FFFFFF;
            }}
        """)
        self.close_btn.clicked.connect(self.reject)
        title_layout.addWidget(self.close_btn)

        self.main_layout.addWidget(self.title_bar)

    def setWindowTitle(self, title):
        """设置窗口标题"""
        super().setWindowTitle(title)
        if hasattr(self, 'title_label'):
            self.title_label.setText(title)

    def mousePressEvent(self, event):
        """鼠标按下事件 - 用于拖拽"""
        if event.button() == Qt.MouseButton.LeftButton and self.title_bar.geometry().contains(event.position().toPoint()):
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 用于拖拽"""
        if event.buttons() == Qt.MouseButton.LeftButton and self.drag_position is not None:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        self.drag_position = None
