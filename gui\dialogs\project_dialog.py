"""
项目创建/选择对话框
"""

import os
from datetime import datetime
from pathlib import Path
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QTextEdit,
    QPushButton, QListWidget, QListWidgetItem, QTabWidget, QWidget,
    QMessageBox, QProgressBar, QFrame, QScrollArea, QGridLayout
)
from PySide6.QtCore import Qt, Signal, QTimer, QThread
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor, QBrush

class ProjectLoadThread(QThread):
    """项目加载线程"""
    progress_updated = Signal(int, str)
    load_completed = Signal(dict)
    load_failed = Signal(str)
    
    def __init__(self, project_manager, project_path=None):
        super().__init__()
        self.project_manager = project_manager
        self.project_path = project_path
        
    def run(self):
        try:
            self.progress_updated.emit(10, "初始化项目管理器...")
            self.msleep(200)
            
            if self.project_path:
                self.progress_updated.emit(30, "加载项目文件...")
                project_data = self.project_manager.load_project(self.project_path)
                self.msleep(300)
                
                self.progress_updated.emit(60, "解析项目数据...")
                self.msleep(200)
                
                self.progress_updated.emit(80, "应用项目设置...")
                self.msleep(200)
                
                self.progress_updated.emit(100, "项目加载完成")
                self.msleep(100)
                
                if project_data:
                    self.load_completed.emit(project_data)
                else:
                    self.load_failed.emit("项目文件加载失败")
            else:
                self.progress_updated.emit(30, "自动加载最后项目...")
                project_data = self.project_manager.auto_load_last_project()
                self.msleep(300)
                
                if not project_data:
                    self.progress_updated.emit(60, "创建默认项目...")
                    project_data = self.project_manager.create_new_project("默认项目")
                    self.msleep(200)
                
                self.progress_updated.emit(80, "应用项目设置...")
                self.msleep(200)
                
                self.progress_updated.emit(100, "项目加载完成")
                self.msleep(100)
                
                self.load_completed.emit(project_data)
                
        except Exception as e:
            self.load_failed.emit(str(e))

class ProjectDialog(QDialog):
    """项目创建/选择对话框"""
    
    project_selected = Signal(dict)  # 项目选择信号
    
    def __init__(self, project_manager, parent=None):
        super().__init__(parent)
        self.project_manager = project_manager
        self.selected_project_data = None
        
        self.setWindowTitle("SWANKSALON - 项目管理")
        self.setFixedSize(800, 600)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        
        self.setup_ui()
        self.load_recent_projects()
        
    def setup_ui(self):
        """设置UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建主容器
        main_container = QWidget()
        main_container.setStyleSheet("""
            QWidget {
                background-color: #1A1A1A;
                border-radius: 12px;
            }
        """)
        container_layout = QVBoxLayout(main_container)
        container_layout.setContentsMargins(20, 20, 20, 20)
        container_layout.setSpacing(20)
        
        # 标题
        title_label = QLabel("SWANKSALON 项目管理")
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 24px;
                font-weight: bold;
                padding: 10px 0px;
            }
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        container_layout.addWidget(title_label)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #333333;
                border-radius: 8px;
                background-color: #2A2A2A;
            }
            QTabBar::tab {
                background-color: #333333;
                color: #FFFFFF;
                padding: 12px 24px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }
            QTabBar::tab:selected {
                background-color: #00C896;
                color: #FFFFFF;
            }
            QTabBar::tab:hover {
                background-color: #444444;
            }
        """)
        
        # 新建项目标签页
        self.create_new_project_tab()
        
        # 打开项目标签页
        self.create_open_project_tab()
        
        container_layout.addWidget(self.tab_widget)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #666666;
                color: #FFFFFF;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #777777;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        # 确定按钮
        self.ok_btn = QPushButton("创建项目")
        self.ok_btn.setStyleSheet("""
            QPushButton {
                background-color: #00C896;
                color: #FFFFFF;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #00E8A8;
            }
            QPushButton:disabled {
                background-color: #555555;
                color: #999999;
            }
        """)
        self.ok_btn.clicked.connect(self.handle_ok_clicked)
        button_layout.addWidget(self.ok_btn)
        
        container_layout.addLayout(button_layout)
        main_layout.addWidget(main_container)
        
    def create_new_project_tab(self):
        """创建新建项目标签页"""
        new_project_widget = QWidget()
        layout = QVBoxLayout(new_project_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(16)
        
        # 项目名称
        name_label = QLabel("项目名称:")
        name_label.setStyleSheet("color: #FFFFFF; font-size: 14px; font-weight: bold;")
        layout.addWidget(name_label)
        
        self.project_name_edit = QLineEdit()
        self.project_name_edit.setPlaceholderText("请输入项目名称")
        self.project_name_edit.setText(f"项目_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        self.project_name_edit.setStyleSheet("""
            QLineEdit {
                background-color: #333333;
                color: #FFFFFF;
                border: 2px solid #555555;
                border-radius: 6px;
                padding: 12px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #00C896;
            }
        """)
        self.project_name_edit.textChanged.connect(self.validate_new_project)
        layout.addWidget(self.project_name_edit)
        
        # 项目描述
        desc_label = QLabel("项目描述:")
        desc_label.setStyleSheet("color: #FFFFFF; font-size: 14px; font-weight: bold;")
        layout.addWidget(desc_label)
        
        self.project_desc_edit = QTextEdit()
        self.project_desc_edit.setPlaceholderText("请输入项目描述（可选）")
        self.project_desc_edit.setMaximumHeight(100)
        self.project_desc_edit.setStyleSheet("""
            QTextEdit {
                background-color: #333333;
                color: #FFFFFF;
                border: 2px solid #555555;
                border-radius: 6px;
                padding: 12px;
                font-size: 14px;
            }
            QTextEdit:focus {
                border-color: #00C896;
            }
        """)
        layout.addWidget(self.project_desc_edit)
        
        layout.addStretch()
        
        self.tab_widget.addTab(new_project_widget, "新建项目")
        
    def create_open_project_tab(self):
        """创建打开项目标签页"""
        open_project_widget = QWidget()
        layout = QVBoxLayout(open_project_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(16)
        
        # 最近项目标签
        recent_label = QLabel("最近项目:")
        recent_label.setStyleSheet("color: #FFFFFF; font-size: 14px; font-weight: bold;")
        layout.addWidget(recent_label)
        
        # 项目列表
        self.project_list = QListWidget()
        self.project_list.setStyleSheet("""
            QListWidget {
                background-color: #333333;
                color: #FFFFFF;
                border: 2px solid #555555;
                border-radius: 6px;
                padding: 8px;
            }
            QListWidget::item {
                padding: 12px;
                border-radius: 4px;
                margin: 2px;
            }
            QListWidget::item:selected {
                background-color: #00C896;
                color: #FFFFFF;
            }
            QListWidget::item:hover {
                background-color: #444444;
            }
        """)
        self.project_list.itemClicked.connect(self.on_project_selected)
        self.project_list.itemDoubleClicked.connect(self.on_project_double_clicked)
        layout.addWidget(self.project_list)
        
        # 浏览按钮
        browse_btn = QPushButton("浏览其他项目...")
        browse_btn.setStyleSheet("""
            QPushButton {
                background-color: #444444;
                color: #FFFFFF;
                border: none;
                border-radius: 6px;
                padding: 12px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #555555;
            }
        """)
        browse_btn.clicked.connect(self.browse_project)
        layout.addWidget(browse_btn)
        
        self.tab_widget.addTab(open_project_widget, "打开项目")
        
    def load_recent_projects(self):
        """加载最近项目"""
        try:
            recent_projects = self.project_manager.get_recent_projects(10)
            
            for project in recent_projects:
                item = QListWidgetItem()
                
                # 项目信息
                name = project.get("name", "未知项目")
                path = project.get("path", "")
                modified_time = project.get("modified_time", "")
                
                # 格式化时间
                try:
                    if modified_time:
                        dt = datetime.fromisoformat(modified_time.replace('Z', '+00:00'))
                        time_str = dt.strftime("%Y-%m-%d %H:%M")
                    else:
                        time_str = "未知时间"
                except:
                    time_str = "未知时间"
                
                item.setText(f"{name}\n{time_str}")
                item.setData(Qt.ItemDataRole.UserRole, project)
                
                self.project_list.addItem(item)
                
        except Exception as e:
            print(f"❌ 加载最近项目失败: {e}")
            
    def validate_new_project(self):
        """验证新项目输入"""
        name = self.project_name_edit.text().strip()
        self.ok_btn.setEnabled(bool(name))
        
    def on_project_selected(self, item):
        """项目选择事件"""
        self.ok_btn.setText("打开项目")
        self.ok_btn.setEnabled(True)
        
    def on_project_double_clicked(self, item):
        """项目双击事件"""
        self.handle_ok_clicked()
        
    def browse_project(self):
        """浏览项目文件"""
        from PySide6.QtWidgets import QFileDialog
        
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择项目文件",
            str(self.project_manager.projects_dir),
            "项目文件 (*.json);;所有文件 (*)"
        )
        
        if file_path:
            try:
                # 加载项目信息
                import json
                with open(file_path, 'r', encoding='utf-8') as f:
                    project_data = json.load(f)
                
                project_info = {
                    "path": file_path,
                    "name": project_data.get("project_info", {}).get("name", "未知项目"),
                    "modified_time": project_data.get("project_info", {}).get("modified_time", "")
                }
                
                # 添加到列表
                item = QListWidgetItem()
                name = project_info["name"]
                item.setText(f"{name}\n{file_path}")
                item.setData(Qt.ItemDataRole.UserRole, project_info)
                
                self.project_list.addItem(item)
                self.project_list.setCurrentItem(item)
                self.on_project_selected(item)
                
            except Exception as e:
                QMessageBox.warning(self, "错误", f"无法加载项目文件:\n{e}")
                
    def handle_ok_clicked(self):
        """处理确定按钮点击"""
        current_tab = self.tab_widget.currentIndex()
        
        if current_tab == 0:  # 新建项目
            self.create_new_project()
        else:  # 打开项目
            self.open_selected_project()
            
    def create_new_project(self):
        """创建新项目"""
        name = self.project_name_edit.text().strip()
        description = self.project_desc_edit.toPlainText().strip()
        
        if not name:
            QMessageBox.warning(self, "警告", "请输入项目名称")
            return
            
        try:
            # 创建项目数据
            project_data = self.project_manager.create_new_project(name)
            
            # 添加描述
            if description:
                project_data["project_info"]["description"] = description
                self.project_manager.save_project()
            
            self.selected_project_data = project_data
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"创建项目失败:\n{e}")
            
    def open_selected_project(self):
        """打开选中的项目"""
        current_item = self.project_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "请选择一个项目")
            return
            
        try:
            project_info = current_item.data(Qt.ItemDataRole.UserRole)
            project_path = project_info["path"]
            
            # 加载项目
            project_data = self.project_manager.load_project(project_path)
            if not project_data:
                QMessageBox.warning(self, "警告", "加载项目失败")
                return
                
            self.selected_project_data = project_data
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开项目失败:\n{e}")
            
    def get_selected_project(self):
        """获取选中的项目数据"""
        return self.selected_project_data
        
    def paintEvent(self, event):
        """绘制圆角背景"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)
        
        # 绘制半透明背景
        painter.fillRect(self.rect(), QColor(0, 0, 0, 100))
        
        super().paintEvent(event)
