#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频播放器组件 - 支持音频播放
"""

import os
import cv2
import numpy as np
from pathlib import Path
from typing import Optional

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QPushButton, QSlider, QFrame
)
from PySide6.QtCore import Qt, QTimer, Signal, QThread, QUrl
from PySide6.QtGui import QPixmap, QImage, QPainter, QPen
from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput

class VideoPlayerThread(QThread):
    """视频播放线程"""
    
    frame_ready = Signal(np.ndarray)
    position_changed = Signal(float)
    
    def __init__(self):
        super().__init__()
        self.video_path = ""
        self.cap = None
        self.is_playing = False
        self.current_position = 0.0
        self.total_frames = 0
        self.fps = 30.0
        self.seek_position = -1
        self._should_stop = False
        self.playback_speed = 1.0  # 播放速度倍数

    def load_video(self, video_path: str, video_info=None):
        """加载视频文件"""
        try:
            print(f"🎬 VideoPlayerThread 加载视频: {video_path}")

            # 检查是否是占位符文件
            if video_path.startswith('placeholder_'):
                print(f"跳过占位符文件: {video_path}")
                return

            # 检查文件是否存在
            if not os.path.exists(video_path):
                print(f"视频文件不存在: {video_path}")
                return

            # 释放之前的视频
            if self.cap:
                self.cap.release()

            self.video_path = video_path

            # 🔧 修复FFmpeg多线程问题：全局设置单线程环境变量
            os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;1'
            os.environ['OPENCV_VIDEOIO_PRIORITY_FFMPEG'] = '0'  # 降低FFmpeg优先级

            # 🔧 强制OpenCV使用单线程
            cv2.setNumThreads(1)

            # 尝试多种后端以避免FFmpeg多线程问题
            # 优先使用非FFmpeg后端
            backends = [cv2.CAP_DSHOW, cv2.CAP_MSMF]

            for backend in backends:
                try:
                    print(f"尝试使用后端: {backend}")
                    self.cap = cv2.VideoCapture(video_path, backend)
                    if self.cap and self.cap.isOpened():
                        # 设置缓冲区大小和线程数
                        self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                        print(f"✅ 成功使用后端: {backend}")
                        break
                    else:
                        if self.cap:
                            self.cap.release()
                        self.cap = None
                except Exception as e:
                    print(f"❌ 后端 {backend} 失败: {e}")
                    if self.cap:
                        self.cap.release()
                    self.cap = None

            # 如果非FFmpeg后端都失败，最后尝试FFmpeg（但设置单线程）
            if not self.cap or not self.cap.isOpened():
                try:
                    print("尝试FFmpeg后端（强制单线程模式）")
                    # 🔧 强化FFmpeg单线程设置
                    old_threads = os.environ.get('OPENCV_FFMPEG_CAPTURE_OPTIONS', '')
                    os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;1|thread_type;1|thread_count;1'

                    self.cap = cv2.VideoCapture(video_path, cv2.CAP_FFMPEG)
                    if self.cap and self.cap.isOpened():
                        self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                        print("✅ 成功使用FFmpeg后端（单线程）")
                    else:
                        if self.cap:
                            self.cap.release()
                        self.cap = None

                    # 恢复环境变量
                    if old_threads:
                        os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = old_threads
                    else:
                        os.environ.pop('OPENCV_FFMPEG_CAPTURE_OPTIONS', None)

                except Exception as e:
                    print(f"❌ FFmpeg后端失败: {e}")
                    self.cap = None

            if self.cap and self.cap.isOpened():
                self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
                self.fps = self.cap.get(cv2.CAP_PROP_FPS) or 30.0

                # 读取第一帧
                ret, frame = self.cap.read()
                if ret:
                    self.frame_ready.emit(frame)
                    self.current_position = 0.0
                    self.position_changed.emit(0.0)
                    print(f"✅ 视频加载成功: {video_path}")
                else:
                    print(f"❌ 无法读取第一帧: {video_path}")
            else:
                print(f"❌ 无法打开视频文件: {video_path}")

        except Exception as e:
            print(f"❌ 加载视频异常: {e}")
            if self.cap:
                self.cap.release()
                self.cap = None

    def play(self):
        """开始播放"""
        self.is_playing = True
    
    def pause(self):
        """暂停播放"""
        self.is_playing = False
    
    def stop(self):
        """停止播放"""
        self.is_playing = False
        self.seek_to_position(0.0)
    
    def seek_to_position(self, position: float):
        """跳转到指定位置（秒）"""
        self.seek_position = position

    def set_position(self, position: float):
        """设置播放位置（秒）- 别名方法"""
        self.seek_to_position(position)
    
    def stop_thread(self):
        """停止线程"""
        self._should_stop = True
        self.is_playing = False

    def set_playback_speed(self, speed: float):
        """设置播放速度"""
        self.playback_speed = speed
        print(f"🎬 视频线程播放速度设置为: {speed}x")
    
    def run(self):
        """主循环"""
        base_frame_time = 1.0 / self.fps * 1000  # 基础帧时间（毫秒）
        
        while not self._should_stop:
            if not self.cap or not self.cap.isOpened():
                self.msleep(100)
                continue
            
            # 处理跳转
            if self.seek_position >= 0:
                frame_number = int(self.seek_position * self.fps)
                self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
                self.current_position = self.seek_position
                self.seek_position = -1
                
                try:
                    # 使用安全的帧读取方法
                    ret, frame = self.cap.read()

                    if ret:
                        self.frame_ready.emit(frame)
                        self.position_changed.emit(self.current_position)
                except Exception as e:
                    print(f"❌ 跳转读取帧失败: {e}")
                    ret = False
            
            # 播放帧
            elif self.is_playing:
                try:
                    # 使用安全的帧读取方法
                    ret, frame = self.cap.read()

                    if ret:
                        self.frame_ready.emit(frame)
                        self.current_position = self.cap.get(cv2.CAP_PROP_POS_FRAMES) / self.fps
                        self.position_changed.emit(self.current_position)
                    else:
                        # 视频播放完毕
                        self.is_playing = False
                except Exception as e:
                    print(f"❌ 播放读取帧失败: {e}")
                    self.is_playing = False
                
                # 根据播放速度调整帧间隔
                frame_time = base_frame_time / self.playback_speed
                self.msleep(int(max(1, frame_time)))
            else:
                self.msleep(50)
        
        # 清理资源
        if self.cap:
            self.cap.release()
            self.cap = None

class VideoDisplay(QLabel):
    """视频显示组件 - 支持原视频尺寸预览"""
    
    def __init__(self):
        super().__init__()
        self.setMinimumSize(640, 360)
        self.setStyleSheet("""
            background-color: #161616;
            border: none;
            margin: 0px;
            padding: 0px;
        """)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setText("无视频")
        self.setScaledContents(False)
        
        # 存储原始帧数据
        self.current_frame = None
        # 存储原始视频尺寸
        self.video_width = 0
        self.video_height = 0
        # 预览模式：'original'(原始尺寸，不拉伸)
        self.preview_mode = 'original'
        # 预览质量：'original'(原画), 'high'(高清), 'smooth'(流畅)
        self.preview_quality = 'original'
        # 预览比例：默认9:16
        self.preview_aspect_ratio = '9:16'
    
    def update_frame(self, frame: np.ndarray):
        """更新显示帧"""
        self.current_frame = frame.copy()

        # 获取原始尺寸
        height, width, channel = frame.shape
        self.video_width = width
        self.video_height = height

        # 🔧 新增：根据预览比例裁剪帧
        frame = self.apply_aspect_ratio_crop(frame)

        # 转换为QImage
        height, width, channel = frame.shape  # 使用裁剪后的尺寸
        bytes_per_line = 3 * width

        # OpenCV使用BGR，Qt使用RGB
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

        q_image = QImage(rgb_frame.data, width, height, bytes_per_line, QImage.Format.Format_RGB888)

        # 转换为QPixmap
        pixmap = QPixmap.fromImage(q_image)

        # 保持原始比例缩放
        scaled_pixmap = self.scale_pixmap_keep_ratio(pixmap)
        self.setPixmap(scaled_pixmap)
    
    def scale_pixmap_keep_ratio(self, pixmap: QPixmap) -> QPixmap:
        """保持原始比例显示，不拉伸，适应显示区域"""
        # 🔧 修改：确保视频不全屏，适应显示区域大小

        # 获取显示区域大小
        display_width = self.width()
        display_height = self.height()

        # 获取视频尺寸
        video_width = pixmap.width()
        video_height = pixmap.height()

        if display_width <= 0 or display_height <= 0:
            return pixmap

        # 计算缩放比例，确保视频适应显示区域
        scale_x = display_width / video_width
        scale_y = display_height / video_height
        scale = min(scale_x, scale_y)  # 选择较小的缩放比例，确保完全显示

        # 根据预览质量进一步调整
        if self.preview_quality == 'smooth':
            scale *= 0.5  # 流畅模式：进一步缩小
        elif self.preview_quality == 'high':
            scale *= 0.75  # 高清模式：适度缩小
        # 原画模式：使用计算出的缩放比例

        # 限制最大缩放比例，避免过大
        scale = min(scale, 1.0)

        if scale != 1.0:
            new_width = int(video_width * scale)
            new_height = int(video_height * scale)
            return pixmap.scaled(new_width, new_height, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)

        return pixmap
    
    def set_preview_quality(self, quality: str):
        """设置预览质量"""
        if quality in ['original', 'high', 'smooth']:
            self.preview_quality = quality
            # 重新绘制当前帧
            if self.current_frame is not None:
                self.update_frame(self.current_frame)

    def set_preview_aspect_ratio(self, aspect_ratio: str):
        """设置预览比例"""
        self.preview_aspect_ratio = aspect_ratio
        # 重新绘制当前帧
        if self.current_frame is not None:
            self.update_frame(self.current_frame)

    def apply_aspect_ratio_crop(self, frame: np.ndarray) -> np.ndarray:
        """根据预览比例裁剪帧"""
        try:
            height, width = frame.shape[:2]

            # 比例映射
            aspect_ratios = {
                '16:9': (16, 9),
                '4:3': (4, 3),
                '9:16': (9, 16),
                '1:1': (1, 1),
                '21:9': (21, 9)
            }

            if self.preview_aspect_ratio not in aspect_ratios:
                return frame

            target_w, target_h = aspect_ratios[self.preview_aspect_ratio]
            target_ratio = target_w / target_h
            current_ratio = width / height

            if abs(current_ratio - target_ratio) < 0.01:
                # 比例已经接近，不需要裁剪
                return frame

            if current_ratio > target_ratio:
                # 当前视频更宽，需要裁剪宽度
                new_width = int(height * target_ratio)
                x_offset = (width - new_width) // 2
                cropped_frame = frame[:, x_offset:x_offset + new_width]
            else:
                # 当前视频更高，需要裁剪高度
                new_height = int(width / target_ratio)
                y_offset = (height - new_height) // 2
                cropped_frame = frame[y_offset:y_offset + new_height, :]

            return cropped_frame

        except Exception as e:
            print(f"❌ 比例裁剪失败: {e}")
            return frame
    
    def resizeEvent(self, event):
        """窗口大小改变时重新缩放"""
        super().resizeEvent(event)
        if self.current_frame is not None:
            self.update_frame(self.current_frame)
    
    def paintEvent(self, event):
        """重写绘制事件，添加时间轴指示器等"""
        super().paintEvent(event)
        
        # 这里可以添加额外的绘制内容，如播放位置指示器等
        painter = QPainter(self)
        painter.setPen(QPen(Qt.GlobalColor.red, 2))
        
        # 示例：绘制中心十字线
        if self.current_frame is not None:
            center_x = self.width() // 2
            center_y = self.height() // 2
            painter.drawLine(center_x - 10, center_y, center_x + 10, center_y)
            painter.drawLine(center_x, center_y - 10, center_x, center_y + 10)

class VideoPlayer(QWidget):
    """视频播放器 - 支持音频播放"""
    
    position_changed = Signal(float)
    
    def __init__(self):
        super().__init__()
        self.current_video = ""
        self.video_info = None
        
        # 初始化媒体播放器（用于音频）
        self.media_player = QMediaPlayer()
        self.audio_output = QAudioOutput()

        # 🔧 修复音频输出设置
        try:
            self.media_player.setAudioOutput(self.audio_output)
            # 设置初始音量
            self.audio_output.setVolume(0.7)
            print("✅ 音频输出初始化成功")
        except Exception as e:
            print(f"❌ 音频输出初始化失败: {e}")

        # 连接媒体播放器信号
        self.media_player.positionChanged.connect(self.on_media_position_changed)
        self.media_player.durationChanged.connect(self.on_media_duration_changed)
        self.media_player.mediaStatusChanged.connect(self.on_media_status_changed)
        
        # 初始化视频播放线程（用于视频帧）
        self.player_thread = VideoPlayerThread()
        self.player_thread.frame_ready.connect(self.on_frame_ready)
        self.player_thread.position_changed.connect(self.on_video_position_changed)
        
        # 同步控制 - 性能优化
        self._updating_position = False
        self._sync_timer = QTimer()
        self._sync_timer.timeout.connect(self.sync_audio_video)
        self._sync_timer.setInterval(500)  # 🔧 优化：从100ms改为500ms，减少同步频率
        
        self.init_ui()
        
        # 启动播放线程
        self.player_thread.start()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # 视频显示区域 - 占据全部空间
        self.video_display = VideoDisplay()
        layout.addWidget(self.video_display)


        # 设置初始音量
        self.audio_output.setVolume(0.7)

    def on_media_status_changed(self, status):
        """媒体状态变化处理"""
        if status == QMediaPlayer.MediaStatus.LoadedMedia:
            print("✅ 媒体加载完成，音频准备就绪")
            # 强制发送位置变化信号以更新时间显示
            current_pos = self.media_player.position() / 1000.0
            self.position_changed.emit(current_pos)
        elif status == QMediaPlayer.MediaStatus.InvalidMedia:
            print("❌ 媒体文件无效")
        elif status == QMediaPlayer.MediaStatus.NoMedia:
            print("ℹ️ 没有媒体文件")

    def load_video(self, video_path: str, video_info):
        """加载视频"""
        # 检查是否是占位符文件
        if video_path.startswith('placeholder_'):
            print(f"Skipping placeholder file in VideoPlayer: {video_path}")
            return

        # 检查文件是否存在
        if not os.path.exists(video_path):
            print(f"Video file not found in VideoPlayer: {video_path}")
            return

        self.current_video = video_path
        self.video_info = video_info

        # 加载到媒体播放器（音频）
        try:
            media_url = QUrl.fromLocalFile(video_path)
            self.media_player.setSource(media_url)
            print(f"✅ 音频源设置成功: {video_path}")
        except Exception as e:
            print(f"❌ 音频源设置失败: {e}")

        # 加载到视频播放线程（视频帧）
        self.player_thread.load_video(video_path)

        # 控制组件现在在主窗口中，这里只需要加载视频
        print(f"视频加载成功: {video_path}")
        print(f"视频时长: {video_info.duration}秒")
    
    def play(self):
        """播放"""
        try:
            print("🎵 开始播放视频...")

            # 播放音频
            if hasattr(self, 'media_player'):
                self.media_player.play()
                print("✅ 音频播放器已启动")

            # 播放视频
            if hasattr(self, 'player_thread'):
                self.player_thread.play()
                print("✅ 视频线程已启动")

            # 启动同步定时器
            if hasattr(self, '_sync_timer'):
                self._sync_timer.start()
                print("✅ 同步定时器已启动")

            print("✅ 视频播放成功")
        except Exception as e:
            print(f"❌ 视频播放失败: {e}")
            # 尝试恢复
            try:
                self.pause()
            except:
                pass

    def pause(self):
        """暂停"""
        try:
            print("🎵 暂停视频...")

            # 暂停音频
            if hasattr(self, 'media_player'):
                self.media_player.pause()
                print("✅ 音频播放器已暂停")

            # 暂停视频
            if hasattr(self, 'player_thread'):
                self.player_thread.pause()
                print("✅ 视频线程已暂停")

            # 停止同步定时器
            if hasattr(self, '_sync_timer'):
                self._sync_timer.stop()
                print("✅ 同步定时器已停止")

            print("✅ 视频暂停成功")
        except Exception as e:
            print(f"❌ 视频暂停失败: {e}")

    def stop(self):
        """停止"""
        self.media_player.stop()
        self.player_thread.stop()
        self._sync_timer.stop()
        print("视频停止")

    def cleanup(self):
        """清理资源"""
        try:
            print("🔧 开始清理视频播放器资源...")

            # 停止播放
            self.is_playing = False
            if hasattr(self, '_sync_timer'):
                self._sync_timer.stop()

            # 停止媒体播放器
            if hasattr(self, 'media_player'):
                try:
                    self.media_player.stop()
                    print("✅ 媒体播放器已停止")
                except Exception as e:
                    print(f"❌ 停止媒体播放器失败: {e}")

            # 停止视频线程
            if hasattr(self, 'player_thread'):
                try:
                    self.player_thread.stop_thread()
                    self.player_thread.wait(2000)  # 等待最多2秒
                    if self.player_thread.isRunning():
                        print("🔧 强制终止视频线程...")
                        self.player_thread.terminate()
                        self.player_thread.wait(1000)
                    print("✅ 视频线程已停止")
                except Exception as e:
                    print(f"❌ 停止视频线程失败: {e}")

            print("✅ 视频播放器资源清理完成")
        except Exception as e:
            print(f"❌ 视频播放器清理失败: {e}")
    
    def toggle_playback(self):
        """切换播放/暂停"""
        if self.is_playing():
            self.pause()
        else:
            self.play()
    
    def is_playing(self) -> bool:
        """是否正在播放"""
        return self.media_player.playbackState() == QMediaPlayer.PlaybackState.PlayingState
    
    def set_position(self, position: float):
        """设置播放位置"""
        if not self._updating_position:
            self._updating_position = True
            
            # 设置媒体播放器位置（音频）
            self.media_player.setPosition(int(position * 1000))
            
            # 设置视频线程位置
            self.player_thread.seek_to_position(position)
            
            # 进度滑块现在在主窗口中
            pass
            
            self._updating_position = False
    
    def sync_audio_video(self):
        """同步音频和视频 - 进一步优化性能"""
        if not self._updating_position and self.is_playing():
            try:
                audio_pos = self.media_player.position() / 1000.0
                video_pos = self.player_thread.current_position

                # 🔧 性能优化：进一步增大同步阈值，减少频繁调整
                sync_threshold = 2.0  # 从1.0s改为2.0s，进一步减少频繁同步
                if abs(audio_pos - video_pos) > sync_threshold:
                    self.player_thread.seek_to_position(audio_pos)
                    print(f"🔧 音视频同步: 音频{audio_pos:.2f}s, 视频{video_pos:.2f}s")
            except Exception as e:
                # 忽略同步错误，避免影响播放流畅性
                pass
    
    def on_volume_changed(self, value):
        """音量改变"""
        self.audio_output.setVolume(value / 100.0)

    def set_volume(self, volume: float):
        """设置音量 (0.0 - 1.0)"""
        try:
            self.audio_output.setVolume(volume)
            print(f"🔊 音量设置为: {volume:.2f}")
        except Exception as e:
            print(f"❌ 音量设置失败: {e}")
            # 尝试备用方法
            try:
                if hasattr(self.media_player, 'setVolume'):
                    self.media_player.setVolume(int(volume * 100))
                    print(f"🔊 使用备用方法设置音频: {volume:.2f}")
            except Exception as e2:
                print(f"❌ 备用音量设置也失败: {e2}")

    def set_playback_speed(self, speed: float):
        """设置播放速度"""
        try:
            # 设置音频播放速度
            if hasattr(self.media_player, 'setPlaybackRate'):
                self.media_player.setPlaybackRate(speed)
                print(f"🚀 音频播放速度设置为: {speed}x")

            # 设置视频播放速度
            if hasattr(self, 'player_thread'):
                self.player_thread.set_playback_speed(speed)
                print(f"🎬 视频播放速度设置为: {speed}x")

        except Exception as e:
            print(f"❌ 播放速度设置失败: {e}")

    def clear_display(self):
        """清空视频显示，显示黑屏"""
        try:
            # 使用video_display组件
            if hasattr(self, 'video_display') and self.video_display:
                # 获取当前视频显示区域的大小
                size = self.video_display.size()
                width = max(size.width(), 640)
                height = max(size.height(), 360)

                # 创建黑色图像
                black_image = QImage(width, height, QImage.Format.Format_RGB888)
                black_image.fill(QColor(0, 0, 0))  # 填充黑色

                # 转换为QPixmap并设置到显示组件
                pixmap = QPixmap.fromImage(black_image)
                self.video_display.setPixmap(pixmap)

                # 清空当前帧数据
                self.video_display.current_frame = None

                print("🖤 视频预览已清空，显示黑屏")
            else:
                print("⚠️ 视频显示组件不存在，无法清空显示")

        except Exception as e:
            print(f"❌ 清空视频显示失败: {e}")
    
    def on_frame_ready(self, frame: np.ndarray):
        """帧数据就绪"""
        self.video_display.update_frame(frame)
    
    def on_media_position_changed(self, position: int):
        """媒体播放器位置改变"""
        if not self._updating_position:
            position_sec = position / 1000.0
            print(f"🎵 音频位置变化: {position}ms -> {position_sec:.2f}s")
            # 进度滑块和时间显示现在在主窗口中
            self.position_changed.emit(position_sec)
    
    def on_video_position_changed(self, position: float):
        """视频位置改变"""
        # 主要同步由音频驱动，这里只做备用
        pass
    
    def on_media_duration_changed(self, duration: int):
        """媒体时长改变"""
        duration_sec = duration / 1000.0
        print(f"🕐 媒体播放器时长变化: {duration}ms -> {duration_sec:.2f}秒")

        # 发送位置变化信号以更新时间显示
        current_pos = self.media_player.position() / 1000.0
        self.position_changed.emit(current_pos)
    
    def on_slider_pressed(self):
        """滑块按下"""
        self.slider_dragging = True
        self._sync_timer.stop()
    
    def on_slider_released(self):
        """滑块释放"""
        self.slider_dragging = False
        # 滑块现在在主窗口中，这个方法可能不再需要
        if self.is_playing():
            self._sync_timer.start()
    
    def on_slider_value_changed(self, value):
        """滑块值改变"""
        # 滑块现在在主窗口中，这个方法可能不再需要
        pass
    
    def update_time_display(self, current: float, total: float):
        """更新时间显示"""
        # 时间标签现在在主窗口中
        current_str = self.format_time(current)
        total_str = self.format_time(total)
        print(f"时间: {current_str} / {total_str}")
    
    def format_time(self, seconds: float) -> str:
        """格式化时间"""
        minutes = int(seconds // 60)
        secs = int(seconds % 60)
        return f"{minutes:02d}:{secs:02d}"
    
    def closeEvent(self, event):
        """关闭事件"""
        try:
            self._sync_timer.stop()
            self.media_player.stop()
            if hasattr(self, 'player_thread') and self.player_thread.isRunning():
                self.player_thread.stop_thread()
                self.player_thread.quit()
                self.player_thread.wait(3000)  # 等待最多3秒
        except Exception as e:
            print(f"关闭视频播放器时出错: {e}")
        finally:
            event.accept()
    
    def __del__(self):
        """析构函数"""
        try:
            if hasattr(self, '_sync_timer') and self._sync_timer:
                self._sync_timer.stop()
            if hasattr(self, 'media_player') and self.media_player:
                self.media_player.stop()
            if hasattr(self, 'player_thread') and self.player_thread and self.player_thread.isRunning():
                self.player_thread.stop_thread()
                self.player_thread.quit()
                self.player_thread.wait(1000)
        except Exception:
            pass  # 析构函数中忽略所有异常 