#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from PySide6.QtWidgets import QWidget
from PySide6.QtCore import Qt, Signal, QPoint
from PySide6.QtGui import QPainter, QColor, QPen, QBrush, QFont, QPolygon

class TimelineRuler(QWidget):
    """专业的时间标尺组件"""

    # 添加播放头拖动信号
    position_changed = Signal(float)

    def __init__(self, global_params=None):
        super().__init__()

        # 使用全局参数管理器
        if global_params is None:
            # 如果没有提供全局参数，创建一个临时的（向后兼容）
            from .multi_track_timeline import TimelineGlobalParams
            self.global_params = TimelineGlobalParams()
            self._owns_global_params = True
        else:
            self.global_params = global_params
            self._owns_global_params = False

        self.setFixedHeight(self.global_params.ruler_height)
        self.setMinimumWidth(3000)

        # 拖动预览位置
        self.drag_position = -1

        # 拖动状态
        self.dragging_playhead = False

        # 🔧 新设计：播放头直接在时间轴标尺中绘制，不使用覆盖层
        # self.playhead_overlay = PlayheadOverlay(self)
        # self.playhead_overlay.setGeometry(0, 0, self.width(), self.height())
        # self.playhead_overlay.set_zoom(self.global_params.pixels_per_second)

        # 连接全局参数变化信号
        self.global_params.zoom_changed.connect(self.on_zoom_changed)
        self.global_params.duration_changed.connect(self.on_duration_changed)
        self.global_params.position_changed.connect(self.on_position_changed)

        self.setStyleSheet("""
            TimelineRuler {
                background-color: #0A0A0A;
                border-bottom: 1px solid #2A2A2A;
            }
        """)

        print("✅ TimelineRuler 初始化完成，使用全局参数管理器")

    def on_zoom_changed(self, pixels_per_second: float):
        """响应缩放变化"""
        self.update_size()
        if hasattr(self, 'playhead_overlay'):
            self.playhead_overlay.set_zoom(pixels_per_second)
        self.update()

    def on_duration_changed(self, duration: float):
        """响应时长变化"""
        self.update_size()

    def on_position_changed(self, position: float):
        """响应位置变化"""
        if hasattr(self, 'playhead_overlay'):
            self.playhead_overlay.set_position(position)

    def update_size(self):
        """根据全局参数更新尺寸"""
        width = self.global_params.get_timeline_width()
        self.setMinimumWidth(max(3000, width))

    def set_position(self, position: float):
        """设置播放位置"""
        # 使用全局参数管理器设置位置
        self.global_params.set_current_position(position)

        # 更新播放头覆盖层
        if hasattr(self, 'playhead_overlay'):
            self.playhead_overlay.set_position(position)

        # 更新绘制
        self.update()

    def set_drag_position(self, x_pos: int):
        """设置拖动预览位置"""
        if x_pos >= 0:
            self.drag_position = x_pos
        else:
            self.drag_position = -1
        self.update()

    def clear_drag_position(self):
        """清除拖动预览位置"""
        self.drag_position = -1
        self.update()

    def apply_snap_if_available(self, time_pos: float) -> float:
        """如果父级支持磁性吸附，则应用磁性吸附"""
        # 向上查找支持磁性吸附的父组件
        parent = self.parent()
        while parent:
            if hasattr(parent, 'apply_snap') and callable(parent.apply_snap):
                return parent.apply_snap(time_pos)
            parent = parent.parent()

        # 如果没有找到支持磁性吸附的父组件，返回原始位置
        return time_pos

    def mousePressEvent(self, event):
        """鼠标按下事件 - 支持拖动播放头"""
        if event.button() == Qt.MouseButton.LeftButton:
            x_pos = event.position().x()
            time_pos = self.global_params.pixels_to_time(x_pos)

            # 清除任何拖动预览线
            self.clear_drag_position()

            # 检查是否点击在播放头附近
            playhead_x = self.global_params.time_to_pixels(self.global_params.current_position)
            if abs(x_pos - playhead_x) <= 10:  # 点击播放头附近10像素内
                self.dragging_playhead = True
            else:
                # 直接跳转到点击位置
                if 0 <= time_pos <= self.global_params.total_duration:
                    # 应用磁性吸附
                    snapped_time = self.global_params.apply_snap(time_pos)
                    self.global_params.set_current_position(snapped_time)
                    self.position_changed.emit(snapped_time)
                    # 同步到播放头覆盖层
                    if hasattr(self, 'playhead_overlay'):
                        self.playhead_overlay.set_position(snapped_time)
                    self.update()

    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 拖动播放头"""
        if self.dragging_playhead:
            x_pos = event.position().x()
            time_pos = max(0, min(self.global_params.pixels_to_time(x_pos), self.global_params.total_duration))
            # 应用磁性吸附
            snapped_time = self.global_params.apply_snap(time_pos)
            self.global_params.set_current_position(snapped_time)
            self.position_changed.emit(snapped_time)
            # 同步到播放头覆盖层
            if hasattr(self, 'playhead_overlay'):
                self.playhead_overlay.set_position(snapped_time)
            self.update()

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.dragging_playhead = False
            # 同步到播放头覆盖层
            if hasattr(self, 'playhead_overlay'):
                self.playhead_overlay.set_position(self.global_params.current_position)

    def paintEvent(self, event):
        """绘制时间标尺"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 🔧 修复：完全清除背景，确保没有残留
        painter.setCompositionMode(QPainter.CompositionMode.CompositionMode_Source)
        painter.fillRect(self.rect(), QColor(80, 92, 112))  # #505C70
        painter.setCompositionMode(QPainter.CompositionMode.CompositionMode_SourceOver)

        # 绘制时间刻度
        self.draw_time_marks(painter)

        # 🔧 新设计：直接在时间轴标尺中绘制播放头
        self.draw_playhead(painter)

        # 绘制拖动预览线
        if self.drag_position >= 0:
            self.draw_drag_preview(painter)

    def draw_time_marks(self, painter):
        """绘制时间刻度 - 智能刻度间隔和全屏绘制"""
        painter.setPen(QPen(QColor(180, 180, 180), 1))
        font = QFont("Arial", 9)
        painter.setFont(font)

        # 🔧 新功能：根据缩放级别智能调整刻度间隔
        pixels_per_second = self.global_params.pixels_per_second

        # 根据缩放级别确定刻度间隔
        if pixels_per_second >= 100:
            # 高缩放：每秒 + 每0.5秒
            major_interval = 1.0
            minor_interval = 0.5
            text_interval = 5
        elif pixels_per_second >= 50:
            # 中高缩放：每2秒 + 每秒
            major_interval = 2.0
            minor_interval = 1.0
            text_interval = 10
        elif pixels_per_second >= 20:
            # 中缩放：每5秒 + 每秒
            major_interval = 5.0
            minor_interval = 1.0
            text_interval = 15
        elif pixels_per_second >= 10:
            # 中低缩放：每10秒 + 每5秒
            major_interval = 10.0
            minor_interval = 5.0
            text_interval = 30
        elif pixels_per_second >= 5:
            # 低缩放：每30秒 + 每10秒
            major_interval = 30.0
            minor_interval = 10.0
            text_interval = 60
        else:
            # 极低缩放：每60秒 + 每30秒
            major_interval = 60.0
            minor_interval = 30.0
            text_interval = 120

        # 🔧 新功能：根据时间轴标尺的实际宽度绘制刻度，确保填满整个标尺
        # 时间轴标尺的实际宽度（这是滚动内容的宽度，不是可视区域宽度）
        ruler_width = self.width()  # 时间轴标尺组件的实际宽度

        # 计算需要绘制的时间范围：从0开始到能填满整个标尺宽度
        start_time = 0.0
        end_time = self.global_params.pixels_to_time(ruler_width)

        # 确保绘制范围覆盖整个标尺宽度，不受总时长限制
        # 这样即使在小缩放比例下也能填满整个标尺

        # 绘制主要刻度
        current_time = int(start_time / major_interval) * major_interval
        while current_time <= end_time:
            # 计算刻度在时间轴标尺中的位置
            x_pos = self.global_params.time_to_pixels(current_time)

            if 0 <= x_pos <= ruler_width:
                # 绘制刻度线
                if current_time % text_interval == 0:
                    # 带文本的长刻度
                    painter.drawLine(x_pos, 20, x_pos, self.height())
                    time_text = self.format_time(current_time)
                    painter.drawText(x_pos + 3, 15, time_text)
                else:
                    # 普通长刻度
                    painter.drawLine(x_pos, 25, x_pos, self.height())

            current_time += major_interval

        # 绘制次要刻度（如果间隔足够大）
        if minor_interval != major_interval and pixels_per_second >= 10:
            painter.setPen(QPen(QColor(120, 120, 120), 1))
            current_time = int(start_time / minor_interval) * minor_interval
            while current_time <= end_time:
                # 跳过与主要刻度重合的位置
                if current_time % major_interval != 0:
                    # 计算刻度在时间轴标尺中的位置
                    x_pos = self.global_params.time_to_pixels(current_time)

                    if 0 <= x_pos <= ruler_width:
                        painter.drawLine(x_pos, 28, x_pos, self.height())

                current_time += minor_interval

    def draw_playhead(self, painter):
        """绘制播放头线 - 只在时间轴标尺中绘制顶部部分，颜色#00c896"""
        if self.global_params.current_position >= 0:
            x_pos = self.global_params.time_to_pixels(self.global_params.current_position)

            # 🔧 修复：检查播放头是否在时间轴的有效范围内
            timeline_width = self.global_params.get_timeline_width()
            if x_pos < 0 or x_pos > timeline_width:
                return  # 播放头超出时间轴范围，不绘制

            # 🔧 修复：只在时间轴标尺的可见区域内绘制
            if 0 <= x_pos <= self.width():
                # 🔧 修复：播放头线只在时间轴标尺区域内绘制，不延伸到按钮栏
                # 线条从三角形底部开始，到时间轴标尺底部结束
                line_start_y = 12  # 从三角形底部开始
                line_end_y = self.height()  # 到时间轴标尺底部
                painter.setPen(QPen(QColor(0, 200, 150), 3))  # #00c896颜色，3px宽度
                painter.drawLine(x_pos, line_start_y, x_pos, line_end_y)

                # 绘制播放头三角形指示器 - 完全盖住线条顶部
                painter.setBrush(QBrush(QColor(0, 200, 150)))  # #00c896颜色填充
                painter.setPen(Qt.PenStyle.NoPen)  # 🔧 修复：去掉边框，避免边框影响覆盖效果

                # 🔧 修复：三角形在时间轴标尺顶部，线条从三角形底部开始
                triangle_top = 0  # 从时间轴标尺顶部开始
                triangle_bottom = 12  # 三角形高度12px
                points = [
                    QPoint(x_pos - 6, triangle_top),
                    QPoint(x_pos + 6, triangle_top),
                    QPoint(x_pos, triangle_bottom)
                ]
                polygon = QPolygon(points)
                painter.drawPolygon(polygon)

    def draw_drag_preview(self, painter):
        """绘制拖动预览线 - 半椭圆指示器样式，颜色#00c896"""
        # 绘制主线条 - 更粗更明显
        painter.setPen(QPen(QColor(0, 200, 150), 3))  # #00c896颜色，3px宽度
        painter.drawLine(self.drag_position, 0, self.drag_position, self.height())

        # 绘制拖动预览三角形指示器
        painter.setBrush(QBrush(QColor(0, 200, 150)))  # #00c896颜色
        painter.setPen(QPen(QColor(0, 200, 150), 1))

        # 绘制三角形
        points = [
            QPoint(self.drag_position - 6, 0),
            QPoint(self.drag_position + 6, 0),
            QPoint(self.drag_position, 12)
        ]
        polygon = QPolygon(points)
        painter.drawPolygon(polygon)

        # 添加发光效果 - 绘制半透明的外围线
        painter.setPen(QPen(QColor(0, 200, 150, 100), 5))  # 半透明，更宽
        painter.drawLine(self.drag_position, 0, self.drag_position, self.height())

    def format_time(self, seconds: float) -> str:
        """格式化时间"""
        minutes = int(seconds // 60)
        secs = int(seconds % 60)
        return f"{minutes:02d}:{secs:02d}"

    def set_zoom(self, pixels_per_second: float):
        """设置缩放级别 - 委托给全局参数管理器"""
        self.global_params.set_pixels_per_second(pixels_per_second)

    def set_total_duration(self, duration: float):
        """设置总时长 - 委托给全局参数管理器"""
        self.global_params.set_total_duration(duration)

    def resizeEvent(self, event):
        """调整大小事件"""
        super().resizeEvent(event)
        if hasattr(self, 'playhead_overlay'):
            self.playhead_overlay.setGeometry(0, 0, self.width(), self.height())

class PlayheadOverlay(QWidget):
    """专门的播放头绘制层，确保红色提示线始终在最顶层"""

    def __init__(self, parent=None):
        super().__init__(parent)


        # 设置透明背景，不阻挡鼠标事件
        self.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground, True)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)

        # 确保始终在最顶层
        self.raise_()
        # 设置最高层级显示
        self.setAttribute(Qt.WidgetAttribute.WA_AlwaysStackOnTop, True)

        # 获取全局参数管理器（从父组件）
        self.global_params = None
        if parent and hasattr(parent, 'global_params'):
            self.global_params = parent.global_params

        # 播放头数据
        self.drag_position = -1

        # 防止重复绘制的标志
        self._is_painting = False



    def set_position(self, position: float):
        """设置播放头位置"""
        if self.global_params:
            # 使用全局参数管理器
            old_x = self.global_params.time_to_pixels(self.global_params.current_position)
            new_x = self.global_params.time_to_pixels(position)

            # 更新绘制区域
            self.update(old_x - 10, 0, 20, self.height())
            self.update(new_x - 10, 0, 20, self.height())
        else:
            # 兼容模式
            self.update()

    def set_drag_position(self, x_pos: int):
        """设置拖动预览位置"""
        self.drag_position = x_pos
        self.update()

    def clear_drag_position(self):
        """清除拖动预览位置"""
        self.drag_position = -1
        self.update()

    def set_zoom(self, pixels_per_second: float):
        """设置缩放级别 - 委托给全局参数管理器"""
        if self.global_params:
            self.global_params.set_pixels_per_second(pixels_per_second)
        self.update()

    def paintEvent(self, event):
        """绘制播放头线 - 确保在最顶层"""
        # 防止重复绘制
        if self._is_painting:
            return
        self._is_painting = True

        painter = None
        try:
            # 🔧 关键修复：先让所有子组件完成绘制
            super().paintEvent(event)

            # 然后绘制播放头，确保在最顶层
            painter = QPainter(self)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)

            # 使用最高级别的合成模式，确保播放头不被遮挡
            painter.setCompositionMode(QPainter.CompositionMode.CompositionMode_SourceOver)

            # 🔧 删除重复绘制：只保留拖动预览线，播放头由唯一实例绘制
            # 播放头绘制逻辑已移除，避免重复绘制

            # 绘制拖动预览线 - 贯穿整个高度，颜色#00c896，半椭圆指示器样式
            if self.drag_position >= 0:
                if 0 <= self.drag_position <= self.width():
                    painter.setPen(QPen(QColor(0, 200, 150), 3))  # #00c896颜色，3px宽度
                    painter.drawLine(self.drag_position, 0, self.drag_position, self.height())

                    # 绘制拖动预览三角形指示器
                    painter.setBrush(QBrush(QColor(0, 200, 150)))  # #00c896颜色
                    painter.setPen(QPen(QColor(0, 200, 150), 1))

                    # 绘制三角形
                    points = [
                        QPoint(self.drag_position - 6, 0),
                        QPoint(self.drag_position + 6, 0),
                        QPoint(self.drag_position, 12)
                    ]
                    polygon = QPolygon(points)
                    painter.drawPolygon(polygon)

        except Exception as e:
            print(f"❌ PlayheadOverlay paintEvent 错误: {e}")
        finally:
            # 🔧 修复：确保 QPainter 正确结束
            if painter and painter.isActive():
                painter.end()
            self._is_painting = False

    def resizeEvent(self, event):
        """调整大小事件"""
        super().resizeEvent(event)


class TrimHandlesOverlay(QWidget):
    """专门的游标绘制层，覆盖整个轨道，支持往外拖扩展"""

    def __init__(self, parent=None):
        super().__init__(parent)


        # 设置透明背景，不阻挡鼠标事件
        self.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground, True)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)

        # 确保始终在最顶层
        self.raise_()
        # 设置最高层级显示
        self.setAttribute(Qt.WidgetAttribute.WA_AlwaysStackOnTop, True)

        # 防止重复绘制的标志
        self._is_painting = False



    def paintEvent(self, event):
        """绘制所有素材块的游标 - 最后绘制确保层级最高"""
        # 防止重复绘制
        if self._is_painting:
            return
        self._is_painting = True

        painter = None
        try:
            # 🔧 关键修复：先让所有子组件完成绘制
            super().paintEvent(event)

            # 然后绘制游标，确保在最顶层
            painter = QPainter(self)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)

            # 使用最高级别的合成模式，确保游标不被遮挡
            painter.setCompositionMode(QPainter.CompositionMode.CompositionMode_SourceOver)

            # 绘制所有VideoImageBlock的裁剪游标和扩展区
            parent = self.parent()
            if parent:
                # 导入VideoImageBlock类（新版）
                try:
                    from gui.components.video_image_block import VideoImageBlock
                    blocks = parent.findChildren(VideoImageBlock)
                    for block in blocks:
                        self.draw_trim_handles_for_block(painter, block)
                except ImportError:
                    # 如果VideoImageBlock还没有实现，尝试旧版
                    try:
                        from .video_thumbnail_block import VideoThumbnailBlock
                        blocks = parent.findChildren(VideoThumbnailBlock)
                        for block in blocks:
                            self.draw_trim_handles_for_block(painter, block)
                    except ImportError:
                        pass  # 都没有实现，跳过

        except Exception as e:
            print(f"❌ TrimHandlesOverlay paintEvent 错误: {e}")
        finally:
            # 🔧 修复：确保 QPainter 正确结束
            if painter and painter.isActive():
                painter.end()
            self._is_painting = False

    def draw_trim_handles_for_block(self, painter, block):
        """为指定的VideoThumbnailBlock绘制裁剪游标"""
        try:
            # 获取block的位置和尺寸
            block_rect = block.geometry()

            # 绘制左侧裁剪游标
            left_cursor_x = block_rect.left()
            painter.setPen(QPen(QColor(255, 215, 0), 3))  # 金色游标
            painter.drawLine(left_cursor_x, block_rect.top(), left_cursor_x, block_rect.bottom())

            # 绘制右侧裁剪游标
            right_cursor_x = block_rect.right()
            painter.drawLine(right_cursor_x, block_rect.top(), right_cursor_x, block_rect.bottom())

            # 绘制游标手柄
            handle_size = 8
            # 左手柄
            painter.fillRect(left_cursor_x - handle_size//2, block_rect.top(), handle_size, handle_size, QColor(255, 215, 0))
            painter.fillRect(left_cursor_x - handle_size//2, block_rect.bottom() - handle_size, handle_size, handle_size, QColor(255, 215, 0))

            # 右手柄
            painter.fillRect(right_cursor_x - handle_size//2, block_rect.top(), handle_size, handle_size, QColor(255, 215, 0))
            painter.fillRect(right_cursor_x - handle_size//2, block_rect.bottom() - handle_size, handle_size, handle_size, QColor(255, 215, 0))

        except Exception as e:
            print(f"❌ 绘制裁剪游标失败: {e}")

    def resizeEvent(self, event):
        """调整大小事件"""
        super().resizeEvent(event)


class TopLevelPlayhead(QWidget):
    """顶层播放头组件 - 在整个时间轴组件的最上层绘制播放头线"""

    def __init__(self, global_params, timeline_start_x=128, parent=None):
        super().__init__(parent)
        self.global_params = global_params
        self.timeline_start_x = timeline_start_x  # 时间轴开始的X位置（轨道标签宽度）
        self.scroll_area = None  # 滚动区域引用

        # 设置为透明背景的顶层覆盖层
        self.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground, True)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.setAttribute(Qt.WidgetAttribute.WA_AlwaysStackOnTop, True)
        self.setStyleSheet("background: transparent;")

        # 监听全局位置变化
        self.global_params.position_changed.connect(self.on_position_changed)

        print("🎯 TopLevelPlayhead 初始化完成")

    def set_scroll_area(self, scroll_area):
        """设置滚动区域引用"""
        self.scroll_area = scroll_area

    def on_position_changed(self, position):
        """位置变化时重绘"""
        self.update()

    def paintEvent(self, event):
        """绘制播放头线 - 从时间轴到轨道底部的完整线条"""
        if self.global_params.current_position < 0:
            return

        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 计算播放头在时间轴内容中的绝对位置
        timeline_x = self.global_params.time_to_pixels(self.global_params.current_position)

        # 🔧 修复：检查播放头是否在时间轴的有效范围内
        timeline_width = self.global_params.get_timeline_width()
        if timeline_x < 0 or timeline_x > timeline_width:
            return  # 播放头超出时间轴范围，不绘制

        # 获取滚动偏移量
        scroll_offset = 0
        if self.scroll_area:
            scroll_bar = self.scroll_area.horizontalScrollBar()
            if scroll_bar:
                scroll_offset = scroll_bar.value()

        # 播放头在窗口中的实际显示位置 = 轨道标签宽度 + 时间轴位置 - 滚动偏移
        x_pos = self.timeline_start_x + timeline_x - scroll_offset

        # 🔧 修复：只在时间轴区域内绘制
        if self.timeline_start_x <= x_pos <= self.width():
            # 🔧 修复：播放头从时间轴标尺开始绘制，不延伸到按钮栏
            # 按钮栏高度 + 时间轴标尺高度，播放头从时间轴标尺开始
            button_bar_height = 48  # 按钮栏高度
            ruler_height = self.global_params.ruler_height  # 时间轴标尺高度，通常是48px

            # 绘制播放头线 - 从时间轴标尺开始到底部
            painter.setPen(QPen(QColor(0, 200, 150), 3))  # #00c896颜色，3px宽度
            start_y = button_bar_height  # 从按钮栏下方（时间轴标尺开始）开始
            end_y = self.height()  # 到组件底部
            painter.drawLine(x_pos, start_y, x_pos, end_y)


class ScrollContentPlayhead(QWidget):
    """滚动内容播放头组件 - 使用移动组件位置的方式实现播放头，不使用频繁绘制"""

    def __init__(self, global_params, parent=None):
        super().__init__(parent)
        self.global_params = global_params

        # 创建播放头线条组件
        self.playhead_line = QWidget(self)
        self.playhead_line.setFixedWidth(3)  # 3px宽度
        self.playhead_line.setStyleSheet("""
            QWidget {
                background-color: #00C896;
                border: none;
            }
        """)

        # 设置为透明背景，但不阻止鼠标事件（因为在滚动内容中）
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground, True)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.setStyleSheet("background: transparent;")

        # 监听全局位置变化
        self.global_params.position_changed.connect(self.on_position_changed)

        # 初始化位置
        self.update_playhead_position()

        print("🎯 ScrollContentPlayhead 初始化完成（移动组件位置方式）")

    def on_position_changed(self, position):
        """位置变化时更新播放头位置"""
        self.update_playhead_position()

    def update_playhead_position(self):
        """更新播放头线条的位置"""
        if self.global_params.current_position < 0:
            self.playhead_line.hide()
            return

        # 计算播放头在时间轴内容中的位置
        timeline_x = self.global_params.time_to_pixels(self.global_params.current_position)

        # 检查播放头是否在时间轴的有效范围内
        timeline_width = self.global_params.get_timeline_width()
        if timeline_x < 0 or timeline_x > timeline_width:
            self.playhead_line.hide()
            return

        # 显示播放头线条
        self.playhead_line.show()

        # 移动播放头线条到正确位置（居中对齐）
        x_pos = timeline_x - 1  # 减1使3px宽的线条居中
        self.playhead_line.move(x_pos, 0)

        # 确保播放头线条在最上层
        self.playhead_line.raise_()

    def resizeEvent(self, event):
        """处理窗口大小变化事件"""
        super().resizeEvent(event)

        # 更新播放头线条的高度以填满整个区域
        if hasattr(self, 'playhead_line'):
            self.playhead_line.setFixedHeight(self.height())
            self.update_playhead_position()

        # 设置为透明背景的覆盖层，显示在最上层
        self.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground, True)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.setAttribute(Qt.WidgetAttribute.WA_AlwaysStackOnTop, True)
        self.setStyleSheet("background: transparent;")

        # 🔧 关键修复：确保播放头显示在所有内容之上
        self.raise_()  # 提升到最上层
        self.show()    # 确保可见

        # 监听全局位置变化
        self.global_params.position_changed.connect(self.on_position_changed)

        print("🎯 ScrollContentPlayhead 初始化完成（已弃用）")

    def on_position_changed(self, position):
        """位置变化时重绘"""
        self.update()

    def paintEvent(self, event):
        """绘制播放头线 - 从时间轴到轨道底部的完整线条"""
        if self.global_params.current_position < 0:
            return

        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 计算播放头X位置
        x_pos = self.global_params.time_to_pixels(self.global_params.current_position)

        # 绘制播放头线 - 贯穿整个高度
        painter.setPen(QPen(QColor(0, 200, 150), 3))  # #00c896颜色，3px宽度
        painter.drawLine(x_pos, 0, x_pos, self.height())

        # 在轨道区域不绘制三角形，只有线条
