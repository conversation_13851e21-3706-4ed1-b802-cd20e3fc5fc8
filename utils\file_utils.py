#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件操作工具模块
"""

import os
import mimetypes
from pathlib import Path
from typing import Dict, Optional, Union


def get_file_info(file_path: Union[str, Path]) -> Dict:
    """
    获取文件信息
    
    Args:
        file_path: 文件路径
        
    Returns:
        包含文件信息的字典
    """
    path = Path(file_path)
    
    if not path.exists():
        raise FileNotFoundError(f"文件不存在: {file_path}")
    
    stat = path.stat()
    mime_type, _ = mimetypes.guess_type(str(path))
    
    return {
        'name': path.name,
        'stem': path.stem,
        'suffix': path.suffix,
        'size': stat.st_size,
        'modified': stat.st_mtime,
        'created': stat.st_ctime,
        'mime_type': mime_type,
        'is_file': path.is_file(),
        'is_dir': path.is_dir(),
        'absolute_path': str(path.absolute())
    }


def validate_file_path(file_path: Union[str, Path]) -> bool:
    """
    验证文件路径是否有效
    
    Args:
        file_path: 文件路径
        
    Returns:
        路径是否有效
    """
    try:
        path = Path(file_path)
        return path.exists() and path.is_file()
    except Exception:
        return False


def get_file_size_human(size_bytes: int) -> str:
    """
    将字节大小转换为人类可读格式
    
    Args:
        size_bytes: 字节大小
        
    Returns:
        人类可读的大小字符串
    """
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"


def ensure_directory(dir_path: Union[str, Path]) -> Path:
    """
    确保目录存在，如果不存在则创建
    
    Args:
        dir_path: 目录路径
        
    Returns:
        目录路径对象
    """
    path = Path(dir_path)
    path.mkdir(parents=True, exist_ok=True)
    return path


def is_video_file(file_path: Union[str, Path]) -> bool:
    """
    检查文件是否为视频文件
    
    Args:
        file_path: 文件路径
        
    Returns:
        是否为视频文件
    """
    video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v'}
    return Path(file_path).suffix.lower() in video_extensions


def is_audio_file(file_path: Union[str, Path]) -> bool:
    """
    检查文件是否为音频文件
    
    Args:
        file_path: 文件路径
        
    Returns:
        是否为音频文件
    """
    audio_extensions = {'.mp3', '.wav', '.aac', '.flac', '.ogg', '.wma', '.m4a'}
    return Path(file_path).suffix.lower() in audio_extensions


def is_image_file(file_path: Union[str, Path]) -> bool:
    """
    检查文件是否为图像文件
    
    Args:
        file_path: 文件路径
        
    Returns:
        是否为图像文件
    """
    image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}
    return Path(file_path).suffix.lower() in image_extensions
