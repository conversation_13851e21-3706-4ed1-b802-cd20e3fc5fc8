#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间精度控制和容错处理系统
基于踩点转场技术方案实现毫秒级精度控制和多重容错机制
"""

import cv2
import numpy as np
from typing import Dict, List, Any, Tuple, Optional, Callable
from dataclasses import dataclass
from collections import deque
import time
import threading
from core.logger import get_logger
from core.beat_detection_engine import BeatPoint, BeatAnalysisResult


@dataclass
class FrameBuffer:
    """帧缓冲区"""
    timestamp: float  # 时间戳
    frame_data: np.ndarray  # 帧数据
    frame_index: int  # 帧索引
    is_keyframe: bool = False  # 是否为关键帧
    sync_confidence: float = 1.0  # 同步置信度


@dataclass
class SyncPoint:
    """同步点"""
    target_timestamp: float  # 目标时间戳
    actual_timestamp: float  # 实际时间戳
    deviation_ms: float  # 偏差（毫秒）
    correction_applied: bool = False  # 是否已应用修正
    correction_method: str = "NONE"  # 修正方法


@dataclass
class QualityMetrics:
    """质量指标"""
    sync_accuracy: float  # 同步精度
    frame_consistency: float  # 帧一致性
    temporal_stability: float  # 时间稳定性
    error_rate: float  # 错误率
    recovery_success_rate: float  # 恢复成功率


class PrecisionControlSystem:
    """时间精度控制系统"""
    
    def __init__(self, target_precision_ms: float = 50.0):
        """
        初始化精度控制系统
        
        Args:
            target_precision_ms: 目标精度（毫秒）
        """
        self.logger = get_logger('precision_control')
        self.target_precision_ms = target_precision_ms
        
        # 缓冲区配置
        self.buffer_size = 30  # 缓冲区大小（帧数）
        self.lookahead_frames = 10  # 前瞻帧数
        
        # 帧缓冲区
        self.frame_buffer: deque[FrameBuffer] = deque(maxlen=self.buffer_size)
        self.sync_points: List[SyncPoint] = []
        
        # 精度控制参数
        self.sync_tolerance_ms = target_precision_ms * 0.5  # 同步容差
        self.correction_threshold_ms = target_precision_ms * 2  # 修正阈值
        
        # 容错机制
        self.max_consecutive_errors = 3  # 最大连续错误数
        self.error_recovery_strategies = self._init_recovery_strategies()
        
        # 统计信息
        self.quality_metrics = QualityMetrics(0.0, 0.0, 0.0, 0.0, 0.0)
        self.error_count = 0
        self.consecutive_errors = 0
        self.total_corrections = 0
        
        # 线程安全
        self._lock = threading.Lock()
        
        self.logger.info(f"精度控制系统初始化完成，目标精度: ±{target_precision_ms}ms")
    
    def _init_recovery_strategies(self) -> Dict[str, Callable]:
        """初始化恢复策略"""
        return {
            "FRAME_INTERPOLATION": self._frame_interpolation_recovery,
            "TEMPORAL_SMOOTHING": self._temporal_smoothing_recovery,
            "BEAT_REALIGNMENT": self._beat_realignment_recovery,
            "FALLBACK_SYNC": self._fallback_sync_recovery
        }
    
    def add_frame_to_buffer(self, frame: np.ndarray, timestamp: float, frame_index: int):
        """添加帧到缓冲区"""
        try:
            with self._lock:
                # 检测关键帧
                is_keyframe = self._detect_keyframe(frame, frame_index)
                
                # 计算同步置信度
                sync_confidence = self._calculate_sync_confidence(timestamp, frame_index)
                
                frame_buffer = FrameBuffer(
                    timestamp=timestamp,
                    frame_data=frame.copy(),
                    frame_index=frame_index,
                    is_keyframe=is_keyframe,
                    sync_confidence=sync_confidence
                )
                
                self.frame_buffer.append(frame_buffer)
                
        except Exception as e:
            self.logger.error(f"添加帧到缓冲区失败: {e}")
    
    def _detect_keyframe(self, frame: np.ndarray, frame_index: int) -> bool:
        """检测关键帧"""
        try:
            # 简化的关键帧检测：基于帧差异
            if len(self.frame_buffer) == 0:
                return True  # 第一帧总是关键帧
            
            # 计算与前一帧的差异
            prev_frame = self.frame_buffer[-1].frame_data
            
            # 转换为灰度图
            gray_current = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            gray_prev = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)
            
            # 计算帧差
            diff = cv2.absdiff(gray_current, gray_prev)
            diff_ratio = np.sum(diff > 30) / diff.size
            
            # 如果差异超过阈值，认为是关键帧
            return diff_ratio > 0.3
            
        except Exception as e:
            self.logger.error(f"关键帧检测失败: {e}")
            return False
    
    def _calculate_sync_confidence(self, timestamp: float, frame_index: int) -> float:
        """计算同步置信度"""
        try:
            # 基于时间戳一致性计算置信度
            if len(self.frame_buffer) < 2:
                return 1.0
            
            # 计算期望的时间间隔
            expected_interval = 1.0 / 30.0  # 假设30fps
            
            # 计算实际时间间隔
            prev_timestamp = self.frame_buffer[-1].timestamp
            actual_interval = timestamp - prev_timestamp
            
            # 计算偏差
            deviation = abs(actual_interval - expected_interval)
            
            # 转换为置信度
            confidence = max(0.0, 1.0 - deviation / expected_interval)
            
            return float(confidence)
            
        except Exception as e:
            self.logger.error(f"同步置信度计算失败: {e}")
            return 0.5
    
    def sync_to_beat(self, target_beat: BeatPoint, current_timestamp: float) -> Tuple[bool, float, str]:
        """
        同步到节拍点
        
        Args:
            target_beat: 目标节拍点
            current_timestamp: 当前时间戳
            
        Returns:
            (是否成功, 修正后时间戳, 修正方法)
        """
        try:
            target_timestamp = target_beat.timestamp
            deviation_ms = abs(current_timestamp - target_timestamp) * 1000
            
            # 记录同步点
            sync_point = SyncPoint(
                target_timestamp=target_timestamp,
                actual_timestamp=current_timestamp,
                deviation_ms=deviation_ms
            )
            
            # 检查是否需要修正
            if deviation_ms <= self.sync_tolerance_ms:
                # 在容差范围内，不需要修正
                sync_point.correction_method = "NONE"
                self.sync_points.append(sync_point)
                return True, current_timestamp, "NONE"
            
            elif deviation_ms <= self.correction_threshold_ms:
                # 需要轻微修正
                corrected_timestamp, method = self._apply_minor_correction(
                    current_timestamp, target_timestamp, target_beat
                )
                sync_point.correction_applied = True
                sync_point.correction_method = method
                self.sync_points.append(sync_point)
                self.total_corrections += 1
                return True, corrected_timestamp, method
            
            else:
                # 需要重大修正或错误恢复
                success, corrected_timestamp, method = self._apply_error_recovery(
                    current_timestamp, target_timestamp, target_beat
                )
                sync_point.correction_applied = success
                sync_point.correction_method = method
                self.sync_points.append(sync_point)
                
                if success:
                    self.total_corrections += 1
                    self.consecutive_errors = 0
                else:
                    self.error_count += 1
                    self.consecutive_errors += 1
                
                return success, corrected_timestamp, method
            
        except Exception as e:
            self.logger.error(f"节拍同步失败: {e}")
            self.error_count += 1
            self.consecutive_errors += 1
            return False, current_timestamp, "ERROR"
    
    def _apply_minor_correction(self, current_timestamp: float, target_timestamp: float, 
                              target_beat: BeatPoint) -> Tuple[float, str]:
        """应用轻微修正"""
        try:
            # 基于节拍置信度选择修正策略
            if target_beat.confidence > 0.8:
                # 高置信度：直接对齐
                return target_timestamp, "DIRECT_ALIGN"
            
            elif target_beat.confidence > 0.5:
                # 中等置信度：加权平均
                weight = target_beat.confidence
                corrected = current_timestamp * (1 - weight) + target_timestamp * weight
                return corrected, "WEIGHTED_ALIGN"
            
            else:
                # 低置信度：渐进式修正
                correction_factor = 0.3
                correction = (target_timestamp - current_timestamp) * correction_factor
                return current_timestamp + correction, "GRADUAL_ALIGN"
            
        except Exception as e:
            self.logger.error(f"轻微修正失败: {e}")
            return current_timestamp, "ERROR"
    
    def _apply_error_recovery(self, current_timestamp: float, target_timestamp: float, 
                            target_beat: BeatPoint) -> Tuple[bool, float, str]:
        """应用错误恢复"""
        try:
            # 根据连续错误数选择恢复策略
            if self.consecutive_errors < self.max_consecutive_errors:
                # 尝试帧插值恢复
                return self._frame_interpolation_recovery(current_timestamp, target_timestamp, target_beat)
            
            elif self.consecutive_errors < self.max_consecutive_errors * 2:
                # 尝试时间平滑恢复
                return self._temporal_smoothing_recovery(current_timestamp, target_timestamp, target_beat)
            
            elif self.consecutive_errors < self.max_consecutive_errors * 3:
                # 尝试节拍重新对齐
                return self._beat_realignment_recovery(current_timestamp, target_timestamp, target_beat)
            
            else:
                # 使用备选同步策略
                return self._fallback_sync_recovery(current_timestamp, target_timestamp, target_beat)
            
        except Exception as e:
            self.logger.error(f"错误恢复失败: {e}")
            return False, current_timestamp, "RECOVERY_FAILED"
    
    def _frame_interpolation_recovery(self, current_timestamp: float, target_timestamp: float, 
                                    target_beat: BeatPoint) -> Tuple[bool, float, str]:
        """帧插值恢复"""
        try:
            # 在缓冲区中寻找合适的帧进行插值
            with self._lock:
                if len(self.frame_buffer) < 2:
                    return False, current_timestamp, "INSUFFICIENT_FRAMES"
                
                # 寻找目标时间戳附近的帧
                nearby_frames = []
                for frame_buf in self.frame_buffer:
                    time_diff = abs(frame_buf.timestamp - target_timestamp)
                    if time_diff < 0.1:  # 100ms窗口
                        nearby_frames.append((frame_buf, time_diff))
                
                if len(nearby_frames) >= 2:
                    # 选择最接近的两帧进行插值
                    nearby_frames.sort(key=lambda x: x[1])
                    frame1, frame2 = nearby_frames[0][0], nearby_frames[1][0]
                    
                    # 计算插值时间戳
                    t1, t2 = frame1.timestamp, frame2.timestamp
                    if t1 != t2:
                        alpha = (target_timestamp - t1) / (t2 - t1)
                        interpolated_timestamp = t1 + alpha * (t2 - t1)
                        return True, interpolated_timestamp, "FRAME_INTERPOLATION"
                
                return False, current_timestamp, "INTERPOLATION_FAILED"
            
        except Exception as e:
            self.logger.error(f"帧插值恢复失败: {e}")
            return False, current_timestamp, "INTERPOLATION_ERROR"
    
    def _temporal_smoothing_recovery(self, current_timestamp: float, target_timestamp: float, 
                                   target_beat: BeatPoint) -> Tuple[bool, float, str]:
        """时间平滑恢复"""
        try:
            # 使用移动平均平滑时间戳
            if len(self.sync_points) < 3:
                return False, current_timestamp, "INSUFFICIENT_HISTORY"
            
            # 计算最近几个同步点的平均偏差
            recent_points = self.sync_points[-3:]
            avg_deviation = np.mean([sp.deviation_ms for sp in recent_points])
            
            # 基于历史偏差进行预测修正
            predicted_correction = avg_deviation / 1000.0  # 转换为秒
            
            if target_timestamp > current_timestamp:
                corrected_timestamp = current_timestamp + predicted_correction
            else:
                corrected_timestamp = current_timestamp - predicted_correction
            
            return True, corrected_timestamp, "TEMPORAL_SMOOTHING"
            
        except Exception as e:
            self.logger.error(f"时间平滑恢复失败: {e}")
            return False, current_timestamp, "SMOOTHING_ERROR"
    
    def _beat_realignment_recovery(self, current_timestamp: float, target_timestamp: float, 
                                 target_beat: BeatPoint) -> Tuple[bool, float, str]:
        """节拍重新对齐恢复"""
        try:
            # 基于节拍模式重新计算对齐点
            if len(self.sync_points) < 5:
                return False, current_timestamp, "INSUFFICIENT_BEAT_HISTORY"
            
            # 分析节拍模式
            recent_intervals = []
            for i in range(1, min(5, len(self.sync_points))):
                interval = self.sync_points[-i].target_timestamp - self.sync_points[-i-1].target_timestamp
                recent_intervals.append(interval)
            
            if recent_intervals:
                avg_interval = np.mean(recent_intervals)
                
                # 基于平均间隔重新对齐
                last_good_sync = None
                for sp in reversed(self.sync_points):
                    if sp.deviation_ms <= self.sync_tolerance_ms:
                        last_good_sync = sp
                        break
                
                if last_good_sync:
                    beats_elapsed = round((target_timestamp - last_good_sync.target_timestamp) / avg_interval)
                    realigned_timestamp = last_good_sync.target_timestamp + beats_elapsed * avg_interval
                    return True, realigned_timestamp, "BEAT_REALIGNMENT"
            
            return False, current_timestamp, "REALIGNMENT_FAILED"
            
        except Exception as e:
            self.logger.error(f"节拍重新对齐失败: {e}")
            return False, current_timestamp, "REALIGNMENT_ERROR"
    
    def _fallback_sync_recovery(self, current_timestamp: float, target_timestamp: float, 
                              target_beat: BeatPoint) -> Tuple[bool, float, str]:
        """备选同步恢复"""
        try:
            # 最后的备选策略：使用固定的修正量
            max_correction = self.target_precision_ms / 1000.0  # 转换为秒
            
            time_diff = target_timestamp - current_timestamp
            
            if abs(time_diff) <= max_correction:
                # 在可接受范围内，直接对齐
                return True, target_timestamp, "FALLBACK_DIRECT"
            else:
                # 超出范围，应用最大修正
                if time_diff > 0:
                    corrected = current_timestamp + max_correction
                else:
                    corrected = current_timestamp - max_correction
                
                return True, corrected, "FALLBACK_LIMITED"
            
        except Exception as e:
            self.logger.error(f"备选同步恢复失败: {e}")
            return False, current_timestamp, "FALLBACK_ERROR"
    
    def validate_multi_track_alignment(self, tracks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """多轨对齐校验"""
        try:
            validation_result = {
                "is_aligned": True,
                "max_deviation_ms": 0.0,
                "track_deviations": {},
                "alignment_quality": 1.0
            }
            
            if len(tracks) < 2:
                return validation_result
            
            # 以第一轨为基准
            reference_track = tracks[0]
            reference_timestamps = reference_track.get("timestamps", [])
            
            max_deviation = 0.0
            total_deviations = []
            
            for i, track in enumerate(tracks[1:], 1):
                track_timestamps = track.get("timestamps", [])
                track_deviations = []
                
                # 比较对应的时间戳
                min_length = min(len(reference_timestamps), len(track_timestamps))
                
                for j in range(min_length):
                    ref_time = reference_timestamps[j]
                    track_time = track_timestamps[j]
                    deviation_ms = abs(ref_time - track_time) * 1000
                    
                    track_deviations.append(deviation_ms)
                    total_deviations.append(deviation_ms)
                    max_deviation = max(max_deviation, deviation_ms)
                
                validation_result["track_deviations"][f"track_{i}"] = {
                    "avg_deviation_ms": np.mean(track_deviations) if track_deviations else 0.0,
                    "max_deviation_ms": np.max(track_deviations) if track_deviations else 0.0,
                    "alignment_points": len(track_deviations)
                }
            
            validation_result["max_deviation_ms"] = max_deviation
            validation_result["is_aligned"] = max_deviation <= self.target_precision_ms
            
            # 计算对齐质量
            if total_deviations:
                avg_deviation = np.mean(total_deviations)
                alignment_quality = max(0.0, 1.0 - avg_deviation / self.target_precision_ms)
                validation_result["alignment_quality"] = alignment_quality
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"多轨对齐校验失败: {e}")
            return {"is_aligned": False, "error": str(e)}
    
    def get_quality_metrics(self) -> QualityMetrics:
        """获取质量指标"""
        try:
            if not self.sync_points:
                return self.quality_metrics
            
            # 计算同步精度
            deviations = [sp.deviation_ms for sp in self.sync_points]
            accurate_syncs = sum(1 for d in deviations if d <= self.target_precision_ms)
            sync_accuracy = accurate_syncs / len(deviations) if deviations else 0.0
            
            # 计算帧一致性
            with self._lock:
                if len(self.frame_buffer) > 1:
                    confidences = [fb.sync_confidence for fb in self.frame_buffer]
                    frame_consistency = np.mean(confidences)
                else:
                    frame_consistency = 1.0
            
            # 计算时间稳定性
            if len(deviations) > 1:
                deviation_std = np.std(deviations)
                temporal_stability = max(0.0, 1.0 - deviation_std / self.target_precision_ms)
            else:
                temporal_stability = 1.0
            
            # 计算错误率
            total_operations = len(self.sync_points)
            error_rate = self.error_count / total_operations if total_operations > 0 else 0.0
            
            # 计算恢复成功率
            recovery_attempts = self.error_count
            recovery_successes = self.total_corrections
            recovery_success_rate = recovery_successes / recovery_attempts if recovery_attempts > 0 else 1.0
            
            self.quality_metrics = QualityMetrics(
                sync_accuracy=sync_accuracy,
                frame_consistency=frame_consistency,
                temporal_stability=temporal_stability,
                error_rate=error_rate,
                recovery_success_rate=recovery_success_rate
            )
            
            return self.quality_metrics
            
        except Exception as e:
            self.logger.error(f"质量指标计算失败: {e}")
            return QualityMetrics(0.0, 0.0, 0.0, 1.0, 0.0)
    
    def reset_system(self):
        """重置系统状态"""
        try:
            with self._lock:
                self.frame_buffer.clear()
                self.sync_points.clear()
                self.error_count = 0
                self.consecutive_errors = 0
                self.total_corrections = 0
                self.quality_metrics = QualityMetrics(0.0, 0.0, 0.0, 0.0, 0.0)
            
            self.logger.info("精度控制系统已重置")
            
        except Exception as e:
            self.logger.error(f"系统重置失败: {e}")
    
    def export_sync_report(self, output_path: str) -> bool:
        """导出同步报告"""
        try:
            metrics = self.get_quality_metrics()
            
            report = {
                "system_config": {
                    "target_precision_ms": self.target_precision_ms,
                    "sync_tolerance_ms": self.sync_tolerance_ms,
                    "correction_threshold_ms": self.correction_threshold_ms
                },
                "quality_metrics": {
                    "sync_accuracy": metrics.sync_accuracy,
                    "frame_consistency": metrics.frame_consistency,
                    "temporal_stability": metrics.temporal_stability,
                    "error_rate": metrics.error_rate,
                    "recovery_success_rate": metrics.recovery_success_rate
                },
                "statistics": {
                    "total_sync_points": len(self.sync_points),
                    "total_errors": self.error_count,
                    "total_corrections": self.total_corrections,
                    "consecutive_errors": self.consecutive_errors
                },
                "sync_points": [
                    {
                        "target_timestamp": sp.target_timestamp,
                        "actual_timestamp": sp.actual_timestamp,
                        "deviation_ms": sp.deviation_ms,
                        "correction_applied": sp.correction_applied,
                        "correction_method": sp.correction_method
                    }
                    for sp in self.sync_points
                ]
            }
            
            import json
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=4, ensure_ascii=False)
            
            self.logger.info(f"同步报告已导出: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出同步报告失败: {e}")
            return False
