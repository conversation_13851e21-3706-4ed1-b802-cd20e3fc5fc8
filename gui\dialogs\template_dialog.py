#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
理发店模板管理对话框
"""

import os
from pathlib import Path
from typing import Optional, Dict, List, Any

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QTabWidget,
    QLabel, QPushButton, QLineEdit, QTextEdit, QComboBox, QSpinBox,
    QDoubleSpinBox, QSlider, QCheckBox, QGroupBox, QListWidget, 
    QListWidgetItem, QFileDialog, QMessageBox, QProgressBar,
    QTableWidget, QTableWidgetItem, QHeaderView, QSplitter,
    QTreeWidget, QTreeWidgetItem, QFrame, QScrollArea, QWidget
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont, QIcon, QPixmap

from core.templates.template_manager import (
    HairSalonTemplate, HairSalonTemplateManager,
    TemplateSegment, TemplateMusic, TemplateTransition,
    TemplateFilter, TemplateWatermark
)

class TemplateSegmentWidget(QGroupBox):
    """模版片段编辑组件"""
    
    segment_changed = Signal(str, dict)  # segment_type, segment_data
    
    def __init__(self, segment: TemplateSegment, parent=None):
        super().__init__(f"{segment.name} ({segment.segment_type})", parent)
        self.segment = segment
        self.init_ui()
    
    def init_ui(self):
        layout = QGridLayout(self)
        
        # 基本信息
        layout.addWidget(QLabel("时长(秒):"), 0, 0)
        self.duration_spin = QDoubleSpinBox()
        self.duration_spin.setRange(0.1, 300.0)
        self.duration_spin.setValue(self.segment.duration)
        self.duration_spin.valueChanged.connect(self.on_segment_changed)
        layout.addWidget(self.duration_spin, 0, 1)
        
        layout.addWidget(QLabel("位置(秒):"), 0, 2)
        self.position_spin = QDoubleSpinBox()
        self.position_spin.setRange(0.0, 3600.0)
        self.position_spin.setValue(self.segment.position)
        self.position_spin.valueChanged.connect(self.on_segment_changed)
        layout.addWidget(self.position_spin, 0, 3)
        
        # 视频文件
        layout.addWidget(QLabel("视频文件:"), 1, 0)
        self.video_path_edit = QLineEdit(self.segment.video_path or "")
        layout.addWidget(self.video_path_edit, 1, 1, 1, 2)
        
        browse_btn = QPushButton("浏览...")
        browse_btn.clicked.connect(self.browse_video_file)
        layout.addWidget(browse_btn, 1, 3)
        
        # 必需标记
        self.required_check = QCheckBox("必需片段")
        self.required_check.setChecked(self.segment.required)
        self.required_check.toggled.connect(self.on_segment_changed)
        layout.addWidget(self.required_check, 2, 0, 1, 2)
        
        # 预览按钮
        preview_btn = QPushButton("预览")
        preview_btn.clicked.connect(self.preview_segment)
        layout.addWidget(preview_btn, 2, 2, 1, 2)
    
    def browse_video_file(self):
        """浏览视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, f"选择{self.segment.name}视频文件",
            "", "视频文件 (*.mp4 *.avi *.mov *.mkv);;所有文件 (*.*)"
        )
        if file_path:
            self.video_path_edit.setText(file_path)
            self.on_segment_changed()
    
    def preview_segment(self):
        """预览片段"""
        try:
            video_path = self.video_path_edit.text().strip()
            if not video_path:
                QMessageBox.information(self, "预览", f"请先选择 {self.segment.name} 的视频文件")
                return
            
            if not os.path.exists(video_path):
                QMessageBox.warning(self, "警告", f"视频文件不存在：{video_path}")
                return
            
            # 尝试使用系统默认播放器预览
            try:
                import subprocess
                import platform
                
                system = platform.system()
                if system == "Darwin":  # macOS
                    subprocess.run(["open", video_path])
                elif system == "Windows":
                    subprocess.run(["start", video_path], shell=True)
                else:  # Linux
                    subprocess.run(["xdg-open", video_path])
                
                print(f"✅ 正在预览片段: {self.segment.name} - {video_path}")
                
            except Exception as e:
                print(f"⚠️ 系统播放器启动失败: {e}")
                # 显示文件信息作为备选
                self.show_video_info(video_path)
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"预览片段失败：{str(e)}")
    
    def show_video_info(self, video_path: str):
        """显示视频文件信息"""
        try:
            import cv2
            
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                QMessageBox.warning(self, "警告", "无法打开视频文件")
                return
            
            # 获取视频信息
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
            duration = frame_count / fps if fps > 0 else 0
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            cap.release()
            
            info_text = f"视频信息：{self.segment.name}\n\n"
            info_text += f"文件路径：{video_path}\n"
            info_text += f"分辨率：{width} x {height}\n"
            info_text += f"帧率：{fps:.2f} FPS\n"
            info_text += f"时长：{duration:.2f} 秒\n"
            info_text += f"总帧数：{int(frame_count)}\n"
            
            QMessageBox.information(self, "视频信息", info_text)
            
        except ImportError:
            QMessageBox.information(self, "预览", f"视频文件：{video_path}\n\n请使用外部播放器查看")
        except Exception as e:
            QMessageBox.warning(self, "警告", f"获取视频信息失败：{str(e)}")
    
    def on_segment_changed(self):
        """片段数据改变"""
        segment_data = {
            'duration': self.duration_spin.value(),
            'position': self.position_spin.value(),
            'video_path': self.video_path_edit.text(),
            'required': self.required_check.isChecked()
        }
        self.segment_changed.emit(self.segment.segment_type, segment_data)
    
    def get_segment_data(self) -> dict:
        """获取片段数据"""
        return {
            'segment_type': self.segment.segment_type,
            'name': self.segment.name,
            'duration': self.duration_spin.value(),
            'position': self.position_spin.value(),
            'video_path': self.video_path_edit.text(),
            'required': self.required_check.isChecked()
        }

class TemplateMusicWidget(QGroupBox):
    """模版音乐编辑组件"""
    
    music_changed = Signal(dict)
    
    def __init__(self, music: TemplateMusic, parent=None):
        super().__init__(f"背景音乐 - {music.name}", parent)
        self.music = music
        self.init_ui()
    
    def init_ui(self):
        layout = QGridLayout(self)
        
        # 音乐文件
        layout.addWidget(QLabel("音乐文件:"), 0, 0)
        self.audio_path_edit = QLineEdit(self.music.audio_path or "")
        layout.addWidget(self.audio_path_edit, 0, 1, 1, 2)
        
        browse_btn = QPushButton("浏览...")
        browse_btn.clicked.connect(self.browse_audio_file)
        layout.addWidget(browse_btn, 0, 3)
        
        # 音量控制
        layout.addWidget(QLabel("音量:"), 1, 0)
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(int(self.music.volume * 100))
        self.volume_slider.valueChanged.connect(self.on_volume_changed)
        layout.addWidget(self.volume_slider, 1, 1, 1, 2)
        
        self.volume_label = QLabel(f"{int(self.music.volume * 100)}%")
        layout.addWidget(self.volume_label, 1, 3)
        
        # 淡入淡出
        layout.addWidget(QLabel("淡入(秒):"), 2, 0)
        self.fade_in_spin = QDoubleSpinBox()
        self.fade_in_spin.setRange(0.0, 10.0)
        self.fade_in_spin.setValue(self.music.fade_in)
        layout.addWidget(self.fade_in_spin, 2, 1)
        
        layout.addWidget(QLabel("淡出(秒):"), 2, 2)
        self.fade_out_spin = QDoubleSpinBox()
        self.fade_out_spin.setRange(0.0, 10.0)
        self.fade_out_spin.setValue(self.music.fade_out)
        layout.addWidget(self.fade_out_spin, 2, 3)
        
        # 循环播放
        self.loop_check = QCheckBox("循环播放")
        self.loop_check.setChecked(self.music.loop)
        layout.addWidget(self.loop_check, 3, 0, 1, 2)
        
        # 试听按钮
        preview_btn = QPushButton("试听")
        preview_btn.clicked.connect(self.preview_music)
        layout.addWidget(preview_btn, 3, 2, 1, 2)
    
    def browse_audio_file(self):
        """浏览音频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择背景音乐文件",
            "", "音频文件 (*.mp3 *.wav *.aac *.m4a);;所有文件 (*.*)"
        )
        if file_path:
            self.audio_path_edit.setText(file_path)
    
    def on_volume_changed(self, value):
        """音量改变"""
        self.volume_label.setText(f"{value}%")
    
    def preview_music(self):
        """试听音乐"""
        try:
            audio_path = self.audio_path_edit.text().strip()
            if not audio_path:
                QMessageBox.information(self, "试听", f"请先选择 {self.music.name} 的音频文件")
                return
            
            if not os.path.exists(audio_path):
                QMessageBox.warning(self, "警告", f"音频文件不存在：{audio_path}")
                return
            
            # 尝试使用系统默认播放器试听
            try:
                import subprocess
                import platform
                
                system = platform.system()
                if system == "Darwin":  # macOS
                    subprocess.run(["open", audio_path])
                elif system == "Windows":
                    subprocess.run(["start", audio_path], shell=True)
                else:  # Linux
                    subprocess.run(["xdg-open", audio_path])
                
                print(f"✅ 正在试听音乐: {self.music.name} - {audio_path}")
                
            except Exception as e:
                print(f"⚠️ 系统播放器启动失败: {e}")
                # 显示音频信息作为备选
                self.show_audio_info(audio_path)
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"试听音乐失败：{str(e)}")
    
    def show_audio_info(self, audio_path: str):
        """显示音频文件信息"""
        try:
            # 尝试使用ffprobe获取音频信息
            import subprocess
            import json
            
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', audio_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')
            if result.returncode == 0 and result.stdout:
                try:
                    data = json.loads(result.stdout)
                except (json.JSONDecodeError, ValueError) as e:
                    QMessageBox.information(self, "音频信息", f"解析ffprobe输出失败: {e}\n输出内容: {result.stdout[:200]}...")
                    return
                format_info = data.get('format', {})
                
                duration = float(format_info.get('duration', 0))
                bit_rate = format_info.get('bit_rate', 'N/A')
                
                info_text = f"音频信息：{self.music.name}\n\n"
                info_text += f"文件路径：{audio_path}\n"
                info_text += f"时长：{duration:.2f} 秒\n"
                info_text += f"比特率：{bit_rate}\n"
                
                # 获取音频流信息
                for stream in data.get('streams', []):
                    if stream.get('codec_type') == 'audio':
                        codec = stream.get('codec_name', 'N/A')
                        sample_rate = stream.get('sample_rate', 'N/A')
                        channels = stream.get('channels', 'N/A')
                        
                        info_text += f"编码：{codec}\n"
                        info_text += f"采样率：{sample_rate} Hz\n"
                        info_text += f"声道数：{channels}\n"
                        break
                
                QMessageBox.information(self, "音频信息", info_text)
            else:
                QMessageBox.information(self, "试听", f"音频文件：{audio_path}\n\n请使用外部播放器试听")
                
        except Exception as e:
            QMessageBox.information(self, "试听", f"音频文件：{audio_path}\n\n请使用外部播放器试听")
    
    def get_music_data(self) -> dict:
        """获取音乐数据"""
        return {
            'name': self.music.name,
            'audio_path': self.audio_path_edit.text(),
            'volume': self.volume_slider.value() / 100.0,
            'fade_in': self.fade_in_spin.value(),
            'fade_out': self.fade_out_spin.value(),
            'loop': self.loop_check.isChecked()
        }

class TemplateWatermarkWidget(QGroupBox):
    """模版水印编辑组件"""
    
    watermark_changed = Signal(dict)
    
    def __init__(self, watermark: TemplateWatermark, parent=None):
        super().__init__(f"水印 - {watermark.name}", parent)
        self.watermark = watermark
        self.init_ui()
    
    def init_ui(self):
        layout = QGridLayout(self)
        
        # 水印内容
        layout.addWidget(QLabel("水印文字:"), 0, 0)
        self.content_edit = QLineEdit(self.watermark.content)
        layout.addWidget(self.content_edit, 0, 1, 1, 3)
        
        # 位置
        layout.addWidget(QLabel("位置:"), 1, 0)
        self.position_combo = QComboBox()
        self.position_combo.addItems([
            "top-left", "top-right", "bottom-left", "bottom-right", "center"
        ])
        self.position_combo.setCurrentText(self.watermark.position)
        layout.addWidget(self.position_combo, 1, 1)
        
        # 透明度
        layout.addWidget(QLabel("透明度:"), 1, 2)
        self.opacity_spin = QDoubleSpinBox()
        self.opacity_spin.setRange(0.0, 1.0)
        self.opacity_spin.setSingleStep(0.1)
        self.opacity_spin.setValue(self.watermark.opacity)
        layout.addWidget(self.opacity_spin, 1, 3)
        
        # 字体设置
        layout.addWidget(QLabel("字体大小:"), 2, 0)
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 72)
        self.font_size_spin.setValue(self.watermark.font_size)
        layout.addWidget(self.font_size_spin, 2, 1)
        
        layout.addWidget(QLabel("字体颜色:"), 2, 2)
        self.font_color_edit = QLineEdit(self.watermark.font_color)
        layout.addWidget(self.font_color_edit, 2, 3)
        
        # 显示时间
        layout.addWidget(QLabel("开始时间:"), 3, 0)
        self.start_time_spin = QDoubleSpinBox()
        self.start_time_spin.setRange(0.0, 3600.0)
        self.start_time_spin.setValue(self.watermark.start_time)
        layout.addWidget(self.start_time_spin, 3, 1)
        
        layout.addWidget(QLabel("持续时间:"), 3, 2)
        self.duration_spin = QDoubleSpinBox()
        self.duration_spin.setRange(0.1, 3600.0)
        self.duration_spin.setValue(self.watermark.duration or 5.0)
        layout.addWidget(self.duration_spin, 3, 3)
    
    def get_watermark_data(self) -> dict:
        """获取水印数据"""
        return {
            'name': self.watermark.name,
            'content': self.content_edit.text(),
            'content_type': 'text',
            'position': self.position_combo.currentText(),
            'opacity': self.opacity_spin.value(),
            'font_size': self.font_size_spin.value(),
            'font_color': self.font_color_edit.text(),
            'start_time': self.start_time_spin.value(),
            'duration': self.duration_spin.value()
        }

class HairSalonTemplateDialog(QDialog):
    """理发店模板管理对话框"""
    
    template_saved = Signal(str)  # template_id
    
    def __init__(self, template_manager: HairSalonTemplateManager, 
                 template: Optional[HairSalonTemplate] = None, parent=None):
        super().__init__(parent)
        self.template_manager = template_manager
        self.template = template
        self.is_new_template = template is None
        
        self.segment_widgets = {}
        self.music_widgets = []
        self.watermark_widgets = []
        
        self.init_ui()
        self.load_template_data()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("理发店视频模板" + ("编辑" if not self.is_new_template else "创建"))
        self.setModal(True)
        self.resize(800, 600)
        
        layout = QVBoxLayout(self)
        
        # 基本信息
        basic_group = QGroupBox("基本信息")
        basic_layout = QGridLayout(basic_group)
        
        basic_layout.addWidget(QLabel("模板名称:"), 0, 0)
        self.name_edit = QLineEdit()
        basic_layout.addWidget(self.name_edit, 0, 1, 1, 2)
        
        basic_layout.addWidget(QLabel("描述:"), 1, 0)
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(60)
        basic_layout.addWidget(self.description_edit, 1, 1, 1, 2)
        
        basic_layout.addWidget(QLabel("总时长(秒):"), 2, 0)
        self.total_duration_spin = QDoubleSpinBox()
        self.total_duration_spin.setRange(10.0, 600.0)
        self.total_duration_spin.setValue(60.0)
        basic_layout.addWidget(self.total_duration_spin, 2, 1)
        
        layout.addWidget(basic_group)
        
        # 选项卡
        tab_widget = QTabWidget()
        
        # 视频片段选项卡
        segments_tab = self.create_segments_tab()
        tab_widget.addTab(segments_tab, "视频片段")
        
        # 背景音乐选项卡
        music_tab = self.create_music_tab()
        tab_widget.addTab(music_tab, "背景音乐")
        
        # 水印选项卡
        watermark_tab = self.create_watermark_tab()
        tab_widget.addTab(watermark_tab, "水印署名")
        
        # 高级设置选项卡
        advanced_tab = self.create_advanced_tab()
        tab_widget.addTab(advanced_tab, "高级设置")
        
        layout.addWidget(tab_widget)
        
        # 按钮组
        button_layout = QHBoxLayout()
        
        # 预览按钮
        preview_btn = QPushButton("预览模板")
        preview_btn.clicked.connect(self.preview_template)
        button_layout.addWidget(preview_btn)
        
        button_layout.addStretch()
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        # 保存按钮
        save_btn = QPushButton("保存模板")
        save_btn.clicked.connect(self.save_template)
        button_layout.addWidget(save_btn)
        
        layout.addLayout(button_layout)
    
    def create_segments_tab(self):
        """创建视频片段选项卡"""
        tab = QScrollArea()
        tab.setWidgetResizable(True)
        
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        
        # 创建默认片段组件
        default_segments = [
            TemplateSegment("intro", "开场介绍", 5.0, 0.0, True),
            TemplateSegment("before", "改造前", 10.0, 5.0, True),
            TemplateSegment("process", "美发过程", 30.0, 15.0, True),
            TemplateSegment("after", "改造后", 10.0, 45.0, True),
            TemplateSegment("outro", "片尾署名", 5.0, 55.0, True)
        ]
        
        for segment in default_segments:
            widget = TemplateSegmentWidget(segment)
            widget.segment_changed.connect(self.on_segment_changed)
            self.segment_widgets[segment.segment_type] = widget
            layout.addWidget(widget)
        
        layout.addStretch()
        tab.setWidget(content_widget)
        return tab
    
    def create_music_tab(self):
        """创建背景音乐选项卡"""
        tab = QScrollArea()
        tab.setWidgetResizable(True)
        
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        
        # 添加音乐按钮
        add_music_btn = QPushButton("添加背景音乐")
        add_music_btn.clicked.connect(self.add_music)
        layout.addWidget(add_music_btn)
        
        # 音乐列表容器
        self.music_container = QVBoxLayout()
        layout.addLayout(self.music_container)
        
        layout.addStretch()
        tab.setWidget(content_widget)
        return tab
    
    def create_watermark_tab(self):
        """创建水印选项卡"""
        tab = QScrollArea()
        tab.setWidgetResizable(True)
        
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        
        # 添加水印按钮
        add_watermark_btn = QPushButton("添加水印")
        add_watermark_btn.clicked.connect(self.add_watermark)
        layout.addWidget(add_watermark_btn)
        
        # 水印列表容器
        self.watermark_container = QVBoxLayout()
        layout.addLayout(self.watermark_container)
        
        layout.addStretch()
        tab.setWidget(content_widget)
        return tab
    
    def create_advanced_tab(self):
        """创建高级设置选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 视频设置
        video_group = QGroupBox("视频设置")
        video_layout = QGridLayout(video_group)
        
        video_layout.addWidget(QLabel("分辨率:"), 0, 0)
        self.resolution_combo = QComboBox()
        self.resolution_combo.addItems(["1920x1080", "1280x720", "960x540"])
        video_layout.addWidget(self.resolution_combo, 0, 1)
        
        video_layout.addWidget(QLabel("帧率:"), 0, 2)
        self.fps_spin = QSpinBox()
        self.fps_spin.setRange(15, 60)
        self.fps_spin.setValue(30)
        video_layout.addWidget(self.fps_spin, 0, 3)
        
        layout.addWidget(video_group)
        
        # 音频设置
        audio_group = QGroupBox("音频设置")
        audio_layout = QGridLayout(audio_group)
        
        audio_layout.addWidget(QLabel("采样率:"), 0, 0)
        self.sample_rate_combo = QComboBox()
        self.sample_rate_combo.addItems(["44100", "48000", "96000"])
        audio_layout.addWidget(self.sample_rate_combo, 0, 1)
        
        layout.addWidget(audio_group)
        
        layout.addStretch()
        return tab
    
    def add_music(self):
        """添加背景音乐"""
        music = TemplateMusic(
            name=f"背景音乐 {len(self.music_widgets) + 1}",
            audio_path="",
            volume=0.8
        )
        
        widget = TemplateMusicWidget(music)
        self.music_widgets.append(widget)
        self.music_container.addWidget(widget)
    
    def add_watermark(self):
        """添加水印"""
        watermark = TemplateWatermark(
            name=f"水印 {len(self.watermark_widgets) + 1}",
            content="高端理发店",
            content_type="text",
            start_time=55.0,
            duration=5.0
        )
        
        widget = TemplateWatermarkWidget(watermark)
        self.watermark_widgets.append(widget)
        self.watermark_container.addWidget(widget)
    
    def on_segment_changed(self, segment_type: str, segment_data: dict):
        """片段数据改变"""
        # 自动重新计算所有片段的时间位置
        self.auto_calculate_positions()
        
        # 更新总时长
        total_duration = 0
        for widget in self.segment_widgets.values():
            data = widget.get_segment_data()
            end_time = data['position'] + data['duration']
            total_duration = max(total_duration, end_time)
        
        self.total_duration_spin.setValue(total_duration)
    
    def auto_calculate_positions(self):
        """自动计算片段位置 - 根据设置的时长自动推算时间位置"""
        try:
            # 按照预设顺序重新计算位置，确保从视频开始的片段时长自动推算
            segment_order = ["intro", "before", "process", "after", "outro"]
            current_position = 0.0
            
            # 首先收集所有片段的时长
            segments_info = {}
            for segment_type in segment_order:
                if segment_type in self.segment_widgets:
                    widget = self.segment_widgets[segment_type]
                    duration = widget.duration_spin.value()
                    segments_info[segment_type] = {
                        'widget': widget,
                        'duration': duration,
                        'position': current_position
                    }
                    current_position += duration
            
            # 现在按顺序更新位置（不触发信号避免递归）
            for segment_type in segment_order:
                if segment_type in segments_info:
                    info = segments_info[segment_type]
                    widget = info['widget']
                    
                    # 更新位置
                    widget.position_spin.blockSignals(True)
                    widget.position_spin.setValue(info['position'])
                    widget.position_spin.blockSignals(False)
                    
                    print(f"📍 {segment_type}: {info['position']:.1f}s - {info['position'] + info['duration']:.1f}s (时长: {info['duration']:.1f}s)")
            
            # 更新总时长
            self.total_duration_spin.blockSignals(True)
            self.total_duration_spin.setValue(current_position)
            self.total_duration_spin.blockSignals(False)
            
            print(f"✅ 自动计算片段位置完成，总时长: {current_position:.1f}秒")
            
            # 触发一次保存以确保数据同步
            self.on_segment_changed()
            
        except Exception as e:
            print(f"❌ 自动计算位置失败: {e}")
    
    def load_template_data(self):
        """加载模板数据"""
        if not self.template:
            # 新模板，添加默认音乐和水印
            self.add_music()
            self.add_watermark()
            return
        
        # 加载现有模板数据
        self.name_edit.setText(self.template.name)
        self.description_edit.setText(self.template.description)
        self.total_duration_spin.setValue(self.template.total_duration)
        
        # 加载片段数据
        for segment in self.template.segments:
            if segment.segment_type in self.segment_widgets:
                widget = self.segment_widgets[segment.segment_type]
                widget.duration_spin.setValue(segment.duration)
                widget.position_spin.setValue(segment.position)
                widget.video_path_edit.setText(segment.video_path or "")
                widget.required_check.setChecked(segment.required)
        
        # 加载音乐数据
        for music in self.template.music:
            widget = TemplateMusicWidget(music)
            self.music_widgets.append(widget)
            self.music_container.addWidget(widget)
        
        # 加载水印数据
        for watermark in self.template.watermarks:
            widget = TemplateWatermarkWidget(watermark)
            self.watermark_widgets.append(widget)
            self.watermark_container.addWidget(widget)
    
    def preview_template(self):
        """预览模板"""
        try:
            # 收集当前模板数据
            template_data = self.collect_template_data()
            if not template_data:
                return
            
            # 创建预览对话框
            from gui.template_preview import TemplatePreviewDialog
            preview_dialog = TemplatePreviewDialog(template_data, self)
            preview_dialog.exec()
            
        except ImportError:
            # 如果预览对话框不存在，显示简单预览信息
            self.show_simple_preview()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"预览模板失败：{str(e)}")
    
    def show_simple_preview(self):
        """显示简单的模板预览信息"""
        try:
            template_data = self.collect_template_data()
            if not template_data:
                return
            
            preview_text = f"模板预览：{template_data['name']}\n\n"
            preview_text += f"总时长：{template_data['total_duration']:.1f}秒\n"
            preview_text += f"描述：{template_data['description']}\n\n"
            
            preview_text += "视频片段：\n"
            for segment in template_data['segments']:
                status = "✅" if segment.get('video_path') else "⏳"
                preview_text += f"{status} {segment['name']}: {segment['position']:.1f}s - {segment['position'] + segment['duration']:.1f}s ({segment['duration']:.1f}s)\n"
            
            if template_data['music']:
                preview_text += "\n背景音乐：\n"
                for music in template_data['music']:
                    status = "✅" if music.get('audio_path') else "⏳"
                    preview_text += f"{status} {music['name']}: 音量{int(music['volume']*100)}%\n"
            
            if template_data['watermarks']:
                preview_text += "\n水印署名：\n"
                for watermark in template_data['watermarks']:
                    preview_text += f"📝 {watermark['content']} ({watermark['position']}, {watermark['start_time']:.1f}s)\n"
            
            QMessageBox.information(self, "模板预览", preview_text)
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"生成预览信息失败：{str(e)}")
    
    def collect_template_data(self) -> dict:
        """收集当前模板数据用于预览"""
        try:
            if not self.name_edit.text().strip():
                QMessageBox.warning(self, "警告", "请先输入模板名称")
                return None
            
            # 收集片段数据
            segments = []
            for widget in self.segment_widgets.values():
                data = widget.get_segment_data()
                segments.append(data)
            
            # 收集音乐数据
            music = []
            for widget in self.music_widgets:
                data = widget.get_music_data()
                music.append(data)
            
            # 收集水印数据
            watermarks = []
            for widget in self.watermark_widgets:
                data = widget.get_watermark_data()
                watermarks.append(data)
            
            return {
                'name': self.name_edit.text().strip(),
                'description': self.description_edit.toPlainText().strip(),
                'total_duration': self.total_duration_spin.value(),
                'segments': segments,
                'music': music,
                'watermarks': watermarks,
                'resolution': self.resolution_combo.currentText(),
                'fps': self.fps_spin.value(),
                'sample_rate': self.sample_rate_combo.currentText()
            }
            
        except Exception as e:
            print(f"❌ 收集模板数据失败: {e}")
            return None
    
    def save_template(self):
        """保存模板"""
        try:
            # 验证数据
            if not self.name_edit.text().strip():
                QMessageBox.warning(self, "警告", "请输入模板名称")
                return
            
            # 自动计算最终位置
            self.auto_calculate_positions()
            
            # 创建或更新模板
            if self.is_new_template:
                template = self.template_manager.create_default_template(
                    self.name_edit.text().strip(),
                    self.description_edit.toPlainText().strip()
                )
            else:
                template = self.template
                template.name = self.name_edit.text().strip()
                template.description = self.description_edit.toPlainText().strip()
            
            # 更新基本信息
            template.total_duration = self.total_duration_spin.value()
            
            # 更新片段数据
            template.segments.clear()
            for widget in self.segment_widgets.values():
                data = widget.get_segment_data()
                segment = TemplateSegment(
                    segment_type=data['segment_type'],
                    name=data['name'],
                    duration=data['duration'],
                    position=data['position'],
                    required=data['required'],
                    video_path=data['video_path'] if data['video_path'] else None
                )
                template.segments.append(segment)
            
            # 更新音乐数据
            template.music.clear()
            for widget in self.music_widgets:
                data = widget.get_music_data()
                # 确保音乐数据完整
                music_data = {
                    'name': data.get('name', '背景音乐'),
                    'audio_path': data.get('audio_path', ''),
                    'start_time': 0.0,
                    'duration': template.total_duration,
                    'volume': data.get('volume', 0.8),
                    'fade_in': data.get('fade_in', 2.0),
                    'fade_out': data.get('fade_out', 2.0),
                    'loop': data.get('loop', False)
                }
                music = TemplateMusic(**music_data)
                template.music.append(music)
            
            # 更新水印数据
            template.watermarks.clear()
            for widget in self.watermark_widgets:
                data = widget.get_watermark_data()
                # 确保水印数据完整
                watermark_data = {
                    'name': data.get('name', '水印'),
                    'content': data.get('content', ''),
                    'content_type': data.get('content_type', 'text'),
                    'position': data.get('position', 'bottom-right'),
                    'start_time': data.get('start_time', 0.0),
                    'duration': data.get('duration', 5.0),
                    'opacity': data.get('opacity', 0.8),
                    'font_size': data.get('font_size', 24),
                    'font_color': data.get('font_color', '#FFFFFF'),
                    'background_color': None
                }
                watermark = TemplateWatermark(**watermark_data)
                template.watermarks.append(watermark)
            
            # 更新高级设置
            try:
                resolution_text = self.resolution_combo.currentText()
                template.resolution = tuple(map(int, resolution_text.split('x')))
                template.fps = self.fps_spin.value()
                template.audio_sample_rate = int(self.sample_rate_combo.currentText())
            except Exception as e:
                print(f"⚠️ 高级设置更新失败，使用默认值: {e}")
                template.resolution = (1920, 1080)
                template.fps = 30
                template.audio_sample_rate = 44100
            
            # 保存模版
            print(f"🔄 正在保存模板: {template.name}")
            success = self.template_manager.save_template(template)
            
            if success:
                QMessageBox.information(self, "成功", f"模板 '{template.name}' 保存成功！\n总时长: {template.total_duration:.1f}秒")
                self.template_saved.emit(template.id)
                self.accept()
            else:
                QMessageBox.critical(self, "错误", "模板保存失败！请检查文件权限和存储空间。")
        
        except Exception as e:
            import traceback
            error_msg = f"保存模板时出错：{str(e)}"
            print(f"❌ {error_msg}")
            print(f"详细错误: {traceback.format_exc()}")
            QMessageBox.critical(self, "错误", error_msg)

class TemplateListDialog(QDialog):
    """模板列表对话框"""
    
    template_selected = Signal(str)  # template_id
    
    def __init__(self, template_manager: HairSalonTemplateManager, parent=None):
        super().__init__(parent)
        self.template_manager = template_manager
        self.init_ui()
        self.refresh_templates()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("模板管理")
        self.setModal(True)
        self.resize(600, 400)
        
        layout = QVBoxLayout(self)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        new_btn = QPushButton("新建模板")
        new_btn.clicked.connect(self.new_template)
        toolbar_layout.addWidget(new_btn)
        
        edit_btn = QPushButton("编辑模板")
        edit_btn.clicked.connect(self.edit_template)
        toolbar_layout.addWidget(edit_btn)
        
        delete_btn = QPushButton("删除模板")
        delete_btn.clicked.connect(self.delete_template)
        toolbar_layout.addWidget(delete_btn)
        
        toolbar_layout.addStretch()
        
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh_templates)
        toolbar_layout.addWidget(refresh_btn)
        
        layout.addLayout(toolbar_layout)
        
        # 模板列表
        self.template_table = QTableWidget()
        self.template_table.setColumnCount(5)
        self.template_table.setHorizontalHeaderLabels([
            "名称", "描述", "时长", "创建时间", "更新时间"
        ])
        self.template_table.horizontalHeader().setStretchLastSection(True)
        self.template_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.template_table.doubleClicked.connect(self.on_template_double_clicked)
        
        layout.addWidget(self.template_table)
        
        # 按钮组
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        select_btn = QPushButton("选择模板")
        select_btn.clicked.connect(self.select_template)
        button_layout.addWidget(select_btn)
        
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.reject)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
    
    def refresh_templates(self):
        """刷新模板列表"""
        templates = self.template_manager.list_templates()
        
        self.template_table.setRowCount(len(templates))
        
        for row, template in enumerate(templates):
            self.template_table.setItem(row, 0, QTableWidgetItem(template['name']))
            self.template_table.setItem(row, 1, QTableWidgetItem(template['description']))
            self.template_table.setItem(row, 2, QTableWidgetItem(f"{template['total_duration']:.1f}s"))
            self.template_table.setItem(row, 3, QTableWidgetItem(template['created_at'][:10]))
            self.template_table.setItem(row, 4, QTableWidgetItem(template['updated_at'][:10]))
            
            # 存储模板ID
            self.template_table.item(row, 0).setData(Qt.UserRole, template['id'])
    
    def get_selected_template_id(self) -> Optional[str]:
        """获取选中的模板ID"""
        current_row = self.template_table.currentRow()
        if current_row >= 0:
            item = self.template_table.item(current_row, 0)
            return item.data(Qt.UserRole)
        return None
    
    def new_template(self):
        """新建模板"""
        dialog = HairSalonTemplateDialog(self.template_manager, parent=self)
        dialog.template_saved.connect(self.refresh_templates)
        dialog.exec()
    
    def edit_template(self):
        """编辑模板"""
        template_id = self.get_selected_template_id()
        if not template_id:
            QMessageBox.warning(self, "警告", "请选择要编辑的模板")
            return
        
        template = self.template_manager.load_template(template_id)
        if not template:
            QMessageBox.critical(self, "错误", "无法加载模板")
            return
        
        dialog = HairSalonTemplateDialog(self.template_manager, template, parent=self)
        dialog.template_saved.connect(self.refresh_templates)
        dialog.exec()
    
    def delete_template(self):
        """删除模板"""
        template_id = self.get_selected_template_id()
        if not template_id:
            QMessageBox.warning(self, "警告", "请选择要删除的模板")
            return
        
        reply = QMessageBox.question(
            self, "确认删除",
            "确定要删除选中的模板吗？此操作不可恢复。",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            success = self.template_manager.delete_template(template_id)
            if success:
                self.refresh_templates()
            else:
                QMessageBox.critical(self, "错误", "删除模板失败")
    
    def select_template(self):
        """选择模板"""
        template_id = self.get_selected_template_id()
        if not template_id:
            QMessageBox.warning(self, "警告", "请选择一个模板")
            return
        
        self.template_selected.emit(template_id)
        self.accept()
    
    def on_template_double_clicked(self):
        """双击模板项"""
        self.select_template() 