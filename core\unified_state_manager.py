#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一状态管理器 - 管理所有运行时状态和频繁访问的变量
"""

import threading
import time
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, field
from PySide6.QtCore import QObject, Signal
from enum import Enum

from .unified_constants_manager import constants, RWLock

class AppState(Enum):
    """应用状态"""
    INITIALIZING = "initializing"
    READY = "ready"
    BUSY = "busy"
    ERROR = "error"

class UIState(Enum):
    """UI状态"""
    NORMAL = "normal"
    FULLSCREEN = "fullscreen"
    MINIMIZED = "minimized"
    MAXIMIZED = "maximized"

@dataclass
class WindowState:
    """窗口状态"""
    x: int = 100
    y: int = 100
    width: int = 1920
    height: int = 1080
    is_maximized: bool = False
    is_fullscreen: bool = False
    has_notch: bool = False
    notch_height: int = 0

@dataclass
class ViewportState:
    """视口状态"""
    # 时间轴视口
    timeline_scroll_x: int = 0
    timeline_scroll_y: int = 0
    timeline_zoom_level: int = 6  # 索引
    timeline_pixels_per_second: int = 100
    
    # 预览窗口视口
    preview_zoom: float = 1.0
    preview_offset_x: int = 0
    preview_offset_y: int = 0
    
    # 媒体库视口
    media_library_scroll: int = 0
    media_library_view_mode: str = "grid"  # "grid", "list"

@dataclass
class SelectionState:
    """选择状态"""
    selected_tracks: List[str] = field(default_factory=list)
    selected_media_items: List[str] = field(default_factory=list)
    selection_start_time: float = -1.0
    selection_end_time: float = -1.0
    multi_select_mode: bool = False

@dataclass
class DragState:
    """拖拽状态"""
    is_dragging: bool = False
    drag_type: str = ""  # "media", "playhead", "selection", "track"
    drag_source: str = ""
    drag_target: str = ""
    drag_start_pos: tuple = (0, 0)
    drag_current_pos: tuple = (0, 0)
    drag_data: Any = None

@dataclass
class PerformanceMetrics:
    """性能指标"""
    fps: float = 0.0
    memory_usage: float = 0.0  # MB
    cpu_usage: float = 0.0     # %
    render_time: float = 0.0   # ms
    last_update: float = 0.0

class UnifiedStateManager(QObject):
    """
    统一状态管理器
    - 管理所有运行时状态
    - 提供状态变化通知
    - 线程安全的状态访问
    """
    
    # 状态变化信号
    app_state_changed = Signal(AppState)
    ui_state_changed = Signal(UIState)
    window_state_changed = Signal(WindowState)
    viewport_changed = Signal(ViewportState)
    selection_changed = Signal(SelectionState)
    drag_state_changed = Signal(DragState)
    performance_updated = Signal(PerformanceMetrics)
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        super().__init__()
        self._initialized = True
        
        # 读写锁
        self._state_lock = RWLock()
        
        # 状态数据
        self.app_state = AppState.INITIALIZING
        self.ui_state = UIState.NORMAL
        self.window_state = WindowState()
        self.viewport_state = ViewportState()
        self.selection_state = SelectionState()
        self.drag_state = DragState()
        self.performance_metrics = PerformanceMetrics()
        
        # 频繁访问的缓存变量
        self._cached_values: Dict[str, Any] = {}
        self._cache_timestamps: Dict[str, float] = {}
        self._cache_ttl = 1.0  # 缓存1秒
        
        # 状态监听器
        self._state_listeners: Dict[str, List[Callable]] = {}
        
        print("✅ 统一状态管理器初始化完成")
    
    # ==================== 应用状态管理 ====================
    
    def get_app_state(self) -> AppState:
        """获取应用状态"""
        with self._state_lock.r_locked():
            return self.app_state
    
    def set_app_state(self, state: AppState):
        """设置应用状态"""
        with self._state_lock.w_locked():
            if self.app_state != state:
                old_state = self.app_state
                self.app_state = state
                self.app_state_changed.emit(state)
                self._notify_listeners('app_state', old_state, state)
    
    def is_app_ready(self) -> bool:
        """检查应用是否就绪"""
        return self.get_app_state() == AppState.READY
    
    def is_app_busy(self) -> bool:
        """检查应用是否忙碌"""
        return self.get_app_state() == AppState.BUSY
    
    # ==================== 窗口状态管理 ====================
    
    def get_window_state(self) -> WindowState:
        """获取窗口状态"""
        with self._state_lock.r_locked():
            return self.window_state
    
    def update_window_geometry(self, x: int, y: int, width: int, height: int):
        """更新窗口几何"""
        with self._state_lock.w_locked():
            self.window_state.x = x
            self.window_state.y = y
            self.window_state.width = width
            self.window_state.height = height
            self.window_state_changed.emit(self.window_state)
    
    def set_window_maximized(self, maximized: bool):
        """设置窗口最大化状态"""
        with self._state_lock.w_locked():
            if self.window_state.is_maximized != maximized:
                self.window_state.is_maximized = maximized
                self.window_state_changed.emit(self.window_state)
    
    def set_notch_info(self, has_notch: bool, notch_height: int = 0):
        """设置刘海屏信息"""
        with self._state_lock.w_locked():
            self.window_state.has_notch = has_notch
            self.window_state.notch_height = notch_height
            self.window_state_changed.emit(self.window_state)
    
    # ==================== 视口状态管理 ====================
    
    def get_viewport_state(self) -> ViewportState:
        """获取视口状态"""
        with self._state_lock.r_locked():
            return self.viewport_state
    
    def update_timeline_viewport(self, scroll_x: int = None, scroll_y: int = None, 
                                zoom_level: int = None, pixels_per_second: int = None):
        """更新时间轴视口"""
        with self._state_lock.w_locked():
            changed = False
            
            if scroll_x is not None and self.viewport_state.timeline_scroll_x != scroll_x:
                self.viewport_state.timeline_scroll_x = scroll_x
                changed = True
            
            if scroll_y is not None and self.viewport_state.timeline_scroll_y != scroll_y:
                self.viewport_state.timeline_scroll_y = scroll_y
                changed = True
            
            if zoom_level is not None and self.viewport_state.timeline_zoom_level != zoom_level:
                self.viewport_state.timeline_zoom_level = zoom_level
                changed = True
            
            if pixels_per_second is not None and self.viewport_state.timeline_pixels_per_second != pixels_per_second:
                self.viewport_state.timeline_pixels_per_second = pixels_per_second
                changed = True
            
            if changed:
                self.viewport_changed.emit(self.viewport_state)
    
    def get_timeline_pixels_per_second(self) -> int:
        """获取时间轴像素比例（频繁访问）"""
        cache_key = "timeline_pixels_per_second"
        
        # 检查缓存
        if self._is_cache_valid(cache_key):
            return self._cached_values[cache_key]
        
        # 从状态获取
        with self._state_lock.r_locked():
            value = self.viewport_state.timeline_pixels_per_second
        
        # 更新缓存
        self._update_cache(cache_key, value)
        return value
    
    def update_preview_viewport(self, zoom: float = None, offset_x: int = None, offset_y: int = None):
        """更新预览视口"""
        with self._state_lock.w_locked():
            changed = False
            
            if zoom is not None and abs(self.viewport_state.preview_zoom - zoom) > 0.01:
                self.viewport_state.preview_zoom = zoom
                changed = True
            
            if offset_x is not None and self.viewport_state.preview_offset_x != offset_x:
                self.viewport_state.preview_offset_x = offset_x
                changed = True
            
            if offset_y is not None and self.viewport_state.preview_offset_y != offset_y:
                self.viewport_state.preview_offset_y = offset_y
                changed = True
            
            if changed:
                self.viewport_changed.emit(self.viewport_state)
    
    # ==================== 选择状态管理 ====================
    
    def get_selection_state(self) -> SelectionState:
        """获取选择状态"""
        with self._state_lock.r_locked():
            return self.selection_state
    
    def set_selected_tracks(self, track_ids: List[str]):
        """设置选中的轨道"""
        with self._state_lock.w_locked():
            if self.selection_state.selected_tracks != track_ids:
                self.selection_state.selected_tracks = track_ids.copy()
                self.selection_changed.emit(self.selection_state)
    
    def set_selected_media_items(self, item_ids: List[str]):
        """设置选中的媒体项"""
        with self._state_lock.w_locked():
            if self.selection_state.selected_media_items != item_ids:
                self.selection_state.selected_media_items = item_ids.copy()
                self.selection_changed.emit(self.selection_state)
    
    def clear_selection(self):
        """清空选择"""
        with self._state_lock.w_locked():
            changed = False
            
            if self.selection_state.selected_tracks:
                self.selection_state.selected_tracks.clear()
                changed = True
            
            if self.selection_state.selected_media_items:
                self.selection_state.selected_media_items.clear()
                changed = True
            
            if self.selection_state.selection_start_time >= 0:
                self.selection_state.selection_start_time = -1.0
                self.selection_state.selection_end_time = -1.0
                changed = True
            
            if changed:
                self.selection_changed.emit(self.selection_state)
    
    # ==================== 拖拽状态管理 ====================
    
    def start_drag(self, drag_type: str, source: str, data: Any = None, start_pos: tuple = (0, 0)):
        """开始拖拽"""
        with self._state_lock.w_locked():
            self.drag_state.is_dragging = True
            self.drag_state.drag_type = drag_type
            self.drag_state.drag_source = source
            self.drag_state.drag_start_pos = start_pos
            self.drag_state.drag_current_pos = start_pos
            self.drag_state.drag_data = data
            self.drag_state_changed.emit(self.drag_state)
    
    def update_drag(self, current_pos: tuple, target: str = ""):
        """更新拖拽状态"""
        with self._state_lock.w_locked():
            if self.drag_state.is_dragging:
                self.drag_state.drag_current_pos = current_pos
                self.drag_state.drag_target = target
                self.drag_state_changed.emit(self.drag_state)
    
    def end_drag(self):
        """结束拖拽"""
        with self._state_lock.w_locked():
            if self.drag_state.is_dragging:
                self.drag_state.is_dragging = False
                self.drag_state.drag_type = ""
                self.drag_state.drag_source = ""
                self.drag_state.drag_target = ""
                self.drag_state.drag_data = None
                self.drag_state_changed.emit(self.drag_state)
    
    def is_dragging(self) -> bool:
        """检查是否在拖拽"""
        with self._state_lock.r_locked():
            return self.drag_state.is_dragging
    
    # ==================== 性能监控 ====================
    
    def update_performance_metrics(self, fps: float = None, memory_mb: float = None, 
                                 cpu_percent: float = None, render_time_ms: float = None):
        """更新性能指标"""
        with self._state_lock.w_locked():
            current_time = time.time()
            
            if fps is not None:
                self.performance_metrics.fps = fps
            if memory_mb is not None:
                self.performance_metrics.memory_usage = memory_mb
            if cpu_percent is not None:
                self.performance_metrics.cpu_usage = cpu_percent
            if render_time_ms is not None:
                self.performance_metrics.render_time = render_time_ms
            
            self.performance_metrics.last_update = current_time
            self.performance_updated.emit(self.performance_metrics)
    
    def get_performance_metrics(self) -> PerformanceMetrics:
        """获取性能指标"""
        with self._state_lock.r_locked():
            return self.performance_metrics
    
    # ==================== 缓存管理 ====================
    
    def _is_cache_valid(self, key: str) -> bool:
        """检查缓存是否有效"""
        if key not in self._cached_values:
            return False
        
        timestamp = self._cache_timestamps.get(key, 0)
        return (time.time() - timestamp) < self._cache_ttl
    
    def _update_cache(self, key: str, value: Any):
        """更新缓存"""
        self._cached_values[key] = value
        self._cache_timestamps[key] = time.time()
    
    def clear_cache(self):
        """清空缓存"""
        self._cached_values.clear()
        self._cache_timestamps.clear()
    
    # ==================== 状态监听器 ====================
    
    def add_state_listener(self, state_name: str, callback: Callable):
        """添加状态监听器"""
        if state_name not in self._state_listeners:
            self._state_listeners[state_name] = []
        self._state_listeners[state_name].append(callback)
    
    def remove_state_listener(self, state_name: str, callback: Callable):
        """移除状态监听器"""
        if state_name in self._state_listeners:
            try:
                self._state_listeners[state_name].remove(callback)
            except ValueError:
                pass
    
    def _notify_listeners(self, state_name: str, old_value: Any, new_value: Any):
        """通知状态监听器"""
        if state_name in self._state_listeners:
            for callback in self._state_listeners[state_name]:
                try:
                    callback(old_value, new_value)
                except Exception as e:
                    print(f"❌ 状态监听器回调失败: {e}")
    
    # ==================== 便捷方法 ====================
    
    def get_current_zoom_pixels_per_second(self) -> int:
        """获取当前缩放的像素比例（高频访问）"""
        cache_key = "current_zoom_pps"
        
        if self._is_cache_valid(cache_key):
            return self._cached_values[cache_key]
        
        with self._state_lock.r_locked():
            zoom_levels = constants.get_zoom_levels()
            zoom_index = self.viewport_state.timeline_zoom_level
            
            if 0 <= zoom_index < len(zoom_levels):
                pps = zoom_levels[zoom_index]
            else:
                pps = constants.get_default_pixels_per_second()
        
        self._update_cache(cache_key, pps)
        return pps
    
    def get_visible_timeline_range(self, viewport_width: int) -> tuple:
        """获取可见的时间轴范围"""
        cache_key = f"visible_range_{viewport_width}_{self.viewport_state.timeline_scroll_x}"
        
        if self._is_cache_valid(cache_key):
            return self._cached_values[cache_key]
        
        pps = self.get_current_zoom_pixels_per_second()
        scroll_x = self.viewport_state.timeline_scroll_x
        
        start_time = scroll_x / pps
        end_time = (scroll_x + viewport_width) / pps
        
        result = (start_time, end_time)
        self._update_cache(cache_key, result)
        return result

# 全局实例
state_manager = UnifiedStateManager()
