#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一视频渲染器使用示例
展示如何用统一架构处理跳转、播放、效果调整
"""

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget
from PySide6.QtWidgets import QSlider, QLabel, QPushButton, QCheckBox
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QPixmap, QImage
import sys
import cv2
import numpy as np

# 导入统一渲染器
from core.unified_video_renderer import UnifiedVideoRenderer, VideoEffect, RenderRequest

class VideoEffectPanel(QWidget):
    """视频效果控制面板"""
    
    def __init__(self, renderer: UnifiedVideoRenderer, video_path: str):
        super().__init__()
        self.renderer = renderer
        self.video_path = video_path
        self.setup_ui()
        
        # 连接渲染器信号
        self.renderer.frame_ready.connect(self.on_frame_ready)
    
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # 视频显示区域
        self.video_label = QLabel("视频预览")
        self.video_label.setMinimumSize(640, 360)
        self.video_label.setStyleSheet("border: 1px solid gray; background: black;")
        self.video_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.video_label)
        
        # 播放控制
        control_layout = QHBoxLayout()
        
        self.play_btn = QPushButton("播放")
        self.play_btn.clicked.connect(self.toggle_playback)
        control_layout.addWidget(self.play_btn)
        
        self.stop_btn = QPushButton("停止")
        self.stop_btn.clicked.connect(self.stop_playback)
        control_layout.addWidget(self.stop_btn)
        
        # 位置滑块
        self.position_slider = QSlider(Qt.Orientation.Horizontal)
        self.position_slider.setRange(0, 1000)  # 0-100%
        self.position_slider.valueChanged.connect(self.on_position_changed)
        control_layout.addWidget(self.position_slider)
        
        layout.addLayout(control_layout)
        
        # 效果控制
        effects_layout = QVBoxLayout()
        
        # 亮度控制
        brightness_layout = QHBoxLayout()
        brightness_layout.addWidget(QLabel("亮度:"))
        self.brightness_slider = QSlider(Qt.Orientation.Horizontal)
        self.brightness_slider.setRange(-100, 100)
        self.brightness_slider.setValue(0)
        self.brightness_slider.valueChanged.connect(self.on_effect_changed)
        brightness_layout.addWidget(self.brightness_slider)
        self.brightness_label = QLabel("0")
        brightness_layout.addWidget(self.brightness_label)
        effects_layout.addLayout(brightness_layout)
        
        # 对比度控制
        contrast_layout = QHBoxLayout()
        contrast_layout.addWidget(QLabel("对比度:"))
        self.contrast_slider = QSlider(Qt.Orientation.Horizontal)
        self.contrast_slider.setRange(50, 200)  # 0.5x - 2.0x
        self.contrast_slider.setValue(100)  # 1.0x
        self.contrast_slider.valueChanged.connect(self.on_effect_changed)
        contrast_layout.addWidget(self.contrast_slider)
        self.contrast_label = QLabel("1.0")
        contrast_layout.addWidget(self.contrast_label)
        effects_layout.addLayout(contrast_layout)
        
        # 饱和度控制
        saturation_layout = QHBoxLayout()
        saturation_layout.addWidget(QLabel("饱和度:"))
        self.saturation_slider = QSlider(Qt.Orientation.Horizontal)
        self.saturation_slider.setRange(0, 200)  # 0x - 2.0x
        self.saturation_slider.setValue(100)  # 1.0x
        self.saturation_slider.valueChanged.connect(self.on_effect_changed)
        saturation_layout.addWidget(self.saturation_slider)
        self.saturation_label = QLabel("1.0")
        saturation_layout.addWidget(self.saturation_label)
        effects_layout.addLayout(saturation_layout)
        
        layout.addLayout(effects_layout)
        
        # 质量控制
        quality_layout = QHBoxLayout()
        quality_layout.addWidget(QLabel("预览质量:"))
        
        self.quality_original = QCheckBox("原画")
        self.quality_original.setChecked(True)
        self.quality_original.toggled.connect(self.on_quality_changed)
        quality_layout.addWidget(self.quality_original)
        
        self.quality_high = QCheckBox("高清")
        self.quality_high.toggled.connect(self.on_quality_changed)
        quality_layout.addWidget(self.quality_high)
        
        self.quality_smooth = QCheckBox("流畅")
        self.quality_smooth.toggled.connect(self.on_quality_changed)
        quality_layout.addWidget(self.quality_smooth)
        
        layout.addLayout(quality_layout)
        
        self.setLayout(layout)
        
        # 状态
        self.is_playing = False
        self.video_duration = 10.0  # 假设10秒视频
    
    def get_current_effects(self) -> list:
        """获取当前效果设置"""
        effects = []
        
        # 亮度
        brightness_value = self.brightness_slider.value()
        effects.append(VideoEffect('brightness', brightness_value, True))
        
        # 对比度
        contrast_value = self.contrast_slider.value() / 100.0
        effects.append(VideoEffect('contrast', contrast_value, True))
        
        # 饱和度
        saturation_value = self.saturation_slider.value() / 100.0
        effects.append(VideoEffect('saturation', saturation_value, True))
        
        return effects
    
    def get_current_quality(self) -> str:
        """获取当前质量设置"""
        if self.quality_smooth.isChecked():
            return 'smooth'
        elif self.quality_high.isChecked():
            return 'high'
        else:
            return 'original'
    
    def toggle_playback(self):
        """切换播放状态"""
        if self.is_playing:
            # 当前在播放，停止播放
            self.renderer.stop_playback(self.video_path)
            self.play_btn.setText("播放")
            self.is_playing = False
        else:
            # 当前停止，开始播放
            effects = self.get_current_effects()
            quality = self.get_current_quality()
            position = (self.position_slider.value() / 1000.0) * self.video_duration
            
            success = self.renderer.start_playback(
                self.video_path, effects, position, quality
            )
            
            if success:
                self.play_btn.setText("暂停")
                self.is_playing = True
    
    def stop_playback(self):
        """停止播放"""
        self.renderer.stop_playback(self.video_path)
        self.play_btn.setText("播放")
        self.is_playing = False
    
    def on_position_changed(self, value):
        """位置滑块变化 - 跳转预览"""
        if not self.is_playing:  # 只在非播放状态下响应拖拽
            position = (value / 1000.0) * self.video_duration
            effects = self.get_current_effects()
            quality = self.get_current_quality()
            
            # 使用统一渲染器跳转
            self.renderer.seek_to_position(self.video_path, position, effects, quality)
    
    def on_effect_changed(self):
        """效果参数变化 - 实时更新"""
        # 更新标签显示
        self.brightness_label.setText(str(self.brightness_slider.value()))
        self.contrast_label.setText(f"{self.contrast_slider.value() / 100.0:.1f}")
        self.saturation_label.setText(f"{self.saturation_slider.value() / 100.0:.1f}")
        
        # 实时更新效果
        effects = self.get_current_effects()
        quality = self.get_current_quality()
        self.renderer.update_effects(self.video_path, effects, quality)
    
    def on_quality_changed(self):
        """质量设置变化"""
        # 确保只有一个质量选项被选中
        sender = self.sender()
        if sender.isChecked():
            if sender == self.quality_original:
                self.quality_high.setChecked(False)
                self.quality_smooth.setChecked(False)
            elif sender == self.quality_high:
                self.quality_original.setChecked(False)
                self.quality_smooth.setChecked(False)
            elif sender == self.quality_smooth:
                self.quality_original.setChecked(False)
                self.quality_high.setChecked(False)
        
        # 重新渲染当前帧
        self.on_effect_changed()
    
    def on_frame_ready(self, video_path: str, frame: np.ndarray):
        """处理渲染完成的帧"""
        if video_path != self.video_path:
            return
        
        try:
            # 转换为Qt显示格式
            height, width, channel = frame.shape
            bytes_per_line = 3 * width
            
            # BGR转RGB
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # 创建QImage
            q_image = QImage(rgb_frame.data, width, height, bytes_per_line, QImage.Format.Format_RGB888)
            
            # 转换为QPixmap并显示
            pixmap = QPixmap.fromImage(q_image)
            
            # 缩放以适应显示区域
            scaled_pixmap = pixmap.scaled(
                self.video_label.size(), 
                Qt.AspectRatioMode.KeepAspectRatio, 
                Qt.TransformationMode.SmoothTransformation
            )
            
            self.video_label.setPixmap(scaled_pixmap)
            
        except Exception as e:
            print(f"❌ 显示帧失败: {e}")

class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("统一视频渲染器演示")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建统一渲染器
        self.renderer = UnifiedVideoRenderer()
        
        # 创建效果面板
        video_path = "test_video.mp4"  # 替换为实际视频路径
        self.effect_panel = VideoEffectPanel(self.renderer, video_path)
        
        self.setCentralWidget(self.effect_panel)
    
    def closeEvent(self, event):
        """窗口关闭时清理资源"""
        self.renderer.cleanup()
        event.accept()

def main():
    app = QApplication(sys.argv)
    
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
