"""
自定义标题栏组件
包含窗口控制按钮、应用图标、标题和菜单按钮区域
"""

from PySide6.QtWidgets import (QWidget, QHBoxLayout, QVBoxLayout, QPushButton, 
                               QLabel, QMenu, QMenuBar, QSizePolicy, QSpacerItem)
from PySide6.QtCore import Qt, Signal, QPoint
from PySide6.QtGui import QAction, QKeySequence, QFont, QIcon, QPixmap, QPainter
from PySide6.QtWidgets import QApplication


class CustomTitleBar(QWidget):
    """自定义标题栏"""
    
    # 窗口控制信号
    minimize_clicked = Signal()
    maximize_clicked = Signal()
    close_clicked = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.is_maximized = False
        self.drag_position = QPoint()
        
        self.setFixedHeight(40)  # 标题栏高度
        self.setStyleSheet("""
            CustomTitleBar {
                background-color: #0A0A0A;
                border-bottom: 1px solid #2A2A2A;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
        """)
        
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(8, 0, 8, 0)
        layout.setSpacing(8)
        
        # 应用图标和标题
        self.create_title_section(layout)
        
        # 菜单区域
        self.create_menu_section(layout)
        
        # 弹性空间
        spacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        layout.addItem(spacer)
        
        # 窗口控制按钮
        self.create_window_controls(layout)
        
    def create_title_section(self, layout):
        """创建标题区域 - 适配刘海屏"""
        # 检测是否为Mac刘海屏，添加左侧安全边距
        import platform
        left_margin = 80 if platform.system() == "Darwin" else 8  # Mac上增加左边距避开刘海

        # 左侧安全区域间距（用于刘海屏）
        if left_margin > 8:
            left_spacer = QSpacerItem(left_margin, 20, QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Minimum)
            layout.addItem(left_spacer)

        # 应用图标
        self.icon_label = QLabel()
        self.icon_label.setFixedSize(24, 24)
        self.icon_label.setStyleSheet("""
            QLabel {
                background-color: #00C896;
                border-radius: 12px;
                color: white;
                font-weight: bold;
                font-size: 12px;
            }
        """)
        self.icon_label.setText("S")
        self.icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.icon_label)

        # 应用标题
        self.title_label = QLabel("SWANKSALON")
        self.title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 14px;
                font-weight: bold;
                background-color: transparent;
            }
        """)
        layout.addWidget(self.title_label)
        
    def create_menu_section(self, layout):
        """创建菜单区域"""
        # SWANKSALON 专业菜单
        self.swanksalon_btn = self.create_menu_button("💎 SWANKSALON")
        self.swanksalon_menu = QMenu(self)
        self.setup_swanksalon_menu()
        self.swanksalon_btn.setMenu(self.swanksalon_menu)
        layout.addWidget(self.swanksalon_btn)
        
        # 🔧 修改：删除理发店模板菜单，功能移动到增强功能菜单
        
        # 文件菜单
        self.file_btn = self.create_menu_button("文件")
        self.file_menu = QMenu(self)
        self.setup_file_menu()
        self.file_btn.setMenu(self.file_menu)
        layout.addWidget(self.file_btn)
        
        # 🔧 删除：编辑菜单已删除
        
        # 增强功能菜单
        self.enhanced_btn = self.create_menu_button("🚀 增强功能")
        self.enhanced_menu = QMenu(self)
        self.setup_enhanced_menu()
        self.enhanced_btn.setMenu(self.enhanced_menu)
        layout.addWidget(self.enhanced_btn)
        
        # 帮助菜单
        self.help_btn = self.create_menu_button("帮助")
        self.help_menu = QMenu(self)
        self.setup_help_menu()
        self.help_btn.setMenu(self.help_menu)
        layout.addWidget(self.help_btn)
        
    def create_menu_button(self, text):
        """创建菜单按钮"""
        btn = QPushButton(text)
        btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #FFFFFF;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #FFFFFF;
                color: #000000;
            }
            QPushButton:pressed {
                background-color: #CCCCCC;
                color: #000000;
            }
            QPushButton::menu-indicator {
                image: none;
                width: 0px;
                height: 0px;
            }
        """)
        return btn
        
    def setup_swanksalon_menu(self):
        """设置SWANKSALON菜单"""
        self.swanksalon_menu.setStyleSheet("""
            QMenu {
                background-color: #1A1A1A;
                color: #FFFFFF;
                border: 1px solid #333333;
                border-radius: 6px;
            }
            QMenu::item {
                padding: 8px 16px;
                border-radius: 4px;
            }
            QMenu::item:selected {
                background-color: #FFFFFF;
                color: #000000;
            }
        """)
        
        swanksalon_action = QAction("SWANKSALON 专业剪辑", self)
        swanksalon_action.setShortcut(QKeySequence("Ctrl+S"))
        swanksalon_action.triggered.connect(self.show_swanksalon_dialog)
        self.swanksalon_menu.addAction(swanksalon_action)
        
        self.swanksalon_menu.addSeparator()
        
    # 🔧 修改：删除理发店模板菜单设置方法

    def setup_file_menu(self):
        """设置文件菜单"""
        self.file_menu.setStyleSheet("""
            QMenu {
                background-color: #1A1A1A;
                color: #FFFFFF;
                border: 1px solid #333333;
                border-radius: 6px;
            }
            QMenu::item {
                padding: 8px 16px;
                border-radius: 4px;
            }
            QMenu::item:selected {
                background-color: #FFFFFF;
                color: #000000;
            }
        """)

        # 项目管理
        new_project_action = QAction("新建项目", self)
        new_project_action.setShortcut(QKeySequence("Ctrl+N"))
        new_project_action.triggered.connect(self.new_project)
        self.file_menu.addAction(new_project_action)

        load_project_action = QAction("打开项目", self)
        load_project_action.setShortcut(QKeySequence("Ctrl+O"))
        load_project_action.triggered.connect(self.load_project)
        self.file_menu.addAction(load_project_action)

        save_project_action = QAction("保存项目", self)
        save_project_action.setShortcut(QKeySequence("Ctrl+S"))
        save_project_action.triggered.connect(self.save_project)
        self.file_menu.addAction(save_project_action)

        self.file_menu.addSeparator()

        # 导入媒体
        import_action = QAction("导入媒体", self)
        import_action.setShortcut(QKeySequence("Ctrl+I"))
        import_action.triggered.connect(self.import_video)
        self.file_menu.addAction(import_action)

        self.file_menu.addSeparator()

        # 导出
        export_action = QAction("导出视频", self)
        export_action.setShortcut(QKeySequence("Ctrl+E"))
        export_action.triggered.connect(self.export_video)
        self.file_menu.addAction(export_action)

        # 🔧 修改：去掉快速预览结果菜单项
        # preview_action = QAction("快速预览结果", self)
        # preview_action.setShortcut(QKeySequence("Ctrl+P"))
        # preview_action.triggered.connect(self.quick_preview_results)
        # self.file_menu.addAction(preview_action)

    # 🔧 删除：编辑菜单设置方法已删除

    def setup_enhanced_menu(self):
        """设置增强功能菜单"""
        self.enhanced_menu.setStyleSheet("""
            QMenu {
                background-color: #1A1A1A;
                color: #FFFFFF;
                border: 1px solid #333333;
                border-radius: 6px;
            }
            QMenu::item {
                padding: 8px 16px;
                border-radius: 4px;
            }
            QMenu::item:selected {
                background-color: #FFFFFF;
                color: #000000;
            }
        """)

        enhanced_features_action = QAction("增强功能面板", self)
        enhanced_features_action.setShortcut(QKeySequence("Ctrl+Shift+E"))
        enhanced_features_action.triggered.connect(self.show_enhanced_features)
        self.enhanced_menu.addAction(enhanced_features_action)

        self.enhanced_menu.addSeparator()

        auto_sync_action = QAction("🎵 智能自动踩点", self)
        auto_sync_action.setShortcut(QKeySequence("Ctrl+Shift+S"))
        auto_sync_action.triggered.connect(self.quick_auto_sync)
        self.enhanced_menu.addAction(auto_sync_action)

        ocr_action = QAction("📝 快速文字识别", self)
        ocr_action.setShortcut(QKeySequence("Ctrl+Shift+O"))
        ocr_action.triggered.connect(self.quick_ocr_extract)
        self.enhanced_menu.addAction(ocr_action)

        audio_analysis_action = QAction("🎤 音频分析", self)
        audio_analysis_action.setShortcut(QKeySequence("Ctrl+Shift+A"))
        audio_analysis_action.triggered.connect(self.show_audio_analysis)
        self.enhanced_menu.addAction(audio_analysis_action)

        self.enhanced_menu.addSeparator()

        # 🔧 新增：从理发店模板菜单移动过来的功能
        batch_process_action = QAction("📦 批量处理", self)
        batch_process_action.setShortcut(QKeySequence("Ctrl+B"))
        batch_process_action.triggered.connect(self.show_batch_processor)
        self.enhanced_menu.addAction(batch_process_action)

        smart_batch_action = QAction("🚀 智能批量处理", self)
        smart_batch_action.setShortcut(QKeySequence("Ctrl+Shift+B"))
        smart_batch_action.triggered.connect(self.show_smart_batch_dialog)
        self.enhanced_menu.addAction(smart_batch_action)

        highlight_action = QAction("✨ 自动识别高亮片段", self)
        highlight_action.setShortcut(QKeySequence("Ctrl+H"))
        highlight_action.triggered.connect(self.auto_detect_highlights)
        self.enhanced_menu.addAction(highlight_action)

        self.enhanced_menu.addSeparator()

        color_action = QAction("🎨 色彩校正", self)
        color_action.setShortcut(QKeySequence("Ctrl+Shift+C"))
        color_action.triggered.connect(self.show_color_correction_panel)
        self.enhanced_menu.addAction(color_action)

        # 🔧 修改：去掉音频编辑器菜单项，功能已整合到右侧面板
        # audio_editor_action = QAction("🎵 音频编辑器", self)
        # audio_editor_action.setShortcut(QKeySequence("Ctrl+Shift+U"))
        # audio_editor_action.triggered.connect(self.show_audio_editor)
        # self.enhanced_menu.addAction(audio_editor_action)

    def setup_help_menu(self):
        """设置帮助菜单"""
        self.help_menu.setStyleSheet("""
            QMenu {
                background-color: #1A1A1A;
                color: #FFFFFF;
                border: 1px solid #333333;
                border-radius: 6px;
            }
            QMenu::item {
                padding: 8px 16px;
                border-radius: 4px;
            }
            QMenu::item:selected {
                background-color: #FFFFFF;
                color: #000000;
            }
        """)

        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about)
        self.help_menu.addAction(about_action)

        help_action = QAction("帮助文档", self)
        help_action.setShortcut(QKeySequence("F1"))
        help_action.triggered.connect(self.show_help)
        self.help_menu.addAction(help_action)

    def create_window_controls(self, layout):
        """创建窗口控制按钮 - 适配刘海屏"""
        # 最小化按钮
        self.minimize_btn = QPushButton("−")
        self.minimize_btn.setFixedSize(32, 32)
        self.minimize_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #FFFFFF;
                border: none;
                border-radius: 4px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #333333;
            }
            QPushButton:pressed {
                background-color: #555555;
            }
        """)
        self.minimize_btn.clicked.connect(self.minimize_clicked.emit)
        layout.addWidget(self.minimize_btn)

        # 最大化/还原按钮
        self.maximize_btn = QPushButton("□")
        self.maximize_btn.setFixedSize(32, 32)
        self.maximize_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #FFFFFF;
                border: none;
                border-radius: 4px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #333333;
            }
            QPushButton:pressed {
                background-color: #555555;
            }
        """)
        self.maximize_btn.clicked.connect(self.maximize_clicked.emit)
        layout.addWidget(self.maximize_btn)

        # 关闭按钮
        self.close_btn = QPushButton("×")
        self.close_btn.setFixedSize(32, 32)
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #FFFFFF;
                border: none;
                border-radius: 4px;
                font-size: 18px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #E81123;
                color: #FFFFFF;
            }
            QPushButton:pressed {
                background-color: #C50E1F;
                color: #FFFFFF;
            }
        """)
        self.close_btn.clicked.connect(self.close_clicked.emit)
        layout.addWidget(self.close_btn)

        # 检测是否为Mac刘海屏，添加右侧安全边距
        import platform
        if platform.system() == "Darwin":
            right_margin = 20  # Mac上增加右边距，避免按钮太靠近边缘
            right_spacer = QSpacerItem(right_margin, 20, QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Minimum)
            layout.addItem(right_spacer)

    def update_maximize_button(self, is_maximized):
        """更新最大化按钮状态"""
        self.is_maximized = is_maximized
        if is_maximized:
            self.maximize_btn.setText("❐")
        else:
            self.maximize_btn.setText("□")

    # 鼠标事件处理 - 实现窗口拖拽
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.parent_window.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if event.buttons() == Qt.MouseButton.LeftButton and self.drag_position:
            self.parent_window.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()

    def mouseDoubleClickEvent(self, event):
        """鼠标双击事件 - 最大化/还原窗口"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.maximize_clicked.emit()
            event.accept()

    # 菜单功能方法 - 这些方法将委托给主窗口
    def show_swanksalon_dialog(self):
        """显示SWANKSALON对话框"""
        if self.parent_window and hasattr(self.parent_window, 'show_swanksalon_dialog'):
            self.parent_window.show_swanksalon_dialog()

    # 🔧 修改：删除模板管理和新建模板方法

    def show_batch_processor(self):
        """显示批量处理器"""
        if self.parent_window and hasattr(self.parent_window, 'show_batch_processor'):
            self.parent_window.show_batch_processor()

    def show_smart_batch_dialog(self):
        """显示智能批量处理对话框"""
        if self.parent_window and hasattr(self.parent_window, 'show_smart_batch_dialog'):
            self.parent_window.show_smart_batch_dialog()

    def auto_detect_highlights(self):
        """自动识别高亮片段"""
        if self.parent_window and hasattr(self.parent_window, 'auto_detect_highlights'):
            self.parent_window.auto_detect_highlights()

    def new_project(self):
        """新建项目"""
        if self.parent_window and hasattr(self.parent_window, 'new_project'):
            self.parent_window.new_project()

    def load_project(self):
        """打开项目"""
        if self.parent_window and hasattr(self.parent_window, 'load_project'):
            self.parent_window.load_project()

    def save_project(self):
        """保存项目"""
        if self.parent_window and hasattr(self.parent_window, 'save_project'):
            self.parent_window.save_project()

    def import_video(self):
        """导入媒体"""
        if self.parent_window and hasattr(self.parent_window, 'import_video'):
            self.parent_window.import_video()

    def export_video(self):
        """导出视频"""
        if self.parent_window and hasattr(self.parent_window, 'export_video'):
            self.parent_window.export_video()

    def quick_preview_results(self):
        """快速预览结果"""
        if self.parent_window and hasattr(self.parent_window, 'quick_preview_results'):
            self.parent_window.quick_preview_results()

    # 🔧 删除：编辑相关方法已删除

    def show_enhanced_features(self):
        """显示增强功能面板"""
        if self.parent_window and hasattr(self.parent_window, 'show_enhanced_features'):
            self.parent_window.show_enhanced_features()

    def quick_auto_sync(self):
        """快速自动踩点"""
        if self.parent_window and hasattr(self.parent_window, 'quick_auto_sync'):
            self.parent_window.quick_auto_sync()

    def quick_ocr_extract(self):
        """快速文字识别"""
        if self.parent_window and hasattr(self.parent_window, 'quick_ocr_extract'):
            self.parent_window.quick_ocr_extract()

    def show_audio_analysis(self):
        """显示音频分析"""
        if self.parent_window and hasattr(self.parent_window, 'show_audio_analysis'):
            self.parent_window.show_audio_analysis()

    def show_color_correction_panel(self):
        """显示色彩校正面板"""
        if self.parent_window and hasattr(self.parent_window, 'show_color_correction_panel'):
            self.parent_window.show_color_correction_panel()

    def show_audio_editor(self):
        """显示音频编辑器"""
        if self.parent_window and hasattr(self.parent_window, 'show_audio_editor'):
            self.parent_window.show_audio_editor()

    def show_about(self):
        """显示关于对话框"""
        if self.parent_window and hasattr(self.parent_window, 'show_about'):
            self.parent_window.show_about()

    def show_help(self):
        """显示帮助文档"""
        if self.parent_window and hasattr(self.parent_window, 'show_help'):
            self.parent_window.show_help()
        else:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.information(self.parent_window, "帮助", "帮助文档功能开发中...")
