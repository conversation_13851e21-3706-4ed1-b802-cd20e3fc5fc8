#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一架构演示 - 展示数据驱动的视频编辑系统
"""

import sys
from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                               QWidget, QSlider, QLabel, QPushButton, QListWidget,
                               QSpinBox, QDoubleSpinBox, QGroupBox)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QPixmap, QImage
import numpy as np

# 导入统一架构组件
from core.unified_project_manager import (UnifiedProjectManager, PlaybackState, 
                                         TrackType, MediaEffect)
from core.unified_playback_controller import (UnifiedPlaybackController, 
                                            PlaybackTrigger)

class ProjectControlPanel(QWidget):
    """项目控制面板"""
    
    def __init__(self, project_manager: UnifiedProjectManager, 
                 playback_controller: UnifiedPlaybackController):
        super().__init__()
        self.project_manager = project_manager
        self.playback_controller = playback_controller
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # 项目信息显示
        info_group = QGroupBox("项目信息")
        info_layout = QVBoxLayout()
        
        self.track_count_label = QLabel("轨道数量: 0")
        self.media_count_label = QLabel("媒体项数量: 0")
        self.duration_label = QLabel("项目时长: 0.00s")
        self.playhead_label = QLabel("播放头位置: 0.00s")
        self.state_label = QLabel("播放状态: 停止")
        
        info_layout.addWidget(self.track_count_label)
        info_layout.addWidget(self.media_count_label)
        info_layout.addWidget(self.duration_label)
        info_layout.addWidget(self.playhead_label)
        info_layout.addWidget(self.state_label)
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # 轨道管理
        track_group = QGroupBox("轨道管理")
        track_layout = QVBoxLayout()
        
        track_buttons = QHBoxLayout()
        self.add_video_track_btn = QPushButton("添加视频轨道")
        self.add_audio_track_btn = QPushButton("添加音频轨道")
        self.remove_track_btn = QPushButton("删除选中轨道")
        
        track_buttons.addWidget(self.add_video_track_btn)
        track_buttons.addWidget(self.add_audio_track_btn)
        track_buttons.addWidget(self.remove_track_btn)
        track_layout.addLayout(track_buttons)
        
        self.track_list = QListWidget()
        track_layout.addWidget(self.track_list)
        track_group.setLayout(track_layout)
        layout.addWidget(track_group)
        
        # 播放控制
        playback_group = QGroupBox("播放控制")
        playback_layout = QVBoxLayout()
        
        # 播放按钮
        playback_buttons = QHBoxLayout()
        self.play_btn = QPushButton("播放")
        self.pause_btn = QPushButton("暂停")
        self.stop_btn = QPushButton("停止")
        
        playback_buttons.addWidget(self.play_btn)
        playback_buttons.addWidget(self.pause_btn)
        playback_buttons.addWidget(self.stop_btn)
        playback_layout.addLayout(playback_buttons)
        
        # 时间轴控制
        timeline_layout = QHBoxLayout()
        timeline_layout.addWidget(QLabel("时间轴:"))
        
        self.timeline_slider = QSlider(Qt.Orientation.Horizontal)
        self.timeline_slider.setRange(0, 1000)  # 0-100%
        timeline_layout.addWidget(self.timeline_slider)
        
        self.position_spinbox = QDoubleSpinBox()
        self.position_spinbox.setRange(0.0, 999.0)
        self.position_spinbox.setSuffix("s")
        self.position_spinbox.setDecimals(2)
        timeline_layout.addWidget(self.position_spinbox)
        
        playback_layout.addLayout(timeline_layout)
        
        # 播放速度
        speed_layout = QHBoxLayout()
        speed_layout.addWidget(QLabel("播放速度:"))
        
        self.speed_spinbox = QDoubleSpinBox()
        self.speed_spinbox.setRange(0.1, 4.0)
        self.speed_spinbox.setValue(1.0)
        self.speed_spinbox.setSingleStep(0.1)
        self.speed_spinbox.setSuffix("x")
        speed_layout.addWidget(self.speed_spinbox)
        
        playback_layout.addLayout(speed_layout)
        playback_group.setLayout(playback_layout)
        layout.addWidget(playback_group)
        
        # 媒体项管理
        media_group = QGroupBox("媒体项管理")
        media_layout = QVBoxLayout()
        
        media_buttons = QHBoxLayout()
        self.add_media_btn = QPushButton("添加测试媒体")
        self.remove_media_btn = QPushButton("删除选中媒体")
        
        media_buttons.addWidget(self.add_media_btn)
        media_buttons.addWidget(self.remove_media_btn)
        media_layout.addLayout(media_buttons)
        
        # 媒体项参数
        media_params = QHBoxLayout()
        media_params.addWidget(QLabel("开始位置:"))
        self.media_start_spinbox = QDoubleSpinBox()
        self.media_start_spinbox.setRange(0.0, 999.0)
        self.media_start_spinbox.setSuffix("s")
        media_params.addWidget(self.media_start_spinbox)
        
        media_params.addWidget(QLabel("时长:"))
        self.media_duration_spinbox = QDoubleSpinBox()
        self.media_duration_spinbox.setRange(0.1, 999.0)
        self.media_duration_spinbox.setValue(5.0)
        self.media_duration_spinbox.setSuffix("s")
        media_params.addWidget(self.media_duration_spinbox)
        
        media_layout.addLayout(media_params)
        media_group.setLayout(media_layout)
        layout.addWidget(media_group)
        
        # 效果控制
        effects_group = QGroupBox("效果控制")
        effects_layout = QVBoxLayout()
        
        # 亮度
        brightness_layout = QHBoxLayout()
        brightness_layout.addWidget(QLabel("亮度:"))
        self.brightness_slider = QSlider(Qt.Orientation.Horizontal)
        self.brightness_slider.setRange(-100, 100)
        self.brightness_slider.setValue(0)
        brightness_layout.addWidget(self.brightness_slider)
        self.brightness_value_label = QLabel("0")
        brightness_layout.addWidget(self.brightness_value_label)
        effects_layout.addLayout(brightness_layout)
        
        # 对比度
        contrast_layout = QHBoxLayout()
        contrast_layout.addWidget(QLabel("对比度:"))
        self.contrast_slider = QSlider(Qt.Orientation.Horizontal)
        self.contrast_slider.setRange(50, 200)
        self.contrast_slider.setValue(100)
        contrast_layout.addWidget(self.contrast_slider)
        self.contrast_value_label = QLabel("1.0")
        contrast_layout.addWidget(self.contrast_value_label)
        effects_layout.addLayout(contrast_layout)
        
        effects_group.setLayout(effects_layout)
        layout.addWidget(effects_group)
        
        # 初始化按钮
        init_layout = QHBoxLayout()
        self.init_project_btn = QPushButton("初始化默认项目")
        self.reset_btn = QPushButton("重置所有参数")
        
        init_layout.addWidget(self.init_project_btn)
        init_layout.addWidget(self.reset_btn)
        layout.addLayout(init_layout)
        
        self.setLayout(layout)
        
        # 连接按钮事件
        self.add_video_track_btn.clicked.connect(self.add_video_track)
        self.add_audio_track_btn.clicked.connect(self.add_audio_track)
        self.add_media_btn.clicked.connect(self.add_test_media)
        
        self.play_btn.clicked.connect(self.play)
        self.pause_btn.clicked.connect(self.pause)
        self.stop_btn.clicked.connect(self.stop)
        
        self.timeline_slider.valueChanged.connect(self.on_timeline_changed)
        self.position_spinbox.valueChanged.connect(self.on_position_spinbox_changed)
        self.speed_spinbox.valueChanged.connect(self.on_speed_changed)
        
        self.brightness_slider.valueChanged.connect(self.on_brightness_changed)
        self.contrast_slider.valueChanged.connect(self.on_contrast_changed)
        
        self.init_project_btn.clicked.connect(self.init_default_project)
        self.reset_btn.clicked.connect(self.reset_all_parameters)
    
    def connect_signals(self):
        """连接项目管理器信号"""
        self.project_manager.playback_state_changed.connect(self.on_playback_state_changed)
        self.project_manager.playhead_position_changed.connect(self.on_playhead_position_changed)
        self.project_manager.track_added.connect(self.on_track_added)
        self.project_manager.track_removed.connect(self.on_track_removed)
        self.project_manager.media_item_added.connect(self.on_media_item_added)
        self.project_manager.project_duration_changed.connect(self.on_project_duration_changed)
    
    # ==================== 轨道管理 ====================
    
    def add_video_track(self):
        """添加视频轨道"""
        track_id = self.project_manager.add_track(TrackType.VIDEO)
        print(f"✅ 添加视频轨道: {track_id}")
    
    def add_audio_track(self):
        """添加音频轨道"""
        track_id = self.project_manager.add_track(TrackType.AUDIO)
        print(f"✅ 添加音频轨道: {track_id}")
    
    def add_test_media(self):
        """添加测试媒体项"""
        tracks = self.project_manager.get_all_tracks()
        if not tracks:
            print("❌ 请先添加轨道")
            return
        
        # 添加到第一个轨道
        track = tracks[0]
        start_pos = self.media_start_spinbox.value()
        duration = self.media_duration_spinbox.value()
        
        # 使用测试文件路径
        test_file = f"test_{track.track_type.value}.mp4"
        
        item_id = self.project_manager.add_media_item(
            track.track_id, test_file, start_pos, duration, duration
        )
        print(f"✅ 添加测试媒体: {item_id}")
    
    # ==================== 播放控制 ====================
    
    def play(self):
        """播放"""
        self.playback_controller.play(PlaybackTrigger.USER_PLAY_BUTTON)
    
    def pause(self):
        """暂停"""
        self.playback_controller.pause()
    
    def stop(self):
        """停止"""
        self.playback_controller.stop()
    
    def on_timeline_changed(self, value):
        """时间轴滑块变化"""
        duration = self.project_manager.get_project_duration()
        position = (value / 1000.0) * duration
        
        # 使用统一的跳转方法
        self.playback_controller.seek(position, PlaybackTrigger.TIMELINE_CLICK)
    
    def on_position_spinbox_changed(self, value):
        """位置输入框变化"""
        self.playback_controller.seek(value, PlaybackTrigger.EXTERNAL_API)
    
    def on_speed_changed(self, value):
        """播放速度变化"""
        self.project_manager.set_playback_speed(value)
    
    # ==================== 效果控制 ====================
    
    def on_brightness_changed(self, value):
        """亮度变化"""
        self.brightness_value_label.setText(str(value))
        # TODO: 应用到当前选中的媒体项
        print(f"🔆 亮度调整: {value}")
    
    def on_contrast_changed(self, value):
        """对比度变化"""
        contrast_value = value / 100.0
        self.contrast_value_label.setText(f"{contrast_value:.1f}")
        # TODO: 应用到当前选中的媒体项
        print(f"🔳 对比度调整: {contrast_value}")
    
    # ==================== 信号处理 ====================
    
    def on_playback_state_changed(self, state: PlaybackState):
        """播放状态变化"""
        self.state_label.setText(f"播放状态: {state.value}")
        
        # 更新按钮状态
        if state == PlaybackState.PLAYING:
            self.play_btn.setText("暂停")
        else:
            self.play_btn.setText("播放")
    
    def on_playhead_position_changed(self, position: float):
        """播放头位置变化"""
        self.playhead_label.setText(f"播放头位置: {position:.2f}s")
        
        # 更新时间轴滑块
        duration = self.project_manager.get_project_duration()
        if duration > 0:
            slider_value = int((position / duration) * 1000)
            self.timeline_slider.blockSignals(True)
            self.timeline_slider.setValue(slider_value)
            self.timeline_slider.blockSignals(False)
        
        # 更新位置输入框
        self.position_spinbox.blockSignals(True)
        self.position_spinbox.setValue(position)
        self.position_spinbox.blockSignals(False)
    
    def on_track_added(self, track_id: str):
        """轨道添加"""
        track = self.project_manager.get_track(track_id)
        if track:
            self.track_list.addItem(f"{track.name} ({track.track_type.value})")
            self.track_count_label.setText(f"轨道数量: {self.project_manager.get_track_count()}")
    
    def on_track_removed(self, track_id: str):
        """轨道移除"""
        self.track_count_label.setText(f"轨道数量: {self.project_manager.get_track_count()}")
    
    def on_media_item_added(self, track_id: str, item_id: str):
        """媒体项添加"""
        self.media_count_label.setText(f"媒体项数量: {self.project_manager.get_media_count()}")
    
    def on_project_duration_changed(self, duration: float):
        """项目时长变化"""
        self.duration_label.setText(f"项目时长: {duration:.2f}s")
        self.position_spinbox.setMaximum(duration)
    
    # ==================== 初始化和重置 ====================
    
    def init_default_project(self):
        """初始化默认项目"""
        self.project_manager.initialize_default_project()
        self.track_list.clear()
        print("🔄 项目已初始化")
    
    def reset_all_parameters(self):
        """重置所有参数"""
        self.brightness_slider.setValue(0)
        self.contrast_slider.setValue(100)
        self.speed_spinbox.setValue(1.0)
        self.media_start_spinbox.setValue(0.0)
        self.media_duration_spinbox.setValue(5.0)
        print("🔄 参数已重置")

class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("统一架构演示 - 数据驱动视频编辑")
        self.setGeometry(100, 100, 1000, 800)
        
        # 创建核心组件
        self.project_manager = UnifiedProjectManager()
        self.playback_controller = UnifiedPlaybackController(self.project_manager)
        
        # 创建控制面板
        self.control_panel = ProjectControlPanel(
            self.project_manager, self.playback_controller
        )
        
        self.setCentralWidget(self.control_panel)
    
    def closeEvent(self, event):
        """窗口关闭时清理资源"""
        self.playback_controller.cleanup()
        self.project_manager.cleanup()
        event.accept()

def main():
    app = QApplication(sys.argv)
    
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
