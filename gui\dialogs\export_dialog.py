#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导出对话框
"""

import os
from pathlib import Path

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QComboBox, QSpinBox, QSlider,
    QLineEdit, QFileDialog, QGroupBox, QCheckBox,
    QProgressBar, QTextEdit, QTabWidget, QWidget
)
from PySide6.QtCore import Qt, Signal

class ExportDialog(QDialog):
    """导出对话框"""
    
    def __init__(self, config, parent=None):
        super().__init__(parent)
        self.config = config
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("导出视频")
        self.setModal(True)
        self.resize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        
        # 基本设置选项卡
        basic_tab = QWidget()
        self.create_basic_tab(basic_tab)
        tab_widget.addTab(basic_tab, "基本设置")
        
        # 高级设置选项卡
        advanced_tab = QWidget()
        self.create_advanced_tab(advanced_tab)
        tab_widget.addTab(advanced_tab, "高级设置")
        
        layout.addWidget(tab_widget)
        
        # 按钮组
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        export_btn = QPushButton("导出")
        export_btn.clicked.connect(self.accept)
        button_layout.addWidget(export_btn)
        
        layout.addLayout(button_layout)
    
    def create_basic_tab(self, tab):
        """创建基本设置选项卡"""
        layout = QGridLayout(tab)
        
        # 输出文件
        layout.addWidget(QLabel("输出文件:"), 0, 0)
        self.output_path_edit = QLineEdit()
        self.output_path_edit.setText(
            str(Path(self.config.get('paths.last_export_dir')) / "output.mp4")
        )
        layout.addWidget(self.output_path_edit, 0, 1)
        
        browse_btn = QPushButton("浏览...")
        browse_btn.clicked.connect(self.browse_output_file)
        layout.addWidget(browse_btn, 0, 2)
        
        # 输出格式
        layout.addWidget(QLabel("格式:"), 1, 0)
        self.format_combo = QComboBox()
        self.format_combo.addItems(["MP4", "AVI", "MOV", "MKV", "WMV"])
        self.format_combo.currentTextChanged.connect(self.on_format_changed)
        layout.addWidget(self.format_combo, 1, 1, 1, 2)
        
        # 质量预设
        layout.addWidget(QLabel("质量:"), 2, 0)
        self.quality_combo = QComboBox()
        self.quality_combo.addItems(["低 (快速)", "中 (平衡)", "高 (质量)", "最高 (慢速)"])
        self.quality_combo.setCurrentIndex(2)
        layout.addWidget(self.quality_combo, 2, 1, 1, 2)
        
        # 分辨率
        layout.addWidget(QLabel("分辨率:"), 3, 0)
        self.resolution_combo = QComboBox()
        self.resolution_combo.addItems([
            "原始", "1920x1080", "1280x720", "960x540", "640x360"
        ])
        layout.addWidget(self.resolution_combo, 3, 1, 1, 2)
        
        # 帧率
        layout.addWidget(QLabel("帧率:"), 4, 0)
        self.fps_spinbox = QSpinBox()
        self.fps_spinbox.setRange(1, 60)
        self.fps_spinbox.setValue(30)
        self.fps_spinbox.setSuffix(" fps")
        layout.addWidget(self.fps_spinbox, 4, 1, 1, 2)
    
    def create_advanced_tab(self, tab):
        """创建高级设置选项卡"""
        layout = QVBoxLayout(tab)
        
        # 视频编码设置
        video_group = QGroupBox("视频编码")
        video_layout = QGridLayout(video_group)
        
        # 编码器
        video_layout.addWidget(QLabel("编码器:"), 0, 0)
        self.video_codec_combo = QComboBox()
        self.video_codec_combo.addItems(["H.264", "H.265", "VP9", "AV1"])
        video_layout.addWidget(self.video_codec_combo, 0, 1)
        
        # 比特率
        video_layout.addWidget(QLabel("比特率:"), 1, 0)
        self.bitrate_spinbox = QSpinBox()
        self.bitrate_spinbox.setRange(100, 50000)
        self.bitrate_spinbox.setValue(5000)
        self.bitrate_spinbox.setSuffix(" kbps")
        video_layout.addWidget(self.bitrate_spinbox, 1, 1)
        
        layout.addWidget(video_group)
        
        # 音频编码设置
        audio_group = QGroupBox("音频编码")
        audio_layout = QGridLayout(audio_group)
        
        # 音频编码器
        audio_layout.addWidget(QLabel("编码器:"), 0, 0)
        self.audio_codec_combo = QComboBox()
        self.audio_codec_combo.addItems(["AAC", "MP3", "OGG"])
        audio_layout.addWidget(self.audio_codec_combo, 0, 1)
        
        # 音频比特率
        audio_layout.addWidget(QLabel("比特率:"), 1, 0)
        self.audio_bitrate_combo = QComboBox()
        self.audio_bitrate_combo.addItems(["128", "192", "256", "320"])
        self.audio_bitrate_combo.setCurrentText("192")
        audio_layout.addWidget(self.audio_bitrate_combo, 1, 1)
        
        # 采样率
        audio_layout.addWidget(QLabel("采样率:"), 2, 0)
        self.sample_rate_combo = QComboBox()
        self.sample_rate_combo.addItems(["44100", "48000"])
        self.sample_rate_combo.setCurrentText("48000")
        audio_layout.addWidget(self.sample_rate_combo, 2, 1)
        
        layout.addWidget(audio_group)
        
        # 其他选项
        options_group = QGroupBox("其他选项")
        options_layout = QVBoxLayout(options_group)
        
        self.hardware_accel_check = QCheckBox("启用硬件加速")
        options_layout.addWidget(self.hardware_accel_check)
        
        self.two_pass_check = QCheckBox("两遍编码 (更好的质量)")
        options_layout.addWidget(self.two_pass_check)
        
        layout.addWidget(options_group)
        
        layout.addStretch()
    
    def browse_output_file(self):
        """浏览输出文件"""
        current_path = self.output_path_edit.text()
        if not current_path:
            current_path = str(Path(self.config.get('paths.last_export_dir')) / "output.mp4")
        
        format_ext = self.format_combo.currentText().lower()
        filter_text = f"{format_ext.upper()} 文件 (*.{format_ext});;所有文件 (*.*)"
        
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "选择输出文件",
            current_path,
            filter_text
        )
        
        if file_path:
            self.output_path_edit.setText(file_path)
            # 保存导出目录
            self.config.set('paths.last_export_dir', os.path.dirname(file_path))
            self.config.save_config()
    
    def on_format_changed(self, format_text):
        """格式改变时更新文件扩展名"""
        current_path = self.output_path_edit.text()
        if current_path:
            path = Path(current_path)
            new_path = path.with_suffix(f".{format_text.lower()}")
            self.output_path_edit.setText(str(new_path))
    
    def get_export_settings(self) -> dict:
        """获取导出设置"""
        # 解析质量设置
        quality_map = {
            "低 (快速)": "ultrafast",
            "中 (平衡)": "medium", 
            "高 (质量)": "slow",
            "最高 (慢速)": "veryslow"
        }
        
        # 解析分辨率
        resolution = self.resolution_combo.currentText()
        if resolution == "原始":
            width, height = None, None
        else:
            width, height = map(int, resolution.split('x'))
        
        return {
            'output_path': self.output_path_edit.text(),
            'format': self.format_combo.currentText().lower(),
            'quality_preset': quality_map[self.quality_combo.currentText()],
            'width': width,
            'height': height,
            'fps': self.fps_spinbox.value(),
            'video_codec': self.video_codec_combo.currentText().lower(),
            'video_bitrate': self.bitrate_spinbox.value(),
            'audio_codec': self.audio_codec_combo.currentText().lower(),
            'audio_bitrate': int(self.audio_bitrate_combo.currentText()),
            'sample_rate': int(self.sample_rate_combo.currentText()),
            'hardware_accel': self.hardware_accel_check.isChecked(),
            'two_pass': self.two_pass_check.isChecked()
        } 