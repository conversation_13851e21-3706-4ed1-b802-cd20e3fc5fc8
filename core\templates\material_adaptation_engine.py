#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
素材动态适配引擎
基于踩点转场技术方案实现智能素材分段和动态适配
"""

import cv2
import numpy as np
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path
import json
import time
from core.logger import get_logger
from core.beat_detection_engine import BeatAnalysisResult, BeatPoint

try:
    from moviepy.editor import VideoFileClip
    MOVIEPY_AVAILABLE = True
except ImportError:
    MOVIEPY_AVAILABLE = False
    print("⚠️ moviepy未安装，视频处理功能将受限")


@dataclass
class VideoSegment:
    """视频片段数据结构"""
    start_time: float  # 开始时间（秒）
    end_time: float  # 结束时间（秒）
    duration: float  # 时长（秒）
    segment_type: str  # 片段类型: CLOSE_UP, MEDIUM, WIDE, ENVIRONMENT
    quality_score: float  # 质量评分 (0-1)
    motion_level: str  # 运动强度: LOW, MEDIUM, HIGH
    face_count: int  # 人脸数量
    is_valid: bool = True  # 是否有效
    adaptation_strategy: str = "NORMAL"  # 适配策略


@dataclass
class AdaptationPlan:
    """适配计划"""
    target_duration: float  # 目标总时长
    beat_segments: List[Dict[str, Any]]  # 节拍片段分配
    fill_segments: List[VideoSegment]  # 填充片段
    removed_segments: List[VideoSegment]  # 删除的无效片段
    adaptation_ratio: float  # 适配比例


class MaterialAdaptationEngine:
    """素材动态适配引擎"""
    
    def __init__(self):
        self.logger = get_logger('material_adaptation')
        
        # 适配参数
        self.min_segment_duration = 0.5  # 最小片段时长（秒）
        self.max_segment_duration = 4.0  # 最大片段时长（秒）
        self.quality_threshold = 0.3  # 质量阈值
        self.face_detection_enabled = True
        
        # 缓存
        self._analysis_cache = {}
        
        self.logger.info("素材动态适配引擎初始化完成")
    
    def analyze_video_material(self, video_path: str) -> List[VideoSegment]:
        """
        分析视频素材，识别不同类型的片段
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            视频片段列表
        """
        if video_path in self._analysis_cache:
            return self._analysis_cache[video_path]
        
        try:
            self.logger.info(f"开始分析视频素材: {Path(video_path).name}")
            start_time = time.time()
            
            segments = []
            
            if MOVIEPY_AVAILABLE:
                segments = self._analyze_with_moviepy(video_path)
            else:
                segments = self._analyze_with_opencv(video_path)
            
            # 缓存结果
            self._analysis_cache[video_path] = segments
            
            analysis_time = time.time() - start_time
            self.logger.info(
                f"视频分析完成: {len(segments)}个片段, "
                f"用时={analysis_time:.2f}s"
            )
            
            return segments
            
        except Exception as e:
            self.logger.error(f"视频分析失败: {e}")
            return []
    
    def _analyze_with_moviepy(self, video_path: str) -> List[VideoSegment]:
        """使用MoviePy分析视频"""
        try:
            clip = VideoFileClip(video_path)
            duration = clip.duration
            fps = clip.fps
            
            segments = []
            segment_duration = 2.0  # 默认2秒片段
            
            current_time = 0.0
            while current_time < duration:
                end_time = min(current_time + segment_duration, duration)
                
                # 提取片段进行分析
                segment_clip = clip.subclip(current_time, end_time)
                
                # 分析片段特征
                segment_type, quality_score, motion_level, face_count = self._analyze_segment_features(
                    segment_clip, current_time
                )
                
                segment = VideoSegment(
                    start_time=current_time,
                    end_time=end_time,
                    duration=end_time - current_time,
                    segment_type=segment_type,
                    quality_score=quality_score,
                    motion_level=motion_level,
                    face_count=face_count,
                    is_valid=quality_score > self.quality_threshold
                )
                
                segments.append(segment)
                current_time = end_time
                
                segment_clip.close()
            
            clip.close()
            return segments
            
        except Exception as e:
            self.logger.error(f"MoviePy分析失败: {e}")
            return []
    
    def _analyze_with_opencv(self, video_path: str) -> List[VideoSegment]:
        """使用OpenCV分析视频"""
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise ValueError(f"无法打开视频文件: {video_path}")
            
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = total_frames / fps
            
            segments = []
            segment_frames = int(fps * 2.0)  # 2秒片段
            
            frame_count = 0
            while frame_count < total_frames:
                start_frame = frame_count
                end_frame = min(frame_count + segment_frames, total_frames)
                
                start_time = start_frame / fps
                end_time = end_frame / fps
                
                # 分析片段特征
                segment_type, quality_score, motion_level, face_count = self._analyze_opencv_segment(
                    cap, start_frame, end_frame, fps
                )
                
                segment = VideoSegment(
                    start_time=start_time,
                    end_time=end_time,
                    duration=end_time - start_time,
                    segment_type=segment_type,
                    quality_score=quality_score,
                    motion_level=motion_level,
                    face_count=face_count,
                    is_valid=quality_score > self.quality_threshold
                )
                
                segments.append(segment)
                frame_count = end_frame
            
            cap.release()
            return segments
            
        except Exception as e:
            self.logger.error(f"OpenCV分析失败: {e}")
            return []
    
    def _analyze_segment_features(self, segment_clip, start_time: float) -> Tuple[str, float, str, int]:
        """分析片段特征（MoviePy版本）"""
        try:
            # 提取中间帧进行分析
            mid_time = segment_clip.duration / 2
            frame = segment_clip.get_frame(mid_time)
            
            # 转换为OpenCV格式
            frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            
            return self._analyze_frame_features(frame_bgr)
            
        except Exception as e:
            self.logger.error(f"片段特征分析失败: {e}")
            return "MEDIUM", 0.5, "MEDIUM", 0
    
    def _analyze_opencv_segment(self, cap: cv2.VideoCapture, start_frame: int, 
                               end_frame: int, fps: float) -> Tuple[str, float, str, int]:
        """分析片段特征（OpenCV版本）"""
        try:
            # 跳转到中间帧
            mid_frame = (start_frame + end_frame) // 2
            cap.set(cv2.CAP_PROP_POS_FRAMES, mid_frame)
            
            ret, frame = cap.read()
            if not ret:
                return "MEDIUM", 0.3, "LOW", 0
            
            return self._analyze_frame_features(frame)
            
        except Exception as e:
            self.logger.error(f"OpenCV片段分析失败: {e}")
            return "MEDIUM", 0.5, "MEDIUM", 0
    
    def _analyze_frame_features(self, frame: np.ndarray) -> Tuple[str, float, str, int]:
        """分析帧特征"""
        try:
            h, w = frame.shape[:2]
            
            # 1. 人脸检测
            face_count = 0
            if self.face_detection_enabled:
                face_count = self._detect_faces(frame)
            
            # 2. 图像质量评估
            quality_score = self._assess_image_quality(frame)
            
            # 3. 运动强度估算（基于边缘密度）
            motion_level = self._estimate_motion_level(frame)
            
            # 4. 镜头类型判断
            segment_type = self._classify_shot_type(frame, face_count)
            
            return segment_type, quality_score, motion_level, face_count
            
        except Exception as e:
            self.logger.error(f"帧特征分析失败: {e}")
            return "MEDIUM", 0.5, "MEDIUM", 0
    
    def _detect_faces(self, frame: np.ndarray) -> int:
        """检测人脸数量"""
        try:
            # 使用OpenCV的Haar级联检测器
            face_cascade = cv2.CascadeClassifier(
                cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
            )
            
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            faces = face_cascade.detectMultiScale(
                gray, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30)
            )
            
            return len(faces)
            
        except Exception as e:
            self.logger.error(f"人脸检测失败: {e}")
            return 0
    
    def _assess_image_quality(self, frame: np.ndarray) -> float:
        """评估图像质量"""
        try:
            # 1. 清晰度评估（拉普拉斯方差）
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
            sharpness_score = min(1.0, laplacian_var / 1000.0)
            
            # 2. 亮度评估
            brightness = np.mean(gray)
            brightness_score = 1.0 - abs(brightness - 128) / 128.0
            
            # 3. 对比度评估
            contrast = np.std(gray)
            contrast_score = min(1.0, contrast / 64.0)
            
            # 综合质量评分
            quality_score = (sharpness_score * 0.5 + brightness_score * 0.3 + contrast_score * 0.2)
            
            return float(np.clip(quality_score, 0.0, 1.0))
            
        except Exception as e:
            self.logger.error(f"质量评估失败: {e}")
            return 0.5
    
    def _estimate_motion_level(self, frame: np.ndarray) -> str:
        """估算运动强度"""
        try:
            # 基于边缘密度估算运动强度
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            edges = cv2.Canny(gray, 50, 150)
            edge_density = np.sum(edges > 0) / edges.size
            
            if edge_density > 0.15:
                return "HIGH"
            elif edge_density > 0.08:
                return "MEDIUM"
            else:
                return "LOW"
                
        except Exception as e:
            self.logger.error(f"运动强度估算失败: {e}")
            return "MEDIUM"
    
    def _classify_shot_type(self, frame: np.ndarray, face_count: int) -> str:
        """分类镜头类型"""
        try:
            h, w = frame.shape[:2]
            
            if face_count == 0:
                return "ENVIRONMENT"
            elif face_count == 1:
                # 基于人脸大小判断镜头类型
                face_cascade = cv2.CascadeClassifier(
                    cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
                )
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                faces = face_cascade.detectMultiScale(gray, scaleFactor=1.1, minNeighbors=5)
                
                if len(faces) > 0:
                    face_area = faces[0][2] * faces[0][3]  # width * height
                    frame_area = h * w
                    face_ratio = face_area / frame_area
                    
                    if face_ratio > 0.15:
                        return "CLOSE_UP"
                    elif face_ratio > 0.05:
                        return "MEDIUM"
                    else:
                        return "WIDE"
                else:
                    return "MEDIUM"
            else:
                return "WIDE"  # 多人镜头通常是广角
                
        except Exception as e:
            self.logger.error(f"镜头分类失败: {e}")
            return "MEDIUM"
    
    def create_adaptation_plan(self, segments: List[VideoSegment], 
                             beat_analysis: BeatAnalysisResult) -> AdaptationPlan:
        """
        创建素材适配计划
        
        Args:
            segments: 视频片段列表
            beat_analysis: 节拍分析结果
            
        Returns:
            适配计划
        """
        try:
            self.logger.info("创建素材适配计划...")
            
            # 1. 过滤无效片段
            valid_segments = [seg for seg in segments if seg.is_valid]
            removed_segments = [seg for seg in segments if not seg.is_valid]
            
            # 2. 计算目标时长
            beat_points = beat_analysis.beat_points
            if len(beat_points) < 2:
                target_duration = 30.0  # 默认30秒
            else:
                target_duration = beat_points[-1].timestamp - beat_points[0].timestamp
            
            # 3. 分配节拍片段
            beat_segments = self._allocate_beat_segments(valid_segments, beat_points)
            
            # 4. 处理长镜头拆分
            beat_segments = self._split_long_segments(beat_segments, beat_points)
            
            # 5. 处理特写镜头延长
            beat_segments = self._extend_closeup_segments(beat_segments, beat_points)
            
            # 6. 添加环境空镜填充
            fill_segments = self._add_environment_fills(valid_segments, beat_segments, target_duration)
            
            # 7. 计算适配比例
            total_allocated_duration = sum(seg.get('duration', 0) for seg in beat_segments)
            adaptation_ratio = total_allocated_duration / target_duration if target_duration > 0 else 1.0
            
            plan = AdaptationPlan(
                target_duration=target_duration,
                beat_segments=beat_segments,
                fill_segments=fill_segments,
                removed_segments=removed_segments,
                adaptation_ratio=adaptation_ratio
            )
            
            self.logger.info(
                f"适配计划创建完成: 目标时长={target_duration:.1f}s, "
                f"分配片段={len(beat_segments)}, "
                f"适配比例={adaptation_ratio:.2f}"
            )
            
            return plan
            
        except Exception as e:
            self.logger.error(f"创建适配计划失败: {e}")
            return AdaptationPlan(
                target_duration=30.0,
                beat_segments=[],
                fill_segments=[],
                removed_segments=[],
                adaptation_ratio=1.0
            )
    
    def _allocate_beat_segments(self, segments: List[VideoSegment], 
                              beat_points: List[BeatPoint]) -> List[Dict[str, Any]]:
        """分配节拍片段"""
        try:
            beat_segments = []
            
            for i in range(len(beat_points) - 1):
                beat_start = beat_points[i]
                beat_end = beat_points[i + 1]
                beat_duration = beat_end.timestamp - beat_start.timestamp
                
                # 选择最适合的片段
                best_segment = self._select_best_segment(segments, beat_start, beat_duration)
                
                if best_segment:
                    beat_segment = {
                        'beat_index': i,
                        'beat_type': beat_start.beat_type,
                        'start_time': beat_start.timestamp,
                        'duration': beat_duration,
                        'source_segment': best_segment,
                        'adaptation_strategy': self._determine_adaptation_strategy(
                            best_segment, beat_duration
                        )
                    }
                    beat_segments.append(beat_segment)
            
            return beat_segments
            
        except Exception as e:
            self.logger.error(f"节拍片段分配失败: {e}")
            return []
    
    def _select_best_segment(self, segments: List[VideoSegment], 
                           beat_point: BeatPoint, target_duration: float) -> Optional[VideoSegment]:
        """选择最佳片段"""
        try:
            if not segments:
                return None
            
            # 评分标准
            scored_segments = []
            
            for segment in segments:
                score = 0.0
                
                # 1. 质量评分权重
                score += segment.quality_score * 0.4
                
                # 2. 节拍类型匹配
                if beat_point.beat_type == "STRONG" and segment.segment_type == "CLOSE_UP":
                    score += 0.3
                elif beat_point.beat_type == "MEDIUM" and segment.segment_type == "MEDIUM":
                    score += 0.2
                elif beat_point.beat_type == "WEAK" and segment.segment_type == "WIDE":
                    score += 0.1
                
                # 3. 时长匹配度
                duration_diff = abs(segment.duration - target_duration)
                duration_score = max(0.0, 1.0 - duration_diff / target_duration)
                score += duration_score * 0.2
                
                # 4. 人脸数量加分
                if segment.face_count > 0:
                    score += 0.1
                
                scored_segments.append((segment, score))
            
            # 选择评分最高的片段
            scored_segments.sort(key=lambda x: x[1], reverse=True)
            return scored_segments[0][0] if scored_segments else None
            
        except Exception as e:
            self.logger.error(f"片段选择失败: {e}")
            return segments[0] if segments else None
    
    def _determine_adaptation_strategy(self, segment: VideoSegment, target_duration: float) -> str:
        """确定适配策略"""
        try:
            duration_ratio = target_duration / segment.duration
            
            if duration_ratio > 1.5:
                return "EXTEND"  # 需要延长
            elif duration_ratio < 0.7:
                return "COMPRESS"  # 需要压缩
            else:
                return "NORMAL"  # 正常使用
                
        except Exception as e:
            self.logger.error(f"适配策略确定失败: {e}")
            return "NORMAL"
    
    def _split_long_segments(self, beat_segments: List[Dict[str, Any]], 
                           beat_points: List[BeatPoint]) -> List[Dict[str, Any]]:
        """拆分长镜头"""
        try:
            processed_segments = []
            
            for beat_segment in beat_segments:
                source_segment = beat_segment['source_segment']
                target_duration = beat_segment['duration']
                
                if (source_segment.duration > self.max_segment_duration and 
                    target_duration < source_segment.duration):
                    
                    # 拆分长镜头
                    beat_segment['adaptation_strategy'] = "SPLIT"
                    beat_segment['split_start'] = 0.0
                    beat_segment['split_duration'] = target_duration
                
                processed_segments.append(beat_segment)
            
            return processed_segments
            
        except Exception as e:
            self.logger.error(f"长镜头拆分失败: {e}")
            return beat_segments
    
    def _extend_closeup_segments(self, beat_segments: List[Dict[str, Any]], 
                               beat_points: List[BeatPoint]) -> List[Dict[str, Any]]:
        """延长特写镜头"""
        try:
            for beat_segment in beat_segments:
                source_segment = beat_segment['source_segment']
                
                if (source_segment.segment_type == "CLOSE_UP" and 
                    beat_segment['adaptation_strategy'] == "EXTEND"):
                    
                    # 特写镜头延长策略
                    beat_segment['extend_method'] = "SLOW_MOTION"
                    beat_segment['speed_factor'] = 0.8  # 80%速度
            
            return beat_segments
            
        except Exception as e:
            self.logger.error(f"特写镜头延长失败: {e}")
            return beat_segments
    
    def _add_environment_fills(self, all_segments: List[VideoSegment], 
                             beat_segments: List[Dict[str, Any]], 
                             target_duration: float) -> List[VideoSegment]:
        """添加环境空镜填充"""
        try:
            # 找到环境镜头
            env_segments = [seg for seg in all_segments if seg.segment_type == "ENVIRONMENT"]
            
            # 计算需要填充的时长
            allocated_duration = sum(seg.get('duration', 0) for seg in beat_segments)
            fill_needed = max(0, target_duration - allocated_duration)
            
            fill_segments = []
            if fill_needed > 0 and env_segments:
                # 选择质量最好的环境镜头进行填充
                env_segments.sort(key=lambda x: x.quality_score, reverse=True)
                
                current_fill = 0.0
                for env_seg in env_segments:
                    if current_fill >= fill_needed:
                        break
                    
                    fill_duration = min(env_seg.duration, fill_needed - current_fill)
                    fill_segment = VideoSegment(
                        start_time=env_seg.start_time,
                        end_time=env_seg.start_time + fill_duration,
                        duration=fill_duration,
                        segment_type="ENVIRONMENT",
                        quality_score=env_seg.quality_score,
                        motion_level=env_seg.motion_level,
                        face_count=0,
                        adaptation_strategy="FILL"
                    )
                    
                    fill_segments.append(fill_segment)
                    current_fill += fill_duration
            
            return fill_segments
            
        except Exception as e:
            self.logger.error(f"环境填充失败: {e}")
            return []
