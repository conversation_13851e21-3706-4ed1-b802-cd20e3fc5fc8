# 对话框模块 (Dialogs)

## 📋 模块概述

对话框模块包含应用程序中各种功能对话框界面，提供专门的功能配置、设置和操作界面。每个对话框都针对特定功能进行优化，提供直观的用户交互体验。

## 📁 模块文件

### 📤 export_dialog.py
**功能**: 视频导出对话框
- **ExportDialog类**: 视频导出配置界面

**主要功能**:
- 导出格式选择（MP4, AVI, MOV等）
- 分辨率和帧率设置
- 视频质量和比特率配置
- 音频编码参数设置
- 导出进度显示
- 批量导出支持

**界面特性**:
- 预设配置快速选择
- 实时文件大小预估
- 导出预览功能
- 高级参数自定义

### 💎 swanksalon_dialog.py
**功能**: SWANKSALON专业功能对话框
- **SwankSalonDialog类**: SWANKSALON功能配置界面

**主要功能**:
- 理发店模板选择
- 客户信息配置
- 美颜参数调整
- 音乐选择和配置
- 批量处理设置
- 品牌元素配置

### 📋 template_dialog.py
**功能**: 模板管理对话框
- **TemplateDialog类**: 模板管理界面

**主要功能**:
- 模板浏览和预览
- 模板创建和编辑
- 模板分类管理
- 模板导入导出
- 模板应用配置

### 🔄 batch_dialog.py
**功能**: 批量处理对话框
- **BatchDialog类**: 批量处理配置界面

**主要功能**:
- 批量任务创建
- 处理参数配置
- 进度监控显示
- 错误处理设置
- 结果报告查看

### ✨ enhanced_features_dialog.py
**功能**: 增强功能对话框
- **EnhancedFeaturesDialog类**: 增强功能配置界面

**主要功能**:
- AI功能开关配置
- OCR文字识别设置
- 自动同步参数调整
- 高级算法选择
- 性能优化配置

## 🎨 设计特性

### 统一设计语言
- 一致的视觉风格
- 标准化的控件布局
- 统一的颜色方案
- 规范的字体使用

### 用户体验优化
- 直观的操作流程
- 清晰的功能分组
- 实时的参数预览
- 友好的错误提示

## 🚀 使用示例

### 导出对话框使用
```python
from gui.dialogs import ExportDialog

# 创建导出对话框
export_dialog = ExportDialog(parent_window)

# 设置默认参数
export_dialog.set_default_settings({
    'format': 'mp4',
    'resolution': '1920x1080',
    'fps': 30,
    'quality': 'high'
})

# 显示对话框
if export_dialog.exec() == QDialog.Accepted:
    settings = export_dialog.get_export_settings()
    # 执行导出操作
```

### SWANKSALON对话框使用
```python
from gui.dialogs import SwankSalonDialog

# 创建SWANKSALON对话框
salon_dialog = SwankSalonDialog()

# 设置客户信息
salon_dialog.set_customer_info({
    'name': '张三',
    'service_type': '发型改造',
    'date': '2024-07-13'
})

# 显示对话框并获取配置
if salon_dialog.exec() == QDialog.Accepted:
    config = salon_dialog.get_salon_config()
    # 应用SWANKSALON配置
```

## 📊 对话框规范

### 布局规范
- 标准对话框尺寸：600x400像素
- 按钮区域高度：50像素
- 内容区域边距：20像素
- 控件间距：10像素

### 交互规范
- 确定/取消按钮标准化
- Tab键顺序合理设置
- Enter键确认，Esc键取消
- 实时验证和提示

### 响应式设计
- 支持窗口大小调整
- 内容自适应布局
- 高DPI屏幕支持
- 多分辨率适配
