#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能批量处理对话框
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
                               QWidget, QLabel, QPushButton, QTextEdit, QProgressBar,
                               QGroupBox, QFormLayout, QSpinBox, QDoubleSpinBox,
                               QCheckBox, QComboBox, QFileDialog, QMessageBox,
                               QTableWidget, QTableWidgetItem, QHeaderView,
                               QListWidget, QListWidgetItem, QSplitter)
from PySide6.QtCore import Qt, QThread, Signal, QTimer
from PySide6.QtGui import QFont, QIcon

from core.ai_features.smart_template_processor import AutomationSettings
from core.batch_processing.batch_processor import HairSalonBatchProcessor
from core.common.logger import get_logger


class SmartBatchProcessingThread(QThread):
    """智能批量处理线程"""
    
    progress_updated = Signal(str, float)  # job_id, progress
    batch_completed = Signal(str, dict)    # job_id, result
    job_completed = Signal(str, str)       # job_id, status
    error_occurred = Signal(str, str)      # job_id, error
    
    def __init__(self, batch_processor, job_id):
        super().__init__()
        self.batch_processor = batch_processor
        self.job_id = job_id
        self.logger = get_logger('smart_batch_thread')
    
    def run(self):
        try:
            # 设置进度回调
            self.batch_processor.progress_callback = self.on_progress_update
            self.batch_processor.completion_callback = self.on_job_complete
            
            # 开始智能批量处理
            success = self.batch_processor.process_batch_with_intelligence(self.job_id)
            
            if not success:
                self.error_occurred.emit(self.job_id, "Batch processing failed")
                
        except Exception as e:
            self.error_occurred.emit(self.job_id, str(e))
    
    def on_progress_update(self, job_id: str, progress: float):
        """进度更新回调"""
        self.progress_updated.emit(job_id, progress)
    
    def on_job_complete(self, job_id: str, status: str):
        """任务完成回调"""
        self.job_completed.emit(job_id, status)


class SmartBatchDialog(QDialog):
    """智能批量处理对话框"""
    
    def __init__(self, parent=None, config=None, template_manager=None, 
                 video_processor=None, batch_processor=None):
        super().__init__(parent)
        self.config = config
        self.template_manager = template_manager
        self.video_processor = video_processor
        self.batch_processor = batch_processor or HairSalonBatchProcessor(
            config, template_manager, video_processor
        )
        self.logger = get_logger('smart_batch_dialog')
        
        self.setWindowTitle("🚀 智能批量处理")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)
        
        # 当前任务
        self.current_job_id = None
        self.processing_thread = None
        
        self.init_ui()
        self.load_templates()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：配置面板
        config_widget = self.create_config_panel()
        splitter.addWidget(config_widget)
        
        # 右侧：结果面板
        result_widget = self.create_result_panel()
        splitter.addWidget(result_widget)
        
        # 设置分割比例
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 1)
        
        layout.addWidget(splitter)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("🚀 开始智能处理")
        self.start_button.clicked.connect(self.start_smart_processing)
        
        self.stop_button = QPushButton("⏹️ 停止处理")
        self.stop_button.clicked.connect(self.stop_processing)
        self.stop_button.setEnabled(False)
        
        self.close_button = QPushButton("关闭")
        self.close_button.clicked.connect(self.accept)
        
        button_layout.addWidget(self.start_button)
        button_layout.addWidget(self.stop_button)
        button_layout.addStretch()
        button_layout.addWidget(self.close_button)
        
        layout.addLayout(button_layout)
    
    def create_config_panel(self):
        """创建配置面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 模板选择
        template_group = QGroupBox("📋 模板选择")
        template_layout = QFormLayout(template_group)
        
        self.template_combo = QComboBox()
        template_layout.addRow("模板:", self.template_combo)
        
        layout.addWidget(template_group)
        
        # 智能功能设置
        smart_group = QGroupBox("🧠 智能功能设置")
        smart_layout = QFormLayout(smart_group)
        
        self.auto_sync_check = QCheckBox("自动踩点同步")
        self.auto_sync_check.setChecked(True)
        self.auto_sync_check.setToolTip("自动将视频片段同步到音乐节拍")
        
        self.smart_trimming_check = QCheckBox("智能剪辑")
        self.smart_trimming_check.setChecked(True)
        self.smart_trimming_check.setToolTip("根据音乐节奏智能调整片段时长")
        
        self.text_extraction_check = QCheckBox("文字识别")
        self.text_extraction_check.setChecked(True)
        self.text_extraction_check.setToolTip("自动识别视频中的文字内容")
        
        self.audio_analysis_check = QCheckBox("音频分析")
        self.audio_analysis_check.setChecked(True)
        self.audio_analysis_check.setToolTip("分析音频特征并生成优化建议")
        
        smart_layout.addRow("", self.auto_sync_check)
        smart_layout.addRow("", self.smart_trimming_check)
        smart_layout.addRow("", self.text_extraction_check)
        smart_layout.addRow("", self.audio_analysis_check)
        
        layout.addWidget(smart_group)
        
        # 高级设置
        advanced_group = QGroupBox("⚙️ 高级设置")
        advanced_layout = QFormLayout(advanced_group)
        
        self.sync_tolerance_spin = QDoubleSpinBox()
        self.sync_tolerance_spin.setRange(0.1, 2.0)
        self.sync_tolerance_spin.setValue(0.3)
        self.sync_tolerance_spin.setSuffix(" 秒")
        self.sync_tolerance_spin.setToolTip("同步容差，越小越精确")
        
        self.min_confidence_spin = QDoubleSpinBox()
        self.min_confidence_spin.setRange(0.1, 1.0)
        self.min_confidence_spin.setValue(0.6)
        self.min_confidence_spin.setSingleStep(0.1)
        self.min_confidence_spin.setToolTip("最小置信度阈值")
        
        self.auto_adjust_check = QCheckBox("自动调整时长")
        self.auto_adjust_check.setChecked(True)
        self.auto_adjust_check.setToolTip("根据音乐节拍自动调整片段时长")
        
        advanced_layout.addRow("同步容差:", self.sync_tolerance_spin)
        advanced_layout.addRow("最小置信度:", self.min_confidence_spin)
        advanced_layout.addRow("", self.auto_adjust_check)
        
        layout.addWidget(advanced_group)
        
        # 批次管理
        batch_group = QGroupBox("📁 批次管理")
        batch_layout = QVBoxLayout(batch_group)
        
        batch_buttons = QHBoxLayout()
        
        self.add_batch_button = QPushButton("➕ 添加批次")
        self.add_batch_button.clicked.connect(self.add_batch)
        
        self.remove_batch_button = QPushButton("➖ 删除批次")
        self.remove_batch_button.clicked.connect(self.remove_batch)
        
        self.import_folder_button = QPushButton("📂 从文件夹导入")
        self.import_folder_button.clicked.connect(self.import_from_folder)
        
        batch_buttons.addWidget(self.add_batch_button)
        batch_buttons.addWidget(self.remove_batch_button)
        batch_buttons.addWidget(self.import_folder_button)
        
        batch_layout.addLayout(batch_buttons)
        
        # 批次列表
        self.batch_list = QListWidget()
        self.batch_list.setMaximumHeight(200)
        batch_layout.addWidget(self.batch_list)
        
        layout.addWidget(batch_group)
        
        return widget
    
    def create_result_panel(self):
        """创建结果面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 处理进度
        progress_group = QGroupBox("📊 处理进度")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        self.progress_label = QLabel("准备就绪")
        
        progress_layout.addWidget(self.progress_label)
        progress_layout.addWidget(self.progress_bar)
        
        layout.addWidget(progress_group)
        
        # 结果表格
        result_group = QGroupBox("📋 处理结果")
        result_layout = QVBoxLayout(result_group)
        
        self.result_table = QTableWidget()
        self.result_table.setColumnCount(6)
        self.result_table.setHorizontalHeaderLabels([
            "批次名称", "状态", "智能功能", "输出文件", "处理时间", "错误信息"
        ])
        self.result_table.horizontalHeader().setStretchLastSection(True)
        
        result_layout.addWidget(self.result_table)
        
        layout.addWidget(result_group)
        
        # 智能分析报告
        analysis_group = QGroupBox("🧠 智能分析报告")
        analysis_layout = QVBoxLayout(analysis_group)
        
        self.analysis_text = QTextEdit()
        self.analysis_text.setReadOnly(True)
        self.analysis_text.setMaximumHeight(200)
        
        analysis_layout.addWidget(self.analysis_text)
        
        layout.addWidget(analysis_group)
        
        return widget
    
    def load_templates(self):
        """加载模板列表"""
        try:
            templates = self.template_manager.list_templates()
            self.template_combo.clear()
            
            for template_info in templates:
                self.template_combo.addItem(
                    f"{template_info['name']} (v{template_info['version']})",
                    template_info['id']
                )
                
        except Exception as e:
            self.logger.error(f"Failed to load templates: {str(e)}")
            QMessageBox.warning(self, "警告", f"加载模板失败:\n{str(e)}")
    
    def add_batch(self):
        """添加批次"""
        # 这里应该打开一个对话框让用户选择视频文件
        # 简化实现，直接添加示例批次
        batch_name = f"批次_{self.batch_list.count() + 1}"
        item = QListWidgetItem(batch_name)
        item.setData(Qt.ItemDataRole.UserRole, {
            'batch_name': batch_name,
            'videos': {},  # 这里应该包含实际的视频文件路径
            'custom_settings': {}
        })
        self.batch_list.addItem(item)

    def load_batch_data(self, batch_data_list):
        """加载批次数据（用于智能导入）"""
        try:
            self.batch_list.clear()

            for batch_data in batch_data_list:
                batch_name = batch_data.get('batch_name', f"批次_{self.batch_list.count() + 1}")
                item = QListWidgetItem(batch_name)
                item.setData(Qt.ItemDataRole.UserRole, batch_data)
                self.batch_list.addItem(item)

            self.logger.info(f"Loaded {len(batch_data_list)} batches from smart import")

        except Exception as e:
            self.logger.error(f"Failed to load batch data: {str(e)}")

    def remove_batch(self):
        """删除批次"""
        current_row = self.batch_list.currentRow()
        if current_row >= 0:
            self.batch_list.takeItem(current_row)
    
    def import_from_folder(self):
        """从文件夹导入批次"""
        folder = QFileDialog.getExistingDirectory(self, "选择包含视频文件的文件夹")
        if folder:
            # 这里应该扫描文件夹并自动创建批次
            QMessageBox.information(self, "提示", "文件夹导入功能开发中...")
    
    def get_automation_settings(self) -> AutomationSettings:
        """获取自动化设置"""
        return AutomationSettings(
            enable_auto_sync=self.auto_sync_check.isChecked(),
            enable_smart_trimming=self.smart_trimming_check.isChecked(),
            enable_text_extraction=self.text_extraction_check.isChecked(),
            enable_audio_analysis=self.audio_analysis_check.isChecked(),
            sync_tolerance=self.sync_tolerance_spin.value(),
            min_segment_confidence=self.min_confidence_spin.value(),
            auto_adjust_timing=self.auto_adjust_check.isChecked()
        )
    
    def start_smart_processing(self):
        """开始智能处理"""
        try:
            # 验证设置
            if self.template_combo.currentData() is None:
                QMessageBox.warning(self, "警告", "请选择一个模板")
                return
            
            if self.batch_list.count() == 0:
                QMessageBox.warning(self, "警告", "请添加至少一个批次")
                return
            
            # 收集批次数据
            video_batches = []
            for i in range(self.batch_list.count()):
                item = self.batch_list.item(i)
                batch_data = item.data(Qt.ItemDataRole.UserRole)
                video_batches.append(batch_data)
            
            # 创建智能批量任务
            template_id = self.template_combo.currentData()
            automation_settings = self.get_automation_settings()
            
            self.current_job_id = self.batch_processor.create_smart_batch_job(
                template_id, video_batches, automation_settings
            )
            
            # 启动处理线程
            self.processing_thread = SmartBatchProcessingThread(
                self.batch_processor, self.current_job_id
            )
            self.processing_thread.progress_updated.connect(self.on_progress_updated)
            self.processing_thread.job_completed.connect(self.on_job_completed)
            self.processing_thread.error_occurred.connect(self.on_error_occurred)
            self.processing_thread.start()
            
            # 更新界面状态
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.progress_label.setText("正在进行智能分析和处理...")
            
            self.logger.info(f"Smart batch processing started: {self.current_job_id}")
            
        except Exception as e:
            self.logger.error(f"Failed to start smart processing: {str(e)}")
            QMessageBox.critical(self, "错误", f"启动智能处理失败:\n{str(e)}")
    
    def stop_processing(self):
        """停止处理"""
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.terminate()
            self.processing_thread.wait(3000)
        
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_label.setText("处理已停止")
    
    def on_progress_updated(self, job_id: str, progress: float):
        """进度更新"""
        self.progress_bar.setValue(int(progress))
        self.progress_label.setText(f"处理进度: {progress:.1f}%")
    
    def on_job_completed(self, job_id: str, status: str):
        """任务完成"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        
        if status == "completed":
            self.progress_label.setText("✅ 智能处理完成")
            self.show_processing_results(job_id)
        else:
            self.progress_label.setText(f"❌ 处理失败: {status}")
    
    def on_error_occurred(self, job_id: str, error: str):
        """处理错误"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_label.setText(f"❌ 处理错误: {error}")
        
        QMessageBox.critical(self, "处理错误", f"智能处理失败:\n{error}")
    
    def show_processing_results(self, job_id: str):
        """显示处理结果"""
        try:
            # 获取智能处理报告
            report = self.batch_processor.get_smart_processing_report(job_id)
            
            if report:
                # 更新分析报告
                self.update_analysis_report(report)
                
                # 更新结果表格
                self.update_result_table(job_id)
                
                # 显示成功消息
                success_rate = report['success_rate'] * 100
                QMessageBox.information(
                    self, "处理完成", 
                    f"智能批量处理完成！\n\n"
                    f"总批次: {report['total_batches']}\n"
                    f"智能处理: {report['smart_processed']}\n"
                    f"成功率: {success_rate:.1f}%\n"
                    f"处理时间: {report['processing_time']:.1f}秒"
                )
                
        except Exception as e:
            self.logger.error(f"Failed to show processing results: {str(e)}")
    
    def update_analysis_report(self, report: dict):
        """更新分析报告"""
        text = f"""🧠 智能处理分析报告

📊 处理统计:
• 总批次数: {report['total_batches']}
• 智能处理: {report['smart_processed']}
• 成功率: {report['success_rate'] * 100:.1f}%
• 处理时间: {report['processing_time']:.1f} 秒

🚀 使用的智能功能:
"""
        
        for feature, count in report['automation_features_used'].items():
            feature_names = {
                'auto_sync': '自动踩点同步',
                'smart_trimming': '智能剪辑',
                'text_extraction': '文字识别',
                'audio_analysis': '音频分析'
            }
            feature_name = feature_names.get(feature, feature)
            text += f"• {feature_name}: {count} 个批次\n"
        
        if report['errors']:
            text += f"\n❌ 错误信息:\n"
            for error in report['errors']:
                text += f"• {error}\n"
        
        self.analysis_text.setText(text)
    
    def update_result_table(self, job_id: str):
        """更新结果表格"""
        try:
            job = self.batch_processor.jobs.get(job_id)
            if not job:
                return
            
            self.result_table.setRowCount(len(job.results))
            
            for i, result in enumerate(job.results):
                # 批次名称
                self.result_table.setItem(i, 0, QTableWidgetItem(result.get('batch_name', f'批次{i+1}')))
                
                # 状态
                status = "✅ 成功" if result['success'] else "❌ 失败"
                self.result_table.setItem(i, 1, QTableWidgetItem(status))
                
                # 智能功能
                features = []
                if result.get('automation_features'):
                    for feature, enabled in result['automation_features'].items():
                        if enabled:
                            features.append(feature)
                self.result_table.setItem(i, 2, QTableWidgetItem(', '.join(features)))
                
                # 输出文件
                output_path = result.get('output_path', '')
                self.result_table.setItem(i, 3, QTableWidgetItem(output_path))
                
                # 处理时间
                self.result_table.setItem(i, 4, QTableWidgetItem("--"))
                
                # 错误信息
                error = result.get('error', '')
                self.result_table.setItem(i, 5, QTableWidgetItem(error))
                
        except Exception as e:
            self.logger.error(f"Failed to update result table: {str(e)}")
