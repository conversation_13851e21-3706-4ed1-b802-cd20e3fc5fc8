#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
理发店视频模板管理器
专为高端理发店定制的视频模板系统
"""

import os
import json
import uuid
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime

from ..common.logger import get_logger
from ..common.exceptions import TemplateError, handle_exception

@dataclass
class TemplateSegment:
    """模板片段"""
    segment_type: str  # 'intro', 'before', 'process', 'after', 'outro'
    name: str
    duration: float
    position: float  # 在时间轴上的位置
    required: bool = True  # 是否必需
    
    # 视频属性
    video_path: Optional[str] = None
    video_start: float = 0.0
    video_end: Optional[float] = None
    
    # 音频属性
    audio_path: Optional[str] = None
    audio_volume: float = 1.0
    audio_fade_in: float = 0.0
    audio_fade_out: float = 0.0
    
    # 效果和滤镜
    filters: List[Dict] = None
    transitions: List[Dict] = None
    
    def __post_init__(self):
        if self.filters is None:
            self.filters = []
        if self.transitions is None:
            self.transitions = []

@dataclass
class TemplateMusic:
    """模版音乐"""
    name: str
    audio_path: str
    start_time: float = 0.0
    duration: Optional[float] = None
    volume: float = 0.8
    fade_in: float = 2.0
    fade_out: float = 2.0
    loop: bool = False

@dataclass
class TemplateTransition:
    """模版转场"""
    name: str
    transition_type: str  # 'fade', 'dissolve', 'wipe', 'slide'
    duration: float = 1.0
    position: float = 0.0  # 转场位置
    properties: Dict = None
    
    def __post_init__(self):
        if self.properties is None:
            self.properties = {}

@dataclass
class TemplateFilter:
    """模版滤镜"""
    name: str
    filter_type: str  # 'color_grade', 'blur', 'sharpen', 'vintage'
    start_time: float
    end_time: float
    intensity: float = 1.0
    properties: Dict = None
    
    def __post_init__(self):
        if self.properties is None:
            self.properties = {}

@dataclass
class TemplateWatermark:
    """模版水印/署名"""
    name: str
    content: str  # 文字内容或图片路径
    content_type: str  # 'text' or 'image'
    position: str = 'bottom-right'  # 'top-left', 'top-right', 'bottom-left', 'bottom-right', 'center'
    start_time: float = 0.0
    duration: Optional[float] = None
    opacity: float = 0.8
    font_size: int = 24
    font_color: str = '#FFFFFF'
    background_color: Optional[str] = None

@dataclass
class HairSalonTemplate:
    """理发店视频模板"""
    id: str
    name: str
    description: str
    version: str = "1.0"
    created_at: str = ""
    updated_at: str = ""
    
    # 模板结构
    total_duration: float = 0.0
    segments: List[TemplateSegment] = None
    music: List[TemplateMusic] = None
    transitions: List[TemplateTransition] = None
    filters: List[TemplateFilter] = None
    watermarks: List[TemplateWatermark] = None
    
    # 模板设置
    resolution: tuple = (1920, 1080)
    fps: int = 30
    audio_sample_rate: int = 44100
    
    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.created_at:
            self.created_at = datetime.now().isoformat()
        if not self.updated_at:
            self.updated_at = self.created_at
        if self.segments is None:
            self.segments = []
        if self.music is None:
            self.music = []
        if self.transitions is None:
            self.transitions = []
        if self.filters is None:
            self.filters = []
        if self.watermarks is None:
            self.watermarks = []

class HairSalonTemplateManager:
    """理发店模板管理器"""
    
    def __init__(self, config):
        self.config = config
        self.templates_dir = Path(config.get_temp_dir()) / "hair_salon_templates"
        self.templates_dir.mkdir(parents=True, exist_ok=True)

        # 当前加载的模板
        self.current_template: Optional[HairSalonTemplate] = None

        # 初始化日志器
        self.logger = get_logger('template_manager')
        
        # 预设模板结构
        self.default_segments = [
            {"type": "intro", "name": "开场介绍", "duration": 5.0, "position": 0.0, "required": True},
            {"type": "before", "name": "改造前", "duration": 10.0, "position": 5.0, "required": True},
            {"type": "process", "name": "美发过程", "duration": 30.0, "position": 15.0, "required": True},
            {"type": "after", "name": "改造后", "duration": 10.0, "position": 45.0, "required": True},
            {"type": "outro", "name": "片尾署名", "duration": 5.0, "position": 55.0, "required": True}
        ]
    
    def create_default_template(self, name: str, description: str = "") -> HairSalonTemplate:
        """创建默认理发店模板"""
        try:
            template = HairSalonTemplate(
                id=str(uuid.uuid4()),
                name=name,
                description=description or f"理发店视频模板 - {name}",
                total_duration=60.0
            )
            
            # 添加默认片段
            for seg_info in self.default_segments:
                segment = TemplateSegment(
                    segment_type=seg_info["type"],
                    name=seg_info["name"],
                    duration=seg_info["duration"],
                    position=seg_info["position"],
                    required=seg_info["required"]
                )
                template.segments.append(segment)
            
            # 添加默认背景音乐
            default_music = TemplateMusic(
                name="背景音乐",
                audio_path="",  # 用户需要设置
                start_time=0.0,
                duration=60.0,
                volume=0.6,
                fade_in=2.0,
                fade_out=3.0
            )
            template.music.append(default_music)
            
            # 添加默认转场
            transitions_config = [
                {"name": "开场淡入", "type": "fade", "duration": 1.0, "position": 0.0},
                {"name": "改造前转场", "type": "dissolve", "duration": 1.5, "position": 14.0},
                {"name": "过程转场", "type": "wipe", "duration": 1.0, "position": 44.0},
                {"name": "结果转场", "type": "dissolve", "duration": 1.5, "position": 54.0},
                {"name": "片尾淡出", "type": "fade", "duration": 2.0, "position": 58.0}
            ]
            
            for trans_info in transitions_config:
                transition = TemplateTransition(
                    name=trans_info["name"],
                    transition_type=trans_info["type"],
                    duration=trans_info["duration"],
                    position=trans_info["position"]
                )
                template.transitions.append(transition)
            
            # 添加默认滤镜
            default_filters = [
                {
                    "name": "整体色调",
                    "type": "color_grade",
                    "start": 0.0,
                    "end": 60.0,
                    "intensity": 0.8,
                    "properties": {"brightness": 10, "contrast": 15, "saturation": 20}
                },
                {
                    "name": "过程高光",
                    "type": "sharpen",
                    "start": 15.0,
                    "end": 45.0,
                    "intensity": 0.6,
                    "properties": {"amount": 0.5}
                }
            ]
            
            for filter_info in default_filters:
                template_filter = TemplateFilter(
                    name=filter_info["name"],
                    filter_type=filter_info["type"],
                    start_time=filter_info["start"],
                    end_time=filter_info["end"],
                    intensity=filter_info["intensity"],
                    properties=filter_info["properties"]
                )
                template.filters.append(template_filter)
            
            # 添加默认水印
            watermark = TemplateWatermark(
                name="店铺署名",
                content="高端理发店 - 专业造型",
                content_type="text",
                position="bottom-right",
                start_time=55.0,
                duration=5.0,
                opacity=0.9,
                font_size=28,
                font_color="#FFFFFF",
                background_color="#000000AA"
            )
            template.watermarks.append(watermark)
            
            print(f"✅ 默认模板创建成功: {name}")
            return template
            
        except Exception as e:
            print(f"❌ 创建默认模板失败: {e}")
            # 返回最基本的模板
            return HairSalonTemplate(
                id=str(uuid.uuid4()),
                name=name,
                description=description or f"理发店视频模板 - {name}",
                total_duration=60.0
            )
    
    def save_template(self, template: HairSalonTemplate) -> bool:
        """保存模板"""
        try:
            # 确保模板目录存在
            self.templates_dir.mkdir(parents=True, exist_ok=True)
            
            # 更新时间戳
            template.updated_at = datetime.now().isoformat()
            if not template.created_at:
                template.created_at = template.updated_at
            
            # 验证模板数据
            if not template.name or not template.name.strip():
                print("❌ 模板名称不能为空")
                return False
            
            if not template.id:
                template.id = str(uuid.uuid4())
            
            template_file = self.templates_dir / f"{template.id}.json"
            
            # 转换为字典，处理可能的序列化问题
            try:
                template_dict = asdict(template)
                
                # 确保所有必要字段都存在
                template_dict.setdefault('version', '1.0')
                template_dict.setdefault('segments', [])
                template_dict.setdefault('music', [])
                template_dict.setdefault('transitions', [])
                template_dict.setdefault('filters', [])
                template_dict.setdefault('watermarks', [])
                template_dict.setdefault('resolution', [1920, 1080])
                template_dict.setdefault('fps', 30)
                template_dict.setdefault('audio_sample_rate', 44100)
                
            except Exception as e:
                print(f"❌ 模板序列化失败: {e}")
                return False
            
            # 保存到文件
            try:
                with open(template_file, 'w', encoding='utf-8') as f:
                    json.dump(template_dict, f, indent=2, ensure_ascii=False)
                
                print(f"✅ 模板已保存: {template.name} ({template.id})")
                print(f"   文件路径: {template_file}")
                print(f"   总时长: {template.total_duration:.1f}秒")
                print(f"   片段数: {len(template.segments)}")
                return True
                
            except Exception as e:
                print(f"❌ 文件写入失败: {e}")
                return False
            
        except Exception as e:
            print(f"❌ 保存模板失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return False
    
    def load_template(self, template_id: str) -> Optional[HairSalonTemplate]:
        """加载模板"""
        try:
            if not template_id:
                print("❌ 模板ID为空")
                return None
            
            template_file = self.templates_dir / f"{template_id}.json"
            
            if not template_file.exists():
                print(f"❌ 模板文件不存在: {template_id}")
                print(f"   查找路径: {template_file}")
                return None
            
            print(f"🔄 正在加载模板: {template_id}")
            
            with open(template_file, 'r', encoding='utf-8') as f:
                template_dict = json.load(f)
            
            # 验证模板数据
            if not isinstance(template_dict, dict):
                print("❌ 模板文件格式错误")
                return None
            
            # 重建对象
            template = self._dict_to_template(template_dict)
            if template:
                self.current_template = template
                print(f"✅ 模板已加载: {template.name}")
                print(f"   总时长: {template.total_duration:.1f}秒")
                print(f"   片段数: {len(template.segments)}")
                return template
            else:
                print("❌ 模板重建失败")
                return None
            
        except json.JSONDecodeError as e:
            print(f"❌ 模板文件JSON格式错误: {e}")
            return None
        except Exception as e:
            print(f"❌ 加载模板失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return None
    
    def list_templates(self) -> List[Dict[str, Any]]:
        """列出所有模版"""
        templates = []
        
        for template_file in self.templates_dir.glob("*.json"):
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    template_dict = json.load(f)
                
                templates.append({
                    'id': template_dict['id'],
                    'name': template_dict['name'],
                    'description': template_dict['description'],
                    'version': template_dict.get('version', '1.0'),
                    'created_at': template_dict.get('created_at', ''),
                    'updated_at': template_dict.get('updated_at', ''),
                    'total_duration': template_dict.get('total_duration', 0.0)
                })
                
            except Exception as e:
                print(f"❌ 读取模版文件失败 {template_file}: {e}")
                continue
        
        return sorted(templates, key=lambda x: x['updated_at'], reverse=True)
    
    def delete_template(self, template_id: str) -> bool:
        """删除模版"""
        try:
            template_file = self.templates_dir / f"{template_id}.json"
            
            if template_file.exists():
                template_file.unlink()
                print(f"✅ 模版已删除: {template_id}")
                return True
            else:
                print(f"❌ 模版不存在: {template_id}")
                return False
                
        except Exception as e:
            print(f"❌ 删除模版失败: {e}")
            return False
    
    def apply_template_to_videos(self, template: HairSalonTemplate, 
                               video_files: Dict[str, str]) -> Dict[str, Any]:
        """批量应用模版到视频文件
        
        Args:
            template: 模版对象
            video_files: 视频文件映射 {segment_type: file_path}
                       例如: {'before': '/path/to/before.mp4', 'process': '/path/to/process.mp4'}
        
        Returns:
            应用结果字典
        """
        try:
            result = {
                'success': True,
                'template_id': template.id,
                'template_name': template.name,
                'segments': [],
                'music': [],
                'transitions': [],
                'filters': [],
                'watermarks': [],
                'total_duration': template.total_duration,
                'errors': []
            }
            
            # 处理视频片段
            for segment in template.segments:
                segment_result = {
                    'type': segment.segment_type,
                    'name': segment.name,
                    'position': segment.position,
                    'duration': segment.duration,
                    'video_path': None,
                    'applied': False
                }
                
                # 查找对应的视频文件
                if segment.segment_type in video_files:
                    video_path = video_files[segment.segment_type]
                    if os.path.exists(video_path):
                        segment_result['video_path'] = video_path
                        segment_result['applied'] = True
                    else:
                        error_msg = f"视频文件不存在: {video_path}"
                        result['errors'].append(error_msg)
                elif segment.required:
                    error_msg = f"缺少必需的视频片段: {segment.name} ({segment.segment_type})"
                    result['errors'].append(error_msg)
                
                result['segments'].append(segment_result)
            
            # 复制音乐设置
            for music in template.music:
                result['music'].append(asdict(music))
            
            # 复制转场设置
            for transition in template.transitions:
                result['transitions'].append(asdict(transition))
            
            # 复制滤镜设置
            for filter_item in template.filters:
                result['filters'].append(asdict(filter_item))
            
            # 复制水印设置
            for watermark in template.watermarks:
                result['watermarks'].append(asdict(watermark))
            
            # 检查是否有错误
            if result['errors']:
                result['success'] = False
            
            return result
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'template_id': template.id if template else None
            }
    
    def _dict_to_template(self, template_dict: Dict) -> HairSalonTemplate:
        """将字典转换为模版对象"""
        try:
            # 重建片段
            segments = []
            segments_data = template_dict.get('segments', [])
            if segments_data and isinstance(segments_data, list):
                for seg_dict in segments_data:
                    if seg_dict and isinstance(seg_dict, dict):
                        try:
                            segment = TemplateSegment(**seg_dict)
                            segments.append(segment)
                        except Exception as e:
                            print(f"❌ 跳过无效片段: {e}")
                            continue
            
            # 重建音乐
            music = []
            music_data = template_dict.get('music', [])
            if music_data and isinstance(music_data, list):
                for music_dict in music_data:
                    if music_dict and isinstance(music_dict, dict):
                        try:
                            music_item = TemplateMusic(**music_dict)
                            music.append(music_item)
                        except Exception as e:
                            print(f"❌ 跳过无效音乐: {e}")
                            continue
            
            # 重建转场
            transitions = []
            transitions_data = template_dict.get('transitions', [])
            if transitions_data and isinstance(transitions_data, list):
                for trans_dict in transitions_data:
                    if trans_dict and isinstance(trans_dict, dict):
                        try:
                            transition = TemplateTransition(**trans_dict)
                            transitions.append(transition)
                        except Exception as e:
                            print(f"❌ 跳过无效转场: {e}")
                            continue
            
            # 重建滤镜
            filters = []
            filters_data = template_dict.get('filters', [])
            if filters_data and isinstance(filters_data, list):
                for filter_dict in filters_data:
                    if filter_dict and isinstance(filter_dict, dict):
                        try:
                            filter_item = TemplateFilter(**filter_dict)
                            filters.append(filter_item)
                        except Exception as e:
                            print(f"❌ 跳过无效滤镜: {e}")
                            continue
            
            # 重建水印
            watermarks = []
            watermarks_data = template_dict.get('watermarks', [])
            if watermarks_data and isinstance(watermarks_data, list):
                for watermark_dict in watermarks_data:
                    if watermark_dict and isinstance(watermark_dict, dict):
                        try:
                            watermark = TemplateWatermark(**watermark_dict)
                            watermarks.append(watermark)
                        except Exception as e:
                            print(f"❌ 跳过无效水印: {e}")
                            continue
            
            # 处理分辨率 - 修复None值错误
            resolution_data = template_dict.get('resolution', [1920, 1080])
            if resolution_data is None or not isinstance(resolution_data, (list, tuple)):
                resolution_data = [1920, 1080]
            try:
                resolution = tuple(resolution_data) if len(resolution_data) >= 2 else (1920, 1080)
            except (TypeError, IndexError):
                resolution = (1920, 1080)
            
            # 创建模版对象 - 确保所有必需字段都有默认值
            template = HairSalonTemplate(
                id=template_dict.get('id', str(uuid.uuid4())),
                name=template_dict.get('name', '未命名模板'),
                description=template_dict.get('description', ''),
                version=template_dict.get('version', '1.0'),
                created_at=template_dict.get('created_at', datetime.now().isoformat()),
                updated_at=template_dict.get('updated_at', datetime.now().isoformat()),
                total_duration=template_dict.get('total_duration', 0.0),
                segments=segments,
                music=music,
                transitions=transitions,
                filters=filters,
                watermarks=watermarks,
                resolution=resolution,
                fps=template_dict.get('fps', 30),
                audio_sample_rate=template_dict.get('audio_sample_rate', 44100)
            )
            
            return template
            
        except Exception as e:
            print(f"❌ 模板转换失败: {e}")
            # 返回一个基本的默认模板
            return HairSalonTemplate(
                id=str(uuid.uuid4()),
                name="默认模板",
                description="转换失败时的默认模板",
                total_duration=60.0
            ) 