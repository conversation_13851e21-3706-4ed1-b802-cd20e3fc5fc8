#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional

class Config:
    """配置管理类"""
    
    def __init__(self):
        self.config_dir = Path.home() / ".videoeditor"
        self.config_file = self.config_dir / "config.json"
        self.default_config = {
            "window": {
                "width": 1200,
                "height": 800,
                "maximized": False
            },
            "export": {
                "default_format": "mp4",
                "default_quality": "high",
                "default_fps": 30
            },
            "paths": {
                "last_import_dir": str(Path.home()),
                "last_export_dir": str(Path.home() / "Videos"),
                "temp_dir": str(Path.home() / ".videoeditor" / "temp")
            },
            "ffmpeg": {
                "path": "ffmpeg",  # 系统PATH中的ffmpeg
                "threads": -1      # 自动检测线程数
            }
        }
        
        self._config = self.load_config()
        self.ensure_directories()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if not self.config_file.exists():
            return self.default_config.copy()
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 合并默认配置以确保所有键都存在
                return self._merge_configs(self.default_config, config)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self.default_config.copy()
    
    def save_config(self):
        """保存配置文件"""
        try:
            self.config_dir.mkdir(parents=True, exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=4, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def _merge_configs(self, default: dict, user: dict) -> dict:
        """合并配置"""
        result = default.copy()
        for key, value in user.items():
            if isinstance(value, dict) and key in result:
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        return result
    
    def ensure_directories(self):
        """确保必要的目录存在"""
        temp_dir = Path(self._config["paths"]["temp_dir"])
        temp_dir.mkdir(parents=True, exist_ok=True)
    
    def get(self, key: str, default=None):
        """获取配置值"""
        keys = key.split('.')
        value = self._config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value
    
    def set(self, key: str, value):
        """设置配置值"""
        keys = key.split('.')
        config = self._config
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        config[keys[-1]] = value
    
    def get_temp_dir(self) -> Path:
        """获取临时目录"""
        return Path(self._config["paths"]["temp_dir"]) 