#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频处理工具模块
"""

import cv2
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union


def get_video_info(video_path: Union[str, Path]) -> Dict:
    """
    获取视频信息
    
    Args:
        video_path: 视频文件路径
        
    Returns:
        包含视频信息的字典
    """
    cap = cv2.VideoCapture(str(video_path))
    
    if not cap.isOpened():
        raise ValueError(f"无法打开视频文件: {video_path}")
    
    try:
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = frame_count / fps if fps > 0 else 0
        
        return {
            'width': width,
            'height': height,
            'fps': fps,
            'frame_count': frame_count,
            'duration': duration,
            'resolution': f"{width}x{height}",
            'aspect_ratio': width / height if height > 0 else 0
        }
    finally:
        cap.release()


def extract_frames(video_path: Union[str, Path], 
                  start_time: float = 0, 
                  duration: Optional[float] = None,
                  max_frames: Optional[int] = None) -> List[np.ndarray]:
    """
    从视频中提取帧
    
    Args:
        video_path: 视频文件路径
        start_time: 开始时间（秒）
        duration: 持续时间（秒）
        max_frames: 最大帧数
        
    Returns:
        帧数组列表
    """
    cap = cv2.VideoCapture(str(video_path))
    
    if not cap.isOpened():
        raise ValueError(f"无法打开视频文件: {video_path}")
    
    try:
        fps = cap.get(cv2.CAP_PROP_FPS)
        start_frame = int(start_time * fps)
        
        cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
        
        frames = []
        frame_count = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frames.append(frame)
            frame_count += 1
            
            # 检查是否达到最大帧数
            if max_frames and frame_count >= max_frames:
                break
            
            # 检查是否超过持续时间
            if duration:
                current_time = cap.get(cv2.CAP_PROP_POS_FRAMES) / fps
                if current_time >= start_time + duration:
                    break
        
        return frames
    finally:
        cap.release()


def extract_frame_at_time(video_path: Union[str, Path], time_seconds: float) -> Optional[np.ndarray]:
    """
    提取指定时间的帧
    
    Args:
        video_path: 视频文件路径
        time_seconds: 时间（秒）
        
    Returns:
        帧数组，如果失败返回None
    """
    cap = cv2.VideoCapture(str(video_path))
    
    if not cap.isOpened():
        return None
    
    try:
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_number = int(time_seconds * fps)
        
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
        ret, frame = cap.read()
        
        return frame if ret else None
    finally:
        cap.release()


def create_video_thumbnail(video_path: Union[str, Path], 
                          output_path: Union[str, Path],
                          time_seconds: float = 1.0,
                          size: Tuple[int, int] = (320, 240)) -> bool:
    """
    创建视频缩略图
    
    Args:
        video_path: 视频文件路径
        output_path: 输出图片路径
        time_seconds: 提取帧的时间点
        size: 缩略图大小
        
    Returns:
        是否成功创建缩略图
    """
    frame = extract_frame_at_time(video_path, time_seconds)
    
    if frame is None:
        return False
    
    try:
        # 调整大小
        resized_frame = cv2.resize(frame, size)
        
        # 保存图片
        success = cv2.imwrite(str(output_path), resized_frame)
        return success
    except Exception:
        return False


def get_video_codec_info(video_path: Union[str, Path]) -> Dict:
    """
    获取视频编码信息
    
    Args:
        video_path: 视频文件路径
        
    Returns:
        编码信息字典
    """
    cap = cv2.VideoCapture(str(video_path))
    
    if not cap.isOpened():
        raise ValueError(f"无法打开视频文件: {video_path}")
    
    try:
        fourcc = cap.get(cv2.CAP_PROP_FOURCC)
        codec = "".join([chr((int(fourcc) >> 8 * i) & 0xFF) for i in range(4)])
        
        return {
            'fourcc': int(fourcc),
            'codec': codec.strip(),
            'backend': cap.getBackendName()
        }
    finally:
        cap.release()
