#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频处理工具模块
"""

import wave
import numpy as np
from pathlib import Path
from typing import Dict, Optional, Tuple, Union

try:
    import librosa
    LIBROSA_AVAILABLE = True
except ImportError:
    LIBROSA_AVAILABLE = False


def get_audio_info(audio_path: Union[str, Path]) -> Dict:
    """
    获取音频信息
    
    Args:
        audio_path: 音频文件路径
        
    Returns:
        包含音频信息的字典
    """
    path = Path(audio_path)
    
    if not path.exists():
        raise FileNotFoundError(f"音频文件不存在: {audio_path}")
    
    if LIBROSA_AVAILABLE:
        return _get_audio_info_librosa(audio_path)
    else:
        return _get_audio_info_wave(audio_path)


def _get_audio_info_librosa(audio_path: Union[str, Path]) -> Dict:
    """
    使用librosa获取音频信息
    """
    try:
        y, sr = librosa.load(str(audio_path), sr=None)
        duration = len(y) / sr
        
        return {
            'sample_rate': sr,
            'duration': duration,
            'channels': 1 if y.ndim == 1 else y.shape[0],
            'samples': len(y),
            'format': 'float32',
            'bit_depth': 32
        }
    except Exception as e:
        raise ValueError(f"无法读取音频文件: {e}")


def _get_audio_info_wave(audio_path: Union[str, Path]) -> Dict:
    """
    使用wave模块获取WAV文件信息
    """
    try:
        with wave.open(str(audio_path), 'rb') as wav_file:
            frames = wav_file.getnframes()
            sample_rate = wav_file.getframerate()
            channels = wav_file.getnchannels()
            sample_width = wav_file.getsampwidth()
            duration = frames / sample_rate
            
            return {
                'sample_rate': sample_rate,
                'duration': duration,
                'channels': channels,
                'samples': frames,
                'format': f'int{sample_width * 8}',
                'bit_depth': sample_width * 8
            }
    except Exception as e:
        raise ValueError(f"无法读取WAV文件: {e}")


def extract_audio(audio_path: Union[str, Path], 
                 start_time: float = 0, 
                 duration: Optional[float] = None) -> Tuple[np.ndarray, int]:
    """
    提取音频数据
    
    Args:
        audio_path: 音频文件路径
        start_time: 开始时间（秒）
        duration: 持续时间（秒）
        
    Returns:
        音频数据数组和采样率
    """
    if not LIBROSA_AVAILABLE:
        raise ImportError("需要安装librosa库来处理音频文件")
    
    try:
        # 加载音频
        y, sr = librosa.load(str(audio_path), sr=None, offset=start_time, duration=duration)
        return y, sr
    except Exception as e:
        raise ValueError(f"无法提取音频数据: {e}")


def get_audio_features(audio_path: Union[str, Path]) -> Dict:
    """
    获取音频特征
    
    Args:
        audio_path: 音频文件路径
        
    Returns:
        音频特征字典
    """
    if not LIBROSA_AVAILABLE:
        raise ImportError("需要安装librosa库来分析音频特征")
    
    try:
        y, sr = librosa.load(str(audio_path))
        
        # 计算各种特征
        tempo, beats = librosa.beat.beat_track(y=y, sr=sr)
        spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
        spectral_rolloff = librosa.feature.spectral_rolloff(y=y, sr=sr)[0]
        zero_crossing_rate = librosa.feature.zero_crossing_rate(y)[0]
        mfccs = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13)
        
        return {
            'tempo': float(tempo),
            'beat_count': len(beats),
            'spectral_centroid_mean': float(np.mean(spectral_centroids)),
            'spectral_rolloff_mean': float(np.mean(spectral_rolloff)),
            'zero_crossing_rate_mean': float(np.mean(zero_crossing_rate)),
            'mfcc_mean': [float(np.mean(mfcc)) for mfcc in mfccs],
            'rms_energy': float(np.mean(librosa.feature.rms(y=y)[0]))
        }
    except Exception as e:
        raise ValueError(f"无法分析音频特征: {e}")


def normalize_audio(audio_data: np.ndarray, target_level: float = -20.0) -> np.ndarray:
    """
    音频标准化
    
    Args:
        audio_data: 音频数据
        target_level: 目标电平（dB）
        
    Returns:
        标准化后的音频数据
    """
    # 计算RMS
    rms = np.sqrt(np.mean(audio_data ** 2))
    
    if rms == 0:
        return audio_data
    
    # 计算当前电平
    current_level = 20 * np.log10(rms)
    
    # 计算增益
    gain_db = target_level - current_level
    gain_linear = 10 ** (gain_db / 20)
    
    # 应用增益
    normalized_audio = audio_data * gain_linear
    
    # 防止削波
    max_val = np.max(np.abs(normalized_audio))
    if max_val > 1.0:
        normalized_audio = normalized_audio / max_val
    
    return normalized_audio


def detect_silence(audio_data: np.ndarray, 
                  sample_rate: int, 
                  threshold: float = -40.0,
                  min_duration: float = 0.5) -> list:
    """
    检测静音段
    
    Args:
        audio_data: 音频数据
        sample_rate: 采样率
        threshold: 静音阈值（dB）
        min_duration: 最小静音持续时间（秒）
        
    Returns:
        静音段列表，每个元素为(开始时间, 结束时间)
    """
    # 计算能量
    frame_length = int(0.025 * sample_rate)  # 25ms帧
    hop_length = int(0.01 * sample_rate)     # 10ms跳跃
    
    energy = []
    for i in range(0, len(audio_data) - frame_length, hop_length):
        frame = audio_data[i:i + frame_length]
        frame_energy = np.sum(frame ** 2)
        energy.append(frame_energy)
    
    energy = np.array(energy)
    
    # 转换为dB
    energy_db = 10 * np.log10(energy + 1e-10)
    
    # 检测静音
    silence_mask = energy_db < threshold
    
    # 找到静音段
    silence_segments = []
    in_silence = False
    start_time = 0
    
    for i, is_silent in enumerate(silence_mask):
        time = i * hop_length / sample_rate
        
        if is_silent and not in_silence:
            # 静音开始
            in_silence = True
            start_time = time
        elif not is_silent and in_silence:
            # 静音结束
            in_silence = False
            duration = time - start_time
            if duration >= min_duration:
                silence_segments.append((start_time, time))
    
    # 处理最后一个静音段
    if in_silence:
        duration = len(audio_data) / sample_rate - start_time
        if duration >= min_duration:
            silence_segments.append((start_time, len(audio_data) / sample_rate))
    
    return silence_segments
