@echo off
chcp 65001 >nul
echo 🚀 启动视频剪辑工具...
echo.

REM 检查Python是否安装 - 先尝试py命令，再尝试python命令
py --version >nul 2>&1
if errorlevel 1 (
    python --version >nul 2>&1
    if errorlevel 1 (
        echo ❌ 错误: 未找到Python，请先安装Python 3.8+
        echo 📥 请从 https://www.python.org/downloads/ 下载并安装Python
        pause
        exit /b 1
    ) else (
        set PYTHON_CMD=python
        set PIP_CMD=pip
    )
) else (
    set PYTHON_CMD=py
    set PIP_CMD=py -m pip
)

echo ✅ 找到Python:
%PYTHON_CMD% --version

REM 检查依赖是否安装
echo 📦 检查依赖包...
%PIP_CMD% show PySide6 >nul 2>&1
if errorlevel 1 (
    echo 📥 正在安装依赖包...
    %PIP_CMD% install -r requirements.txt
    if errorlevel 1 (
        echo ⚠️  部分依赖安装失败，程序可能无法完全正常运行
        echo 💡 建议手动安装: %PIP_CMD% install PySide6 opencv-python numpy
    )
)

REM 检查FFmpeg
echo 🎬 检查FFmpeg...
ffmpeg -version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  警告: 未找到FFmpeg，部分功能可能无法使用
    echo 📥 请从 https://ffmpeg.org/download.html 下载并安装FFmpeg
    echo.
)

REM 运行程序
echo 🎯 启动程序...
%PYTHON_CMD% main.py

if errorlevel 1 (
    echo.
    echo ❌ 程序启动失败，请检查错误信息
    echo 💡 常见解决方案:
    echo    1. 确保已安装所有依赖: %PIP_CMD% install -r requirements.txt
    echo    2. 检查Python版本是否为3.8+
    echo    3. 如果缺少某些库，请单独安装
)

pause