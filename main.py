#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频剪辑工具 - 主程序
基于 MLT + FFmpeg + PySide6
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QIcon

# 添加项目路径到系统路径
sys.path.append(str(Path(__file__).parent))

from gui.main.main_window_simplified import MainWindow
from gui.components.splash_screen import LoadingManager
from core.common.config import Config

def main():
    """主函数"""
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("VIDEOCUT")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("VideoEdit Pro")

    # 设置应用程序样式 - Qt 6中默认启用高DPI支持

    def create_main_window():
        """创建主窗口的函数"""
        # 初始化配置
        config = Config()

        # 🔧 新增：集成项目管理到主窗口创建
        from gui.dialogs.project_dialog import ProjectDialog
        from core.project_manager import ProjectManager
        from PySide6.QtWidgets import QDialog

        # 创建项目管理器
        project_manager = ProjectManager()

        # 尝试自动加载最后项目
        project_data = project_manager.auto_load_last_project()

        # 如果没有最后项目或是默认项目，显示项目选择对话框
        if not project_data or project_data.get("project_info", {}).get("name") == "默认项目":
            # 显示项目选择对话框
            project_dialog = ProjectDialog(project_manager)
            if project_dialog.exec() == QDialog.DialogCode.Accepted:
                selected_project = project_dialog.get_selected_project()
                if selected_project:
                    project_data = selected_project
            else:
                # 用户取消，创建默认项目
                if not project_data:
                    project_data = project_manager.create_new_project("默认项目")

        # 创建主窗口
        main_window = MainWindow(config)
        main_window.project_manager = project_manager

        # 应用项目数据
        if project_data:
            main_window.apply_project_data(project_data)
            project_name = project_data.get("project_info", {}).get("name", "未知项目")
            main_window.setWindowTitle(f"SWANKSALON - {project_name}")
            main_window.project_modified = False

        return main_window

    # 创建并显示启动加载界面
    loading_manager = LoadingManager()
    splash = loading_manager.show_splash(create_main_window)

    # 处理启动画面事件
    app.processEvents()

    # 等待加载完成后显示主窗口
    def show_main_window():
        if splash.is_loading_complete():
            main_window = splash.get_main_window()
            if main_window:
                loading_manager.hide_splash()
                main_window.show()
            else:
                print("❌ 主窗口创建失败")
        else:
            # 如果还没加载完成，继续等待
            QTimer.singleShot(100, show_main_window)

    # 开始检查加载状态
    QTimer.singleShot(100, show_main_window)

    # 运行应用程序
    sys.exit(app.exec())

if __name__ == '__main__':
    main() 