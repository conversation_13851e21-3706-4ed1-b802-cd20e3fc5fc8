#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一管理器演示 - 展示常量管理和状态管理的使用
"""

import sys
import time
import random
from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                               QWidget, QLabel, QPushButton, QSlider, QTextEdit,
                               QGroupBox, QGridLayout, QProgressBar, QComboBox)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont

# 导入统一管理器
from core.unified_constants_manager import constants, ThemeType, QualityPreset
from core.unified_state_manager import state_manager, AppState, UIState

class ConstantsDemo(QWidget):
    """常量管理器演示"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # 颜色演示
        color_group = QGroupBox("颜色管理")
        color_layout = QGridLayout()
        
        # 显示当前主题颜色
        self.color_labels = {}
        colors = constants.get_theme_colors()
        
        row = 0
        for color_name, color_value in colors.items():
            label = QLabel(f"{color_name}:")
            color_display = QLabel(color_value)
            color_display.setStyleSheet(f"background-color: {color_value}; color: white; padding: 5px;")
            
            color_layout.addWidget(label, row, 0)
            color_layout.addWidget(color_display, row, 1)
            self.color_labels[color_name] = color_display
            row += 1
        
        # 主题切换按钮
        theme_layout = QHBoxLayout()
        self.dark_theme_btn = QPushButton("深色主题")
        self.light_theme_btn = QPushButton("浅色主题")
        
        self.dark_theme_btn.clicked.connect(lambda: self.switch_theme(ThemeType.DARK))
        self.light_theme_btn.clicked.connect(lambda: self.switch_theme(ThemeType.LIGHT))
        
        theme_layout.addWidget(self.dark_theme_btn)
        theme_layout.addWidget(self.light_theme_btn)
        color_layout.addLayout(theme_layout, row, 0, 1, 2)
        
        color_group.setLayout(color_layout)
        layout.addWidget(color_group)
        
        # 尺寸演示
        dimension_group = QGroupBox("尺寸管理")
        dimension_layout = QGridLayout()
        
        # 显示关键尺寸
        dimensions = [
            ("窗口宽度", "default_window_width"),
            ("窗口高度", "default_window_height"),
            ("轨道高度", "track_height"),
            ("轨道间距", "track_spacing"),
            ("媒体库宽度", "media_library_width"),
            ("预览宽度", "video_preview_width"),
        ]
        
        self.dimension_labels = {}
        for i, (name, attr) in enumerate(dimensions):
            label = QLabel(f"{name}:")
            value_label = QLabel(str(constants.get_dimension(attr)))
            
            dimension_layout.addWidget(label, i, 0)
            dimension_layout.addWidget(value_label, i, 1)
            self.dimension_labels[attr] = value_label
        
        dimension_group.setLayout(dimension_layout)
        layout.addWidget(dimension_group)
        
        # 配置演示
        config_group = QGroupBox("配置管理")
        config_layout = QVBoxLayout()
        
        # 缩放级别
        zoom_layout = QHBoxLayout()
        zoom_layout.addWidget(QLabel("缩放级别:"))
        zoom_levels = constants.get_zoom_levels()
        self.zoom_label = QLabel(f"{zoom_levels}")
        zoom_layout.addWidget(self.zoom_label)
        config_layout.addLayout(zoom_layout)
        
        # 播放速度
        speed_layout = QHBoxLayout()
        speed_layout.addWidget(QLabel("播放速度:"))
        speeds = constants.get_playback_speeds()
        self.speed_label = QLabel(f"{speeds}")
        speed_layout.addWidget(self.speed_label)
        config_layout.addLayout(speed_layout)
        
        # 支持格式
        format_layout = QHBoxLayout()
        format_layout.addWidget(QLabel("视频格式:"))
        video_formats = constants.get_supported_formats('video')
        self.format_label = QLabel(f"{video_formats}")
        format_layout.addWidget(self.format_label)
        config_layout.addLayout(format_layout)
        
        config_group.setLayout(config_layout)
        layout.addWidget(config_group)
        
        # 质量预设演示
        quality_group = QGroupBox("质量预设")
        quality_layout = QVBoxLayout()
        
        for quality in QualityPreset:
            scale = constants.get_quality_scale(quality)
            quality_layout.addWidget(QLabel(f"{quality.value}: {scale}x"))
        
        quality_group.setLayout(quality_layout)
        layout.addWidget(quality_group)
        
        self.setLayout(layout)
    
    def switch_theme(self, theme: ThemeType):
        """切换主题"""
        constants.update_theme(theme)
        self.update_color_display()
    
    def update_color_display(self):
        """更新颜色显示"""
        colors = constants.get_theme_colors()
        for color_name, color_value in colors.items():
            if color_name in self.color_labels:
                label = self.color_labels[color_name]
                label.setText(color_value)
                label.setStyleSheet(f"background-color: {color_value}; color: white; padding: 5px;")

class StateDemo(QWidget):
    """状态管理器演示"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.connect_signals()
        
        # 性能监控定时器
        self.perf_timer = QTimer()
        self.perf_timer.timeout.connect(self.update_performance)
        self.perf_timer.start(1000)  # 每秒更新
    
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # 应用状态
        app_state_group = QGroupBox("应用状态")
        app_state_layout = QVBoxLayout()
        
        self.app_state_label = QLabel(f"当前状态: {state_manager.get_app_state().value}")
        app_state_layout.addWidget(self.app_state_label)
        
        # 状态切换按钮
        state_buttons = QHBoxLayout()
        
        ready_btn = QPushButton("就绪")
        busy_btn = QPushButton("忙碌")
        error_btn = QPushButton("错误")
        
        ready_btn.clicked.connect(lambda: state_manager.set_app_state(AppState.READY))
        busy_btn.clicked.connect(lambda: state_manager.set_app_state(AppState.BUSY))
        error_btn.clicked.connect(lambda: state_manager.set_app_state(AppState.ERROR))
        
        state_buttons.addWidget(ready_btn)
        state_buttons.addWidget(busy_btn)
        state_buttons.addWidget(error_btn)
        app_state_layout.addLayout(state_buttons)
        
        app_state_group.setLayout(app_state_layout)
        layout.addWidget(app_state_group)
        
        # 窗口状态
        window_state_group = QGroupBox("窗口状态")
        window_state_layout = QVBoxLayout()
        
        window_state = state_manager.get_window_state()
        self.window_info_label = QLabel(
            f"位置: ({window_state.x}, {window_state.y})\n"
            f"尺寸: {window_state.width}x{window_state.height}\n"
            f"最大化: {window_state.is_maximized}\n"
            f"刘海屏: {window_state.has_notch}"
        )
        window_state_layout.addWidget(self.window_info_label)
        
        # 窗口操作按钮
        window_buttons = QHBoxLayout()
        
        maximize_btn = QPushButton("最大化")
        notch_btn = QPushButton("模拟刘海屏")
        
        maximize_btn.clicked.connect(lambda: state_manager.set_window_maximized(True))
        notch_btn.clicked.connect(lambda: state_manager.set_notch_info(True, 30))
        
        window_buttons.addWidget(maximize_btn)
        window_buttons.addWidget(notch_btn)
        window_state_layout.addLayout(window_buttons)
        
        window_state_group.setLayout(window_state_layout)
        layout.addWidget(window_state_group)
        
        # 视口状态
        viewport_group = QGroupBox("视口状态")
        viewport_layout = QVBoxLayout()
        
        # 时间轴缩放
        zoom_layout = QHBoxLayout()
        zoom_layout.addWidget(QLabel("时间轴缩放:"))
        
        self.zoom_slider = QSlider(Qt.Orientation.Horizontal)
        zoom_levels = constants.get_zoom_levels()
        self.zoom_slider.setRange(0, len(zoom_levels) - 1)
        self.zoom_slider.setValue(6)  # 默认值
        self.zoom_slider.valueChanged.connect(self.on_zoom_changed)
        
        zoom_layout.addWidget(self.zoom_slider)
        self.zoom_value_label = QLabel("100 px/s")
        zoom_layout.addWidget(self.zoom_value_label)
        viewport_layout.addLayout(zoom_layout)
        
        # 滚动位置
        scroll_layout = QHBoxLayout()
        scroll_layout.addWidget(QLabel("滚动位置:"))
        
        self.scroll_slider = QSlider(Qt.Orientation.Horizontal)
        self.scroll_slider.setRange(0, 1000)
        self.scroll_slider.valueChanged.connect(self.on_scroll_changed)
        
        scroll_layout.addWidget(self.scroll_slider)
        self.scroll_value_label = QLabel("0")
        scroll_layout.addWidget(self.scroll_value_label)
        viewport_layout.addLayout(scroll_layout)
        
        viewport_group.setLayout(viewport_layout)
        layout.addWidget(viewport_group)
        
        # 选择状态
        selection_group = QGroupBox("选择状态")
        selection_layout = QVBoxLayout()
        
        self.selection_info_label = QLabel("无选择")
        selection_layout.addWidget(self.selection_info_label)
        
        # 选择操作按钮
        selection_buttons = QHBoxLayout()
        
        select_track_btn = QPushButton("选择轨道")
        select_media_btn = QPushButton("选择媒体")
        clear_selection_btn = QPushButton("清空选择")
        
        select_track_btn.clicked.connect(lambda: state_manager.set_selected_tracks(["track_1", "track_2"]))
        select_media_btn.clicked.connect(lambda: state_manager.set_selected_media_items(["media_1", "media_2"]))
        clear_selection_btn.clicked.connect(lambda: state_manager.clear_selection())
        
        selection_buttons.addWidget(select_track_btn)
        selection_buttons.addWidget(select_media_btn)
        selection_buttons.addWidget(clear_selection_btn)
        selection_layout.addLayout(selection_buttons)
        
        selection_group.setLayout(selection_layout)
        layout.addWidget(selection_group)
        
        # 拖拽状态
        drag_group = QGroupBox("拖拽状态")
        drag_layout = QVBoxLayout()
        
        self.drag_info_label = QLabel("无拖拽")
        drag_layout.addWidget(self.drag_info_label)
        
        # 拖拽操作按钮
        drag_buttons = QHBoxLayout()
        
        start_drag_btn = QPushButton("开始拖拽")
        end_drag_btn = QPushButton("结束拖拽")
        
        start_drag_btn.clicked.connect(lambda: state_manager.start_drag("media", "media_library", "test_data", (100, 200)))
        end_drag_btn.clicked.connect(lambda: state_manager.end_drag())
        
        drag_buttons.addWidget(start_drag_btn)
        drag_buttons.addWidget(end_drag_btn)
        drag_layout.addLayout(drag_buttons)
        
        drag_group.setLayout(drag_layout)
        layout.addWidget(drag_group)
        
        # 性能监控
        perf_group = QGroupBox("性能监控")
        perf_layout = QVBoxLayout()
        
        self.fps_label = QLabel("FPS: 0.0")
        self.memory_label = QLabel("内存: 0.0 MB")
        self.cpu_label = QLabel("CPU: 0.0%")
        
        perf_layout.addWidget(self.fps_label)
        perf_layout.addWidget(self.memory_label)
        perf_layout.addWidget(self.cpu_label)
        
        perf_group.setLayout(perf_layout)
        layout.addWidget(perf_group)
        
        self.setLayout(layout)
    
    def connect_signals(self):
        """连接信号"""
        state_manager.app_state_changed.connect(self.on_app_state_changed)
        state_manager.window_state_changed.connect(self.on_window_state_changed)
        state_manager.viewport_changed.connect(self.on_viewport_changed)
        state_manager.selection_changed.connect(self.on_selection_changed)
        state_manager.drag_state_changed.connect(self.on_drag_state_changed)
        state_manager.performance_updated.connect(self.on_performance_updated)
    
    def on_zoom_changed(self, value):
        """缩放变化"""
        zoom_levels = constants.get_zoom_levels()
        if 0 <= value < len(zoom_levels):
            pps = zoom_levels[value]
            self.zoom_value_label.setText(f"{pps} px/s")
            state_manager.update_timeline_viewport(zoom_level=value, pixels_per_second=pps)
    
    def on_scroll_changed(self, value):
        """滚动变化"""
        self.scroll_value_label.setText(str(value))
        state_manager.update_timeline_viewport(scroll_x=value)
    
    def on_app_state_changed(self, state):
        """应用状态变化"""
        self.app_state_label.setText(f"当前状态: {state.value}")
    
    def on_window_state_changed(self, window_state):
        """窗口状态变化"""
        self.window_info_label.setText(
            f"位置: ({window_state.x}, {window_state.y})\n"
            f"尺寸: {window_state.width}x{window_state.height}\n"
            f"最大化: {window_state.is_maximized}\n"
            f"刘海屏: {window_state.has_notch}"
        )
    
    def on_viewport_changed(self, viewport_state):
        """视口状态变化"""
        # 更新滑块位置（避免循环）
        self.zoom_slider.blockSignals(True)
        self.zoom_slider.setValue(viewport_state.timeline_zoom_level)
        self.zoom_slider.blockSignals(False)
        
        self.scroll_slider.blockSignals(True)
        self.scroll_slider.setValue(viewport_state.timeline_scroll_x)
        self.scroll_slider.blockSignals(False)
    
    def on_selection_changed(self, selection_state):
        """选择状态变化"""
        info = []
        if selection_state.selected_tracks:
            info.append(f"轨道: {selection_state.selected_tracks}")
        if selection_state.selected_media_items:
            info.append(f"媒体: {selection_state.selected_media_items}")
        
        if info:
            self.selection_info_label.setText("\n".join(info))
        else:
            self.selection_info_label.setText("无选择")
    
    def on_drag_state_changed(self, drag_state):
        """拖拽状态变化"""
        if drag_state.is_dragging:
            self.drag_info_label.setText(
                f"拖拽中: {drag_state.drag_type}\n"
                f"源: {drag_state.drag_source}\n"
                f"位置: {drag_state.drag_current_pos}"
            )
        else:
            self.drag_info_label.setText("无拖拽")
    
    def on_performance_updated(self, metrics):
        """性能指标更新"""
        self.fps_label.setText(f"FPS: {metrics.fps:.1f}")
        self.memory_label.setText(f"内存: {metrics.memory_usage:.1f} MB")
        self.cpu_label.setText(f"CPU: {metrics.cpu_usage:.1f}%")
    
    def update_performance(self):
        """更新性能数据（模拟）"""
        # 模拟性能数据
        fps = random.uniform(25, 60)
        memory = random.uniform(100, 500)
        cpu = random.uniform(10, 80)
        render_time = random.uniform(5, 20)
        
        state_manager.update_performance_metrics(fps, memory, cpu, render_time)

class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("统一管理器演示")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央组件
        central_widget = QWidget()
        layout = QHBoxLayout()
        
        # 常量管理器演示
        constants_demo = ConstantsDemo()
        layout.addWidget(constants_demo)
        
        # 状态管理器演示
        state_demo = StateDemo()
        layout.addWidget(state_demo)
        
        central_widget.setLayout(layout)
        self.setCentralWidget(central_widget)
        
        # 设置应用状态为就绪
        state_manager.set_app_state(AppState.READY)

def main():
    app = QApplication(sys.argv)
    
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
