# 模板系统模块 (Templates)

## 📋 模块概述

模板系统模块提供完整的视频模板管理、智能模板处理和素材适配功能。该模块允许用户创建、管理和应用各种视频模板，实现快速、专业的视频制作流程。

## 📁 模块文件

### 📋 template_manager.py
**功能**: 模板管理核心引擎
- **HairSalonTemplateManager类**: 理发店模板管理器

**主要功能**:
- 模板创建和编辑
- 模板保存和加载
- 模板分类管理
- 模板预览生成
- 模板版本控制
- 模板分享和导入

**模板结构**:
```python
template = {
    'id': 'template_001',
    'name': '高端理发店标准模板',
    'category': 'hair_salon',
    'segments': [...],  # 视频片段配置
    'music': {...},     # 背景音乐设置
    'transitions': [...], # 转场效果
    'filters': [...],   # 滤镜效果
    'watermark': {...}  # 水印配置
}
```

### 🧠 smart_template_processor.py
**功能**: 智能模板处理器
- **SmartTemplateProcessor类**: 智能模板处理引擎

**智能功能**:
- 模板智能匹配
- 内容自适应调整
- 时长智能优化
- 风格自动适配
- 质量智能增强

**处理流程**:
1. **内容分析** → 识别视频类型和风格
2. **模板匹配** → 选择最适合的模板
3. **参数调整** → 根据内容调整模板参数
4. **效果应用** → 应用模板效果和转场
5. **质量优化** → 最终质量检查和优化

### 🎯 material_adaptation_engine.py
**功能**: 素材适配引擎
- **MaterialAdaptationEngine类**: 素材智能适配器

**适配功能**:
- 素材尺寸自适应
- 时长智能调整
- 色彩风格统一
- 音频同步适配
- 字幕自动生成

## 🎨 模板类型

### 理发店专业模板

#### 1. 经典改造模板
- **适用场景**: 传统理发店、经典发型改造
- **特点**: 稳重、专业、突出对比效果
- **包含元素**: 
  - 改造前后对比片段
  - 理发过程精华展示
  - 客户满意表情特写
  - 店铺环境展示

#### 2. 时尚潮流模板
- **适用场景**: 时尚理发店、年轻客户群体
- **特点**: 动感、活力、色彩丰富
- **包含元素**:
  - 快节奏剪辑
  - 时尚转场效果
  - 音乐节拍同步
  - 潮流元素装饰

#### 3. 高端沙龙模板
- **适用场景**: 高档理发沙龙、VIP服务
- **特点**: 优雅、精致、品质感强
- **包含元素**:
  - 慢镜头展示
  - 优雅转场效果
  - 高品质滤镜
  - 品牌元素突出

### 通用视频模板

#### 1. 企业宣传模板
- 公司介绍视频
- 产品展示视频
- 团队介绍视频

#### 2. 活动记录模板
- 会议记录视频
- 庆典活动视频
- 培训课程视频

#### 3. 个人作品模板
- 个人简历视频
- 作品集展示
- 生活记录视频

## 🚀 使用示例

### 基础模板使用
```python
from core.templates import HairSalonTemplateManager

# 初始化模板管理器
template_manager = HairSalonTemplateManager()

# 加载模板
template = template_manager.load_template('high_end_salon_template')

# 应用模板到视频
result = template_manager.apply_template(
    template=template,
    video_files=['before.mp4', 'process.mp4', 'after.mp4'],
    music_file='background.mp3',
    output_file='final_video.mp4'
)
```

### 智能模板处理
```python
from core.templates import SmartTemplateProcessor

# 智能处理器
smart_processor = SmartTemplateProcessor()

# 智能匹配和应用模板
result = smart_processor.auto_apply_template(
    video_files=['input1.mp4', 'input2.mp4'],
    style_preference='professional',
    target_duration=60  # 目标时长60秒
)
```

### 素材适配
```python
from core.templates import MaterialAdaptationEngine

# 素材适配引擎
adaptation_engine = MaterialAdaptationEngine()

# 适配素材到模板
adapted_materials = adaptation_engine.adapt_materials(
    template=template,
    materials=['video1.mp4', 'video2.mp4', 'image1.jpg'],
    adaptation_rules={
        'resize_mode': 'smart_crop',
        'color_correction': True,
        'duration_adjustment': 'auto'
    }
)
```

## 🎯 模板配置

### 视频片段配置
```python
segment_config = {
    'type': 'before_after',  # 片段类型
    'duration': 5.0,         # 持续时间
    'position': 0,           # 在模板中的位置
    'effects': {
        'fade_in': 1.0,
        'fade_out': 1.0,
        'color_grade': {
            'brightness': 0.1,
            'contrast': 0.05,
            'saturation': 0.1
        }
    },
    'text_overlay': {
        'text': '改造前',
        'position': 'bottom_center',
        'font_size': 24,
        'color': '#FFFFFF'
    }
}
```

### 转场效果配置
```python
transition_config = {
    'type': 'cross_dissolve',  # 转场类型
    'duration': 1.5,           # 转场时长
    'easing': 'ease_in_out',   # 缓动函数
    'properties': {
        'opacity_curve': 'smooth',
        'blur_amount': 0.5
    }
}
```

### 音乐配置
```python
music_config = {
    'file_path': 'background_music.mp3',
    'volume': 0.7,
    'fade_in': 2.0,
    'fade_out': 3.0,
    'loop': True,
    'sync_to_beats': True,
    'emotion_match': 'uplifting'
}
```

## 🔧 高级功能

### 模板继承
- 基础模板扩展
- 样式覆盖机制
- 组件化模板设计
- 模板版本管理

### 动态参数
- 运行时参数调整
- 条件逻辑支持
- 变量替换机制
- 表达式计算

### 批量应用
- 多模板批量处理
- 参数批量调整
- 结果批量导出
- 进度监控

## ⚡ 性能优化

### 模板缓存
- 模板预加载
- 渲染结果缓存
- 资源智能管理
- 内存使用优化

### 并行处理
- 多模板并行应用
- 片段并行处理
- 效果并行渲染
- I/O操作优化

## 📊 质量控制

### 模板验证
- 配置完整性检查
- 资源可用性验证
- 兼容性测试
- 性能基准测试

### 输出质量
- 视频质量检查
- 音频同步验证
- 效果一致性检查
- 最终质量评估

## 🔍 错误处理

### 常见错误类型
- **TemplateNotFoundError**: 模板文件不存在
- **InvalidTemplateError**: 模板格式错误
- **MaterialMismatchError**: 素材与模板不匹配
- **RenderingError**: 渲染过程错误

### 错误恢复
- 自动错误修复
- 降级处理策略
- 用户友好提示
- 详细错误日志

## 📈 使用统计

### 模板库规模
- 预设模板数量: 50+
- 支持的视频类型: 20+
- 转场效果数量: 30+
- 滤镜效果数量: 40+

### 处理能力
- 模板应用速度: <30秒
- 支持最大时长: 60分钟
- 并发处理数量: 10个
- 成功率: >98%
