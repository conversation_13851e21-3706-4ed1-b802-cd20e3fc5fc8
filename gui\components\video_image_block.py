#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import cv2
from pathlib import Path
from PySide6.QtWidgets import QLabel
from PySide6.QtCore import Qt, Signal, QPoint, QRect, QTimer, QThread
from PySide6.QtGui import QPainter, QColor, QPen, QPixmap, QFont

class ThumbnailGenerator(QThread):
    """异步缩略图生成器"""
    
    thumbnail_ready = Signal(str, QPixmap)  # cache_key, pixmap
    
    def __init__(self):
        super().__init__()
        self.tasks = []  # [(cache_key, video_path, timestamps, width, height, video_processor)]
        self.running = True
    
    def add_task(self, cache_key, video_path, timestamps, width, height, video_processor):
        """添加生成任务"""
        self.tasks.append((cache_key, video_path, timestamps, width, height, video_processor))
        if not self.isRunning():
            self.start()
    
    def run(self):
        """执行缩略图生成"""
        while self.running and self.tasks:
            cache_key, video_path, timestamps, width, height, video_processor = self.tasks.pop(0)
            
            try:
                # 生成拼接的缩略图
                combined_pixmap = self.generate_combined_thumbnail(
                    video_path, timestamps, width, height, video_processor
                )
                
                if combined_pixmap and not combined_pixmap.isNull():
                    self.thumbnail_ready.emit(cache_key, combined_pixmap)
                    
            except Exception as e:
                print(f"生成缩略图失败: {e}")
    
    def generate_combined_thumbnail(self, video_path, timestamps, width, height, video_processor):
        """生成拼接的缩略图"""
        if not video_processor or not os.path.exists(video_path):
            return None
        
        try:
            # 计算单个缩略图尺寸
            thumb_count = len(timestamps)
            if thumb_count == 0:
                return None
            
            single_width = width // thumb_count
            single_height = height
            
            # 创建合成图像
            combined_pixmap = QPixmap(width, height)
            combined_pixmap.fill(QColor(40, 40, 50))
            
            painter = QPainter(combined_pixmap)
            painter.setRenderHint(QPainter.RenderHint.SmoothPixmapTransform)
            
            # 生成每个时间点的缩略图并拼接
            for i, timestamp in enumerate(timestamps):
                if hasattr(video_processor, 'get_frame_at_time'):
                    frame = video_processor.get_frame_at_time(video_path, timestamp)
                    if frame is not None:
                        # 转换为QPixmap
                        from PySide6.QtGui import QImage
                        height_f, width_f, channel = frame.shape
                        bytes_per_line = 3 * width_f
                        q_image = QImage(frame.data, width_f, height_f, bytes_per_line, QImage.Format.Format_RGB888)
                        pixmap = QPixmap.fromImage(q_image)
                        
                        # 缩放到合适尺寸
                        scaled_pixmap = pixmap.scaled(
                            single_width, single_height, 
                            Qt.AspectRatioMode.KeepAspectRatioByExpanding, 
                            Qt.TransformationMode.SmoothTransformation
                        )
                        
                        # 绘制到合成图像
                        x_pos = i * single_width
                        painter.drawPixmap(x_pos, 0, scaled_pixmap)
                        
                        # 绘制分隔线
                        if i > 0:
                            painter.setPen(QPen(QColor(80, 80, 80), 1))
                            painter.drawLine(x_pos, 0, x_pos, height)
            
            painter.end()
            return combined_pixmap
            
        except Exception as e:
            print(f"生成合成缩略图失败: {e}")
            return None
    
    def stop(self):
        """停止生成器"""
        self.running = False
        self.quit()
        self.wait()

class VideoImageBlock(QLabel):
    """基于QLabel的视频缩略图块 - 使用预生成的拼接图片"""
    
    # 全局缩略图生成器
    _thumbnail_generator = None
    
    def __init__(self, media_item, track_index, media_index, timeline, video_processor=None):
        super().__init__()
        self.media_item = media_item
        self.track_index = track_index
        self.media_index = media_index
        self.timeline = timeline
        self.video_processor = video_processor
        
        # 拖动相关
        self.dragging = False
        self.drag_start_pos = None
        self.original_pos = None
        
        # 裁剪相关
        self.left_trim_dragging = False
        self.right_trim_dragging = False
        self.trim_drag_start_pos = None
        self.trim_handle_width = 8
        
        # 裁剪位置（像素）
        self.left_trim_pos = 0
        self.right_trim_pos = 0
        self.preview_left_trim_pos = 0
        self.preview_right_trim_pos = 0
        
        # 检查是否是占位符
        self.is_placeholder = False
        if isinstance(media_item, dict):
            file_path = media_item.get('file_path', '')
            self.is_placeholder = (file_path.startswith('placeholder_') or
                                 media_item.get('is_placeholder', False))
        
        # 缩略图缓存
        self._thumbnail_cache = {}
        self._current_cache_key = None
        self._original_pixmap = None  # 保存原始完整图片
        self._cropped_pixmap = None   # 当前裁剪后的图片
        
        # 初始化全局缩略图生成器
        if VideoImageBlock._thumbnail_generator is None:
            VideoImageBlock._thumbnail_generator = ThumbnailGenerator()
            VideoImageBlock._thumbnail_generator.thumbnail_ready.connect(self._on_thumbnail_ready)
        
        # 设置基本属性
        self.setMinimumHeight(64)
        self.setScaledContents(True)  # 自动缩放内容
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 缩略图配置
        self.thumbnail_width = 100
        self.thumbnail_height = 60
        
        # 生成初始缩略图
        self._generate_thumbnail_if_needed()
    
    def _generate_thumbnail_if_needed(self):
        """根据当前尺寸生成缩略图"""
        if self.is_placeholder or not isinstance(self.media_item, dict):
            self._set_placeholder_image()
            return
        
        file_path = self.media_item['file_path']
        if not os.path.exists(file_path):
            self._set_placeholder_image()
            return
        
        # 计算需要的缩略图参数
        duration = self.media_item.get('duration', 0)
        trim_start = self.media_item.get('trim_start', 0)
        track_width = self.width() if self.width() > 0 else 200
        
        # 计算时间戳
        thumb_count = max(1, track_width // self.thumbnail_width)
        if track_width < self.thumbnail_width:
            thumb_count = 1
        
        timestamps = []
        for i in range(thumb_count):
            if thumb_count == 1:
                # 单帧：显示中间帧
                relative_time = duration / 2
            else:
                # 多帧：均匀分布
                relative_time = (i / (thumb_count - 1)) * duration
            
            actual_timestamp = trim_start + relative_time
            timestamps.append(actual_timestamp)
        
        # 生成缓存键
        cache_key = f"{file_path}_{thumb_count}_{track_width}x{self.height()}_{trim_start:.2f}"
        
        # 检查缓存
        if cache_key in self._thumbnail_cache:
            self._original_pixmap = self._thumbnail_cache[cache_key]
            self._apply_crop_to_image()
            return
        
        # 检查是否已经在生成
        if cache_key == self._current_cache_key:
            return
        
        self._current_cache_key = cache_key
        
        # 添加到生成队列
        if VideoImageBlock._thumbnail_generator:
            VideoImageBlock._thumbnail_generator.add_task(
                cache_key, file_path, timestamps, track_width, self.height(), self.video_processor
            )
    
    def _on_thumbnail_ready(self, cache_key, pixmap):
        """缩略图生成完成"""
        if cache_key == self._current_cache_key:
            self._thumbnail_cache[cache_key] = pixmap
            self._original_pixmap = pixmap
            self._apply_crop_to_image()
    
    def _set_placeholder_image(self):
        """设置占位符图片"""
        pixmap = QPixmap(200, 64)
        pixmap.fill(QColor(60, 60, 80))
        
        painter = QPainter(pixmap)
        painter.setPen(QColor(200, 200, 255))
        painter.setFont(QFont("Arial", 12))
        painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, "🎬 视频")
        painter.end()
        
        self._original_pixmap = pixmap
        self._apply_crop_to_image()
    
    def _apply_crop_to_image(self):
        """应用裁剪到图片 - 核心优势：直接操作图片，不需要重绘"""
        if not self._original_pixmap:
            return
        
        # 使用预览位置还是实际位置
        left_trim = self.preview_left_trim_pos if self.left_trim_dragging else self.left_trim_pos
        right_trim = self.preview_right_trim_pos if self.right_trim_dragging else self.right_trim_pos
        
        if left_trim == 0 and right_trim == 0:
            # 无裁剪，直接使用原图
            self._cropped_pixmap = self._original_pixmap
        else:
            # 创建裁剪后的图片
            original_rect = self._original_pixmap.rect()
            crop_rect = QRect(
                left_trim,
                0,
                original_rect.width() - left_trim - right_trim,
                original_rect.height()
            )
            
            # 确保裁剪区域有效
            crop_rect = crop_rect.intersected(original_rect)
            if crop_rect.width() <= 0 or crop_rect.height() <= 0:
                crop_rect = QRect(0, 0, 1, original_rect.height())
            
            self._cropped_pixmap = self._original_pixmap.copy(crop_rect)
        
        # 设置到QLabel
        self.setPixmap(self._cropped_pixmap)
    
    def resizeEvent(self, event):
        """尺寸变化时重新生成缩略图"""
        super().resizeEvent(event)
        # 只有在尺寸显著变化时才重新生成
        if abs(event.size().width() - event.oldSize().width()) > 20:
            self._generate_thumbnail_if_needed()
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            # 检查是否点击了裁剪手柄
            if self.is_on_left_trim_handle(event.pos()):
                self.left_trim_dragging = True
                self.trim_drag_start_pos = event.pos()
                return
            elif self.is_on_right_trim_handle(event.pos()):
                self.right_trim_dragging = True
                self.trim_drag_start_pos = event.pos()
                return
            
            # 普通拖拽
            self.drag_start_pos = event.pos()
            self.original_pos = self.pos()
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        # 处理裁剪拖拽
        if self.left_trim_dragging or self.right_trim_dragging:
            if self.trim_drag_start_pos:
                delta_x = event.pos().x() - self.trim_drag_start_pos.x()

                if self.left_trim_dragging:
                    # 左游标：从左边裁剪，支持负值（扩展）
                    max_extend = self.media_item.get('trim_start', 0) * getattr(self.timeline, 'pixels_per_second', 100) if isinstance(self.media_item, dict) else 0
                    self.preview_left_trim_pos = max(-max_extend, min(delta_x, self.width() - self.preview_right_trim_pos - 20))
                elif self.right_trim_dragging:
                    # 右游标：从右边裁剪
                    self.preview_right_trim_pos = max(0, min(-delta_x, self.width() - self.preview_left_trim_pos - 20))

                # 实时更新裁剪预览 - 这里是关键优势！
                self._apply_crop_to_image()
            return

        # 更新鼠标样式
        if not (event.buttons() & Qt.MouseButton.LeftButton):
            self.update_cursor(event.pos())
            return

        # 处理普通拖拽
        if (event.buttons() == Qt.MouseButton.LeftButton and
            self.drag_start_pos is not None and
            not self.left_trim_dragging and not self.right_trim_dragging):

            distance = (event.pos() - self.drag_start_pos).manhattanLength()

            if distance >= 5 and not self.dragging:
                self.dragging = True
                self.setStyleSheet("border: 2px solid #00C896;")
                # 显示所有轨道的拖拽指示器
                self.show_all_drag_indicators()

            if self.dragging:
                delta = event.pos() - self.drag_start_pos
                new_pos = self.original_pos + delta
                new_x = max(0, new_pos.x())  # 允许移动到x=0，对应时间轴0秒
                new_y = max(-50, new_pos.y())

                # 🔧 优化：拖动时减少频繁的重叠检测，只做基本的边界检查
                # 重叠检测移到 mouseReleaseEvent 中进行，提高拖动流畅度

                # 减少移动频率，提高性能
                current_pos = self.pos()
                if abs(current_pos.x() - new_x) > 2 or abs(current_pos.y() - new_y) > 2:
                    self.move(new_x, new_y)
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        # 处理裁剪释放
        if self.left_trim_dragging or self.right_trim_dragging:
            # 应用预览裁剪到实际数据
            if isinstance(self.media_item, dict):
                # 计算实际的裁剪时间
                pixels_per_second = getattr(self.timeline, 'pixels_per_second', 100)

                if self.left_trim_dragging:
                    # 左侧裁剪：增加trim_start
                    trim_delta = self.preview_left_trim_pos / pixels_per_second
                    current_trim_start = self.media_item.get('trim_start', 0)
                    self.media_item['trim_start'] = max(0, current_trim_start + trim_delta)

                if self.right_trim_dragging:
                    # 右侧裁剪：减少duration
                    trim_delta = self.preview_right_trim_pos / pixels_per_second
                    current_duration = self.media_item.get('duration', 0)
                    self.media_item['duration'] = max(0.1, current_duration - trim_delta)

                print(f"✅ 裁剪完成: trim_start={self.media_item.get('trim_start', 0):.2f}s, duration={self.media_item.get('duration', 0):.2f}s")

            # 应用裁剪到显示
            self.left_trim_pos = self.preview_left_trim_pos
            self.right_trim_pos = self.preview_right_trim_pos

            self.left_trim_dragging = False
            self.right_trim_dragging = False
            self.trim_drag_start_pos = None
            self.preview_left_trim_pos = 0
            self.preview_right_trim_pos = 0

            # 重新生成缩略图以反映裁剪
            self._generate_thumbnail_if_needed()
            return

        # 处理普通拖拽释放
        if event.button() == Qt.MouseButton.LeftButton:
            if self.dragging:
                # 应用拖拽结果，包括防重叠检测
                self.apply_drag_result()

                # 清除拖拽状态
                self.dragging = False
                self.setStyleSheet("")  # 清除拖拽样式

                # 清除所有拖拽指示器
                self.clear_all_drag_indicators()

                print("✅ 视频拖拽完成")
            elif isinstance(self.media_item, dict):
                # 素材块被点击 - 处理时间轴跳转和媒体点击
                click_x = self.x() + event.pos().x()  # 素材块在轨道中的绝对位置
                pixels_per_second = getattr(self.timeline, 'pixels_per_second', 100)
                time_pos = click_x / pixels_per_second

                # 查找MultiTrackTimeline并处理时间轴跳转
                timeline = self.timeline
                while timeline:
                    if timeline.__class__.__name__ == 'MultiTrackTimeline':
                        break
                    timeline = timeline.parent()

                if timeline and hasattr(timeline, 'on_timeline_position_changed'):
                    timeline.on_timeline_position_changed(time_pos)
                    print(f"🎯 素材块点击，移动时间轴到: {time_pos:.2f}s")

                # 处理媒体点击事件
                file_path = self.media_item['file_path']
                start_time = self.media_item.get('start_time', 0)
                print(f"🎬 视频素材块被点击: {file_path} (起点: {start_time:.2f}s)")

                if timeline and hasattr(timeline, 'on_media_clicked'):
                    timeline.on_media_clicked(file_path, start_time, 'video')

            self.drag_start_pos = None
            self.original_pos = None
    
    def is_on_left_trim_handle(self, pos):
        """检查是否在左侧裁剪手柄上"""
        return pos.x() <= self.trim_handle_width
    
    def is_on_right_trim_handle(self, pos):
        """检查是否在右侧裁剪手柄上"""
        return pos.x() >= self.width() - self.trim_handle_width
    
    def show_all_drag_indicators(self):
        """显示所有轨道的拖拽指示器 - 兼容原版接口"""
        # 查找MultiTrackTimeline并显示拖拽指示器
        timeline = self.timeline
        while timeline and not hasattr(timeline, 'show_all_drag_indicators'):
            timeline = timeline.parent()

        if timeline and hasattr(timeline, 'show_all_drag_indicators'):
            timeline.show_all_drag_indicators()

    def hide_all_drag_indicators(self):
        """隐藏所有轨道的拖拽指示器 - 兼容原版接口"""
        # 查找MultiTrackTimeline并隐藏拖拽指示器
        timeline = self.timeline
        while timeline and not hasattr(timeline, 'clear_all_drag_indicators'):
            timeline = timeline.parent()

        if timeline and hasattr(timeline, 'clear_all_drag_indicators'):
            timeline.clear_all_drag_indicators()

    def clear_all_drag_indicators(self):
        """清除所有拖拽指示器 - 兼容原版接口"""
        self.hide_all_drag_indicators()

    def check_overlap_during_drag(self, desired_time, duration):
        """拖动时检查重叠 - 兼容原版接口"""
        try:
            # 查找MultiTrackTimeline
            timeline = self.timeline
            while timeline and not hasattr(timeline, 'find_non_overlapping_position'):
                timeline = timeline.parent()

            if not timeline:
                return max(0, desired_time)

            # 找到当前素材所在的轨道
            current_track = None
            for track in timeline.tracks:
                if self.media_item in track['media_files']:
                    current_track = track
                    break

            if not current_track:
                return max(0, desired_time)

            # 临时移除当前素材，避免与自己重叠检测
            current_track['media_files'].remove(self.media_item)

            # 检查新位置是否重叠
            final_position = timeline.find_non_overlapping_position(current_track, desired_time, duration)

            # 重新添加素材到轨道
            current_track['media_files'].append(self.media_item)

            return final_position

        except Exception as e:
            print(f"❌ 拖动防重叠检测失败: {e}")
            return max(0, desired_time)

    def apply_drag_result(self):
        """应用拖拽结果 - 兼容原版接口"""
        if not isinstance(self.media_item, dict):
            return

        # 计算新的时间位置
        new_time = max(0, self.x() / getattr(self.timeline, 'pixels_per_second', 100))

        # 应用磁性吸附
        if hasattr(self.timeline, 'apply_snap'):
            new_time = self.timeline.apply_snap(new_time)

        # 应用防重叠检测
        duration = self.media_item.get('duration', 0)
        final_position = self.check_overlap_during_drag(new_time, duration)

        # 更新媒体项位置
        self.media_item['start_time'] = final_position

        # 更新显示位置
        final_x = int(final_position * getattr(self.timeline, 'pixels_per_second', 100))
        self.move(final_x, self.y())

        print(f"媒体块移动到时间: {new_time:.2f}s -> {final_position:.2f}s")

    def update_cursor(self, pos):
        """更新鼠标光标 - 兼容原版接口"""
        if self.is_on_left_trim_handle(pos) or self.is_on_right_trim_handle(pos):
            self.setCursor(Qt.CursorShape.SizeHorCursor)
        else:
            self.setCursor(Qt.CursorShape.ArrowCursor)

    def __del__(self):
        """析构函数"""
        if VideoImageBlock._thumbnail_generator:
            VideoImageBlock._thumbnail_generator.stop()
