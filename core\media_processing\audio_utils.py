#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频处理工具 - 专门处理音频文件，避免OpenCV警告
"""

import os
import subprocess
import json
from typing import Optional, List, Tuple
import tempfile
from pathlib import Path

from .logger import get_logger
from .exceptions import AudioProcessingError


class AudioUtils:
    """音频处理工具类"""
    
    def __init__(self, config=None):
        self.logger = get_logger('audio_utils')
        self.config = config
        
        # FFmpeg路径
        self.ffmpeg_path = 'ffmpeg'
        self.ffprobe_path = 'ffprobe'
        
        if config and hasattr(config, 'ffmpeg_path'):
            self.ffmpeg_path = config.ffmpeg_path
        if config and hasattr(config, 'ffprobe_path'):
            self.ffprobe_path = config.ffprobe_path
    
    def get_audio_duration(self, audio_path: str) -> float:
        """获取音频文件时长"""
        try:
            cmd = [
                self.ffprobe_path,
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                str(audio_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode != 0:
                self.logger.warning(f"FFprobe failed for {audio_path}: {result.stderr}")
                return 30.0  # 默认时长
            
            data = json.loads(result.stdout)
            duration = float(data.get('format', {}).get('duration', 30.0))
            
            self.logger.info(f"Audio duration: {duration:.2f}s for {os.path.basename(audio_path)}")
            return duration
            
        except Exception as e:
            self.logger.warning(f"Failed to get audio duration: {str(e)}")
            return 30.0
    
    def extract_audio_samples(self, audio_path: str, max_duration: float = 30.0) -> Optional[Tuple[List[float], int]]:
        """提取音频样本数据用于波形生成"""
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_path = temp_file.name
            
            try:
                # 使用FFmpeg转换为PCM格式，添加参数避免AAC警告
                cmd = [
                    self.ffmpeg_path,
                    '-threads', '1',  # 强制单线程
                    '-thread_type', 'slice',  # 使用slice线程类型
                    '-hide_banner',  # 隐藏版权信息
                    '-loglevel', 'error',  # 只显示错误信息
                    '-i', str(audio_path),
                    '-t', str(max_duration),  # 限制时长
                    '-ar', '22050',  # 降低采样率以提高性能
                    '-ac', '1',      # 单声道
                    '-acodec', 'pcm_s16le',  # 明确指定PCM编码
                    '-avoid_negative_ts', 'make_zero',  # 避免负时间戳
                    '-f', 'wav',
                    '-y', temp_path
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
                
                if result.returncode != 0:
                    self.logger.warning(f"FFmpeg conversion failed: {result.stderr}")
                    return None
                
                # 读取PCM数据
                samples, sample_rate = self._read_wav_samples(temp_path)
                return samples, sample_rate
                
            finally:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                    
        except Exception as e:
            self.logger.error(f"Failed to extract audio samples: {str(e)}")
            return None
    
    def _read_wav_samples(self, wav_path: str) -> Tuple[List[float], int]:
        """读取WAV文件的样本数据"""
        try:
            import wave
            import struct
            
            with wave.open(wav_path, 'rb') as wav_file:
                frames = wav_file.getnframes()
                sample_rate = wav_file.getframerate()
                sample_width = wav_file.getsampwidth()
                
                # 读取所有帧
                raw_audio = wav_file.readframes(frames)
                
                # 根据样本宽度解析数据
                if sample_width == 1:
                    # 8位无符号
                    samples = [x / 128.0 - 1.0 for x in raw_audio]
                elif sample_width == 2:
                    # 16位有符号
                    samples = [x / 32768.0 for x in struct.unpack(f'<{frames}h', raw_audio)]
                elif sample_width == 4:
                    # 32位有符号
                    samples = [x / 2147483648.0 for x in struct.unpack(f'<{frames}i', raw_audio)]
                else:
                    raise AudioProcessingError(f"Unsupported sample width: {sample_width}")
                
                return samples, sample_rate
                
        except Exception as e:
            self.logger.error(f"Failed to read WAV samples: {str(e)}")
            return [], 22050
    
    def generate_waveform_data(self, audio_path: str, num_points: int = 150) -> List[float]:
        """生成波形数据"""
        try:
            # 提取音频样本
            samples_data = self.extract_audio_samples(audio_path)
            
            if not samples_data:
                return self._generate_placeholder_waveform(num_points)
            
            samples, sample_rate = samples_data
            
            if not samples:
                return self._generate_placeholder_waveform(num_points)
            
            # 计算每个点对应的样本数
            samples_per_point = len(samples) // num_points
            
            if samples_per_point < 1:
                samples_per_point = 1
            
            waveform_data = []
            
            for i in range(num_points):
                start_idx = i * samples_per_point
                end_idx = min(start_idx + samples_per_point, len(samples))
                
                if start_idx < len(samples):
                    # 计算这个区间的RMS值
                    chunk = samples[start_idx:end_idx]
                    if chunk:
                        rms = (sum(x * x for x in chunk) / len(chunk)) ** 0.5
                        waveform_data.append(rms)
                    else:
                        waveform_data.append(0.0)
                else:
                    waveform_data.append(0.0)
            
            # 归一化
            if waveform_data:
                max_val = max(waveform_data)
                if max_val > 0:
                    waveform_data = [val / max_val for val in waveform_data]
            
            self.logger.info(f"Generated waveform data: {len(waveform_data)} points")
            return waveform_data
            
        except Exception as e:
            self.logger.error(f"Failed to generate waveform data: {str(e)}")
            return self._generate_placeholder_waveform(num_points)
    
    def _generate_placeholder_waveform(self, num_points: int = 150) -> List[float]:
        """生成占位符波形数据"""
        import random
        import math
        
        # 生成伪随机但一致的波形
        random.seed(42)  # 固定种子确保一致性
        
        waveform_data = []
        
        for i in range(num_points):
            t = i / num_points
            
            # 基础正弦波
            base_wave = 0.3 * math.sin(2 * math.pi * t * 3)
            
            # 添加高频成分
            high_freq = 0.2 * math.sin(2 * math.pi * t * 12)
            
            # 添加随机噪声
            noise = 0.15 * (random.random() - 0.5)
            
            # 添加包络（音量变化）
            envelope = 0.5 + 0.3 * math.sin(2 * math.pi * t * 0.5)
            
            # 组合所有成分
            amplitude = abs((base_wave + high_freq + noise) * envelope)
            
            # 确保在合理范围内
            amplitude = max(0.05, min(1.0, amplitude))
            waveform_data.append(amplitude)
        
        self.logger.info(f"Generated placeholder waveform: {num_points} points")
        return waveform_data
    
    def is_audio_file(self, file_path: str) -> bool:
        """检查是否为音频文件"""
        audio_extensions = {'.mp3', '.wav', '.aac', '.m4a', '.flac', '.ogg', '.wma', '.opus'}
        return Path(file_path).suffix.lower() in audio_extensions
    
    def validate_audio_file(self, file_path: str) -> bool:
        """验证音频文件是否有效"""
        try:
            if not os.path.exists(file_path):
                return False
            
            if not self.is_audio_file(file_path):
                return False
            
            # 尝试获取时长来验证文件
            duration = self.get_audio_duration(file_path)
            return duration > 0
            
        except Exception:
            return False
