#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能转场选择矩阵
基于踩点转场技术方案实现智能转场效果选择和参数控制
"""

import cv2
import numpy as np
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import json
from core.logger import get_logger
from core.beat_detection_engine import BeatPoint, BeatAnalysisResult
from core.material_adaptation_engine import VideoSegment


class TransitionType(Enum):
    """转场类型枚举"""
    FADE = "fade"
    DISSOLVE = "dissolve"
    WIPE = "wipe"
    SLIDE = "slide"
    ZOOM = "zoom"
    ROTATE = "rotate"
    FLIP = "flip"
    MORPH = "morph"
    PARTICLE = "particle"
    LIGHT_SWEEP = "light_sweep"
    COLOR_BURST = "color_burst"
    SCISSOR_CUT = "scissor_cut"


class BeautyFilterType(Enum):
    """美颜滤镜类型"""
    SKIN_SMOOTH = "skin_smooth"
    FACE_SLIM = "face_slim"
    EYE_BRIGHTEN = "eye_brighten"
    TEETH_WHITEN = "teeth_whiten"
    HAIR_COLOR = "hair_color"
    GLOW_EFFECT = "glow_effect"


@dataclass
class TransitionConfig:
    """转场配置"""
    transition_type: TransitionType
    duration: float  # 转场时长（秒）
    intensity: float  # 强度 (0-1)
    direction: str  # 方向: LEFT, RIGHT, UP, DOWN, CENTER
    easing: str  # 缓动函数: LINEAR, EASE_IN, EASE_OUT, EASE_IN_OUT
    parameters: Dict[str, Any]  # 额外参数


@dataclass
class BeautyFilterConfig:
    """美颜滤镜配置"""
    filter_type: BeautyFilterType
    intensity: float  # 强度 (0-1)
    target_area: str  # 目标区域: FACE, EYES, MOUTH, HAIR, FULL
    blend_mode: str  # 混合模式: NORMAL, MULTIPLY, OVERLAY, SOFT_LIGHT
    parameters: Dict[str, Any]  # 额外参数


@dataclass
class TransitionPlan:
    """转场计划"""
    beat_index: int
    beat_type: str
    music_emotion: str
    segment_from: VideoSegment
    segment_to: VideoSegment
    transition_config: TransitionConfig
    beauty_filters: List[BeautyFilterConfig]
    sync_precision: float  # 同步精度要求


class IntelligentTransitionMatrix:
    """智能转场选择矩阵"""
    
    def __init__(self):
        self.logger = get_logger('transition_matrix')
        
        # 转场选择矩阵
        self.transition_matrix = self._build_transition_matrix()
        
        # 美颜滤镜矩阵
        self.beauty_matrix = self._build_beauty_matrix()
        
        # 参数控制规则
        self.parameter_rules = self._build_parameter_rules()
        
        self.logger.info("智能转场选择矩阵初始化完成")
    
    def _build_transition_matrix(self) -> Dict[str, Dict[str, List[TransitionType]]]:
        """构建转场选择矩阵"""
        return {
            # 节拍类型 -> 音乐情感 -> 推荐转场列表
            "STRONG": {
                "EXCITEMENT": [
                    TransitionType.LIGHT_SWEEP,
                    TransitionType.COLOR_BURST,
                    TransitionType.SCISSOR_CUT,
                    TransitionType.ZOOM
                ],
                "JOY": [
                    TransitionType.PARTICLE,
                    TransitionType.DISSOLVE,
                    TransitionType.ROTATE,
                    TransitionType.SLIDE
                ],
                "CALM": [
                    TransitionType.FADE,
                    TransitionType.DISSOLVE,
                    TransitionType.MORPH,
                    TransitionType.WIPE
                ]
            },
            "MEDIUM": {
                "EXCITEMENT": [
                    TransitionType.SLIDE,
                    TransitionType.FLIP,
                    TransitionType.ZOOM,
                    TransitionType.WIPE
                ],
                "JOY": [
                    TransitionType.DISSOLVE,
                    TransitionType.ROTATE,
                    TransitionType.MORPH,
                    TransitionType.FADE
                ],
                "CALM": [
                    TransitionType.FADE,
                    TransitionType.DISSOLVE,
                    TransitionType.WIPE,
                    TransitionType.SLIDE
                ]
            },
            "WEAK": {
                "EXCITEMENT": [
                    TransitionType.FADE,
                    TransitionType.SLIDE,
                    TransitionType.DISSOLVE,
                    TransitionType.WIPE
                ],
                "JOY": [
                    TransitionType.FADE,
                    TransitionType.DISSOLVE,
                    TransitionType.MORPH,
                    TransitionType.SLIDE
                ],
                "CALM": [
                    TransitionType.FADE,
                    TransitionType.DISSOLVE,
                    TransitionType.WIPE,
                    TransitionType.MORPH
                ]
            }
        }
    
    def _build_beauty_matrix(self) -> Dict[str, Dict[str, List[BeautyFilterType]]]:
        """构建美颜滤镜矩阵"""
        return {
            # 镜头类型 -> 节拍类型 -> 推荐滤镜列表
            "CLOSE_UP": {
                "STRONG": [
                    BeautyFilterType.SKIN_SMOOTH,
                    BeautyFilterType.FACE_SLIM,
                    BeautyFilterType.EYE_BRIGHTEN,
                    BeautyFilterType.GLOW_EFFECT
                ],
                "MEDIUM": [
                    BeautyFilterType.SKIN_SMOOTH,
                    BeautyFilterType.EYE_BRIGHTEN,
                    BeautyFilterType.TEETH_WHITEN
                ],
                "WEAK": [
                    BeautyFilterType.SKIN_SMOOTH,
                    BeautyFilterType.GLOW_EFFECT
                ]
            },
            "MEDIUM": {
                "STRONG": [
                    BeautyFilterType.SKIN_SMOOTH,
                    BeautyFilterType.HAIR_COLOR,
                    BeautyFilterType.GLOW_EFFECT
                ],
                "MEDIUM": [
                    BeautyFilterType.SKIN_SMOOTH,
                    BeautyFilterType.HAIR_COLOR
                ],
                "WEAK": [
                    BeautyFilterType.SKIN_SMOOTH
                ]
            },
            "WIDE": {
                "STRONG": [
                    BeautyFilterType.GLOW_EFFECT,
                    BeautyFilterType.HAIR_COLOR
                ],
                "MEDIUM": [
                    BeautyFilterType.HAIR_COLOR
                ],
                "WEAK": []
            },
            "ENVIRONMENT": {
                "STRONG": [
                    BeautyFilterType.GLOW_EFFECT
                ],
                "MEDIUM": [],
                "WEAK": []
            }
        }
    
    def _build_parameter_rules(self) -> Dict[str, Dict[str, Any]]:
        """构建参数控制规则"""
        return {
            "transition_duration": {
                "STRONG": {"min": 0.1, "max": 0.3, "default": 0.2},
                "MEDIUM": {"min": 0.2, "max": 0.5, "default": 0.3},
                "WEAK": {"min": 0.3, "max": 0.8, "default": 0.5}
            },
            "transition_intensity": {
                "EXCITEMENT": {"min": 0.7, "max": 1.0, "default": 0.9},
                "JOY": {"min": 0.5, "max": 0.8, "default": 0.7},
                "CALM": {"min": 0.3, "max": 0.6, "default": 0.5}
            },
            "beauty_intensity": {
                "CLOSE_UP": {"min": 0.6, "max": 1.0, "default": 0.8},
                "MEDIUM": {"min": 0.4, "max": 0.7, "default": 0.6},
                "WIDE": {"min": 0.2, "max": 0.5, "default": 0.4},
                "ENVIRONMENT": {"min": 0.0, "max": 0.3, "default": 0.2}
            }
        }
    
    def generate_transition_plan(self, beat_analysis: BeatAnalysisResult, 
                               segments: List[VideoSegment]) -> List[TransitionPlan]:
        """
        生成智能转场计划
        
        Args:
            beat_analysis: 节拍分析结果
            segments: 视频片段列表
            
        Returns:
            转场计划列表
        """
        try:
            self.logger.info("生成智能转场计划...")
            
            plans = []
            beat_points = beat_analysis.beat_points
            music_emotion = beat_analysis.music_emotion
            
            for i in range(len(beat_points) - 1):
                current_beat = beat_points[i]
                next_beat = beat_points[i + 1]
                
                # 获取对应的视频片段
                segment_from = self._get_segment_for_beat(segments, current_beat.timestamp)
                segment_to = self._get_segment_for_beat(segments, next_beat.timestamp)
                
                if segment_from and segment_to:
                    # 选择转场效果
                    transition_config = self._select_transition(
                        current_beat.beat_type, music_emotion, segment_from, segment_to
                    )
                    
                    # 选择美颜滤镜
                    beauty_filters = self._select_beauty_filters(
                        segment_from.segment_type, current_beat.beat_type, music_emotion
                    )
                    
                    # 计算同步精度要求
                    sync_precision = self._calculate_sync_precision(current_beat, beat_analysis)
                    
                    plan = TransitionPlan(
                        beat_index=i,
                        beat_type=current_beat.beat_type,
                        music_emotion=music_emotion,
                        segment_from=segment_from,
                        segment_to=segment_to,
                        transition_config=transition_config,
                        beauty_filters=beauty_filters,
                        sync_precision=sync_precision
                    )
                    
                    plans.append(plan)
            
            self.logger.info(f"转场计划生成完成: {len(plans)}个转场")
            return plans
            
        except Exception as e:
            self.logger.error(f"转场计划生成失败: {e}")
            return []
    
    def _get_segment_for_beat(self, segments: List[VideoSegment], timestamp: float) -> Optional[VideoSegment]:
        """获取指定时间戳对应的视频片段"""
        for segment in segments:
            if segment.start_time <= timestamp <= segment.end_time:
                return segment
        return None
    
    def _select_transition(self, beat_type: str, music_emotion: str, 
                         segment_from: VideoSegment, segment_to: VideoSegment) -> TransitionConfig:
        """选择转场效果"""
        try:
            # 从转场矩阵获取推荐转场
            recommended_transitions = self.transition_matrix.get(beat_type, {}).get(music_emotion, [])
            
            if not recommended_transitions:
                transition_type = TransitionType.FADE  # 默认淡入淡出
            else:
                # 根据片段特征进一步筛选
                transition_type = self._refine_transition_selection(
                    recommended_transitions, segment_from, segment_to
                )
            
            # 计算转场参数
            duration = self._calculate_transition_duration(beat_type, music_emotion)
            intensity = self._calculate_transition_intensity(music_emotion, segment_from, segment_to)
            direction = self._determine_transition_direction(segment_from, segment_to)
            easing = self._select_easing_function(beat_type, music_emotion)
            
            # 构建转场配置
            config = TransitionConfig(
                transition_type=transition_type,
                duration=duration,
                intensity=intensity,
                direction=direction,
                easing=easing,
                parameters=self._build_transition_parameters(transition_type, segment_from, segment_to)
            )
            
            return config
            
        except Exception as e:
            self.logger.error(f"转场选择失败: {e}")
            return TransitionConfig(
                transition_type=TransitionType.FADE,
                duration=0.5,
                intensity=0.5,
                direction="CENTER",
                easing="EASE_IN_OUT",
                parameters={}
            )
    
    def _refine_transition_selection(self, candidates: List[TransitionType], 
                                   segment_from: VideoSegment, segment_to: VideoSegment) -> TransitionType:
        """根据片段特征细化转场选择"""
        try:
            # 评分系统
            scored_transitions = []
            
            for transition in candidates:
                score = 0.0
                
                # 基于片段类型的适配性评分
                if transition in [TransitionType.ZOOM, TransitionType.MORPH]:
                    if segment_from.segment_type == "CLOSE_UP" or segment_to.segment_type == "CLOSE_UP":
                        score += 0.3
                
                if transition in [TransitionType.SLIDE, TransitionType.WIPE]:
                    if segment_from.motion_level == "HIGH" or segment_to.motion_level == "HIGH":
                        score += 0.2
                
                if transition in [TransitionType.FADE, TransitionType.DISSOLVE]:
                    if segment_from.quality_score > 0.7 and segment_to.quality_score > 0.7:
                        score += 0.2
                
                # 基于人脸数量的适配性
                if transition in [TransitionType.PARTICLE, TransitionType.LIGHT_SWEEP]:
                    if segment_from.face_count > 0 or segment_to.face_count > 0:
                        score += 0.2
                
                scored_transitions.append((transition, score))
            
            # 选择评分最高的转场
            scored_transitions.sort(key=lambda x: x[1], reverse=True)
            return scored_transitions[0][0] if scored_transitions else TransitionType.FADE
            
        except Exception as e:
            self.logger.error(f"转场细化选择失败: {e}")
            return candidates[0] if candidates else TransitionType.FADE
    
    def _calculate_transition_duration(self, beat_type: str, music_emotion: str) -> float:
        """计算转场时长"""
        try:
            rules = self.parameter_rules["transition_duration"].get(beat_type, {})
            base_duration = rules.get("default", 0.5)
            
            # 根据音乐情感调整
            emotion_multiplier = {
                "EXCITEMENT": 0.8,  # 快节奏，短转场
                "JOY": 1.0,         # 标准时长
                "CALM": 1.2         # 慢节奏，长转场
            }.get(music_emotion, 1.0)
            
            duration = base_duration * emotion_multiplier
            
            # 限制在合理范围内
            min_duration = rules.get("min", 0.1)
            max_duration = rules.get("max", 1.0)
            
            return float(np.clip(duration, min_duration, max_duration))
            
        except Exception as e:
            self.logger.error(f"转场时长计算失败: {e}")
            return 0.5
    
    def _calculate_transition_intensity(self, music_emotion: str, 
                                      segment_from: VideoSegment, segment_to: VideoSegment) -> float:
        """计算转场强度"""
        try:
            rules = self.parameter_rules["transition_intensity"].get(music_emotion, {})
            base_intensity = rules.get("default", 0.5)
            
            # 根据片段质量调整
            avg_quality = (segment_from.quality_score + segment_to.quality_score) / 2
            quality_multiplier = 0.8 + (avg_quality * 0.4)  # 0.8-1.2范围
            
            intensity = base_intensity * quality_multiplier
            
            # 限制在合理范围内
            min_intensity = rules.get("min", 0.0)
            max_intensity = rules.get("max", 1.0)
            
            return float(np.clip(intensity, min_intensity, max_intensity))
            
        except Exception as e:
            self.logger.error(f"转场强度计算失败: {e}")
            return 0.5
    
    def _determine_transition_direction(self, segment_from: VideoSegment, segment_to: VideoSegment) -> str:
        """确定转场方向"""
        try:
            # 基于片段类型确定方向
            if segment_from.segment_type == "WIDE" and segment_to.segment_type == "CLOSE_UP":
                return "CENTER"  # 从广角到特写，向中心
            elif segment_from.segment_type == "CLOSE_UP" and segment_to.segment_type == "WIDE":
                return "OUT"     # 从特写到广角，向外
            elif segment_from.motion_level == "HIGH":
                return "LEFT"    # 高运动，向左
            elif segment_to.motion_level == "HIGH":
                return "RIGHT"   # 目标高运动，向右
            else:
                return "CENTER"  # 默认居中
                
        except Exception as e:
            self.logger.error(f"转场方向确定失败: {e}")
            return "CENTER"
    
    def _select_easing_function(self, beat_type: str, music_emotion: str) -> str:
        """选择缓动函数"""
        try:
            if beat_type == "STRONG":
                if music_emotion == "EXCITEMENT":
                    return "EASE_IN"     # 强拍+兴奋：快进入
                else:
                    return "EASE_OUT"    # 强拍+其他：快退出
            elif beat_type == "WEAK":
                return "EASE_IN_OUT"     # 弱拍：平滑进出
            else:
                return "LINEAR"          # 中等拍：线性
                
        except Exception as e:
            self.logger.error(f"缓动函数选择失败: {e}")
            return "EASE_IN_OUT"
    
    def _build_transition_parameters(self, transition_type: TransitionType, 
                                   segment_from: VideoSegment, segment_to: VideoSegment) -> Dict[str, Any]:
        """构建转场参数"""
        try:
            parameters = {}
            
            if transition_type == TransitionType.ZOOM:
                parameters["zoom_factor"] = 1.2 if segment_to.segment_type == "CLOSE_UP" else 0.8
                parameters["center_x"] = 0.5
                parameters["center_y"] = 0.5
                
            elif transition_type == TransitionType.ROTATE:
                parameters["angle"] = 90 if segment_from.motion_level == "HIGH" else 45
                parameters["center_x"] = 0.5
                parameters["center_y"] = 0.5
                
            elif transition_type == TransitionType.PARTICLE:
                parameters["particle_count"] = 100 if segment_from.face_count > 0 else 50
                parameters["particle_size"] = 3
                parameters["particle_color"] = [255, 255, 255]
                
            elif transition_type == TransitionType.COLOR_BURST:
                parameters["burst_color"] = [255, 100, 100]  # 理发店主题色
                parameters["burst_intensity"] = 0.8
                parameters["burst_radius"] = 100
            
            return parameters
            
        except Exception as e:
            self.logger.error(f"转场参数构建失败: {e}")
            return {}
    
    def _select_beauty_filters(self, segment_type: str, beat_type: str, music_emotion: str) -> List[BeautyFilterConfig]:
        """选择美颜滤镜"""
        try:
            # 从美颜矩阵获取推荐滤镜
            recommended_filters = self.beauty_matrix.get(segment_type, {}).get(beat_type, [])
            
            beauty_configs = []
            
            for filter_type in recommended_filters:
                # 计算滤镜强度
                intensity = self._calculate_beauty_intensity(segment_type, filter_type, music_emotion)
                
                # 确定目标区域
                target_area = self._determine_beauty_target(filter_type, segment_type)
                
                # 选择混合模式
                blend_mode = self._select_beauty_blend_mode(filter_type, music_emotion)
                
                config = BeautyFilterConfig(
                    filter_type=filter_type,
                    intensity=intensity,
                    target_area=target_area,
                    blend_mode=blend_mode,
                    parameters=self._build_beauty_parameters(filter_type, segment_type)
                )
                
                beauty_configs.append(config)
            
            return beauty_configs
            
        except Exception as e:
            self.logger.error(f"美颜滤镜选择失败: {e}")
            return []
    
    def _calculate_beauty_intensity(self, segment_type: str, filter_type: BeautyFilterType, music_emotion: str) -> float:
        """计算美颜强度"""
        try:
            rules = self.parameter_rules["beauty_intensity"].get(segment_type, {})
            base_intensity = rules.get("default", 0.5)
            
            # 根据滤镜类型调整
            filter_multipliers = {
                BeautyFilterType.SKIN_SMOOTH: 1.0,
                BeautyFilterType.FACE_SLIM: 0.8,
                BeautyFilterType.EYE_BRIGHTEN: 1.2,
                BeautyFilterType.TEETH_WHITEN: 0.9,
                BeautyFilterType.HAIR_COLOR: 1.1,
                BeautyFilterType.GLOW_EFFECT: 0.7
            }
            
            multiplier = filter_multipliers.get(filter_type, 1.0)
            
            # 根据音乐情感调整
            emotion_multipliers = {
                "EXCITEMENT": 1.2,
                "JOY": 1.0,
                "CALM": 0.8
            }
            
            emotion_multiplier = emotion_multipliers.get(music_emotion, 1.0)
            
            intensity = base_intensity * multiplier * emotion_multiplier
            
            # 限制范围
            min_intensity = rules.get("min", 0.0)
            max_intensity = rules.get("max", 1.0)
            
            return float(np.clip(intensity, min_intensity, max_intensity))
            
        except Exception as e:
            self.logger.error(f"美颜强度计算失败: {e}")
            return 0.5
    
    def _determine_beauty_target(self, filter_type: BeautyFilterType, segment_type: str) -> str:
        """确定美颜目标区域"""
        target_map = {
            BeautyFilterType.SKIN_SMOOTH: "FACE",
            BeautyFilterType.FACE_SLIM: "FACE",
            BeautyFilterType.EYE_BRIGHTEN: "EYES",
            BeautyFilterType.TEETH_WHITEN: "MOUTH",
            BeautyFilterType.HAIR_COLOR: "HAIR",
            BeautyFilterType.GLOW_EFFECT: "FULL"
        }
        
        return target_map.get(filter_type, "FACE")
    
    def _select_beauty_blend_mode(self, filter_type: BeautyFilterType, music_emotion: str) -> str:
        """选择美颜混合模式"""
        if music_emotion == "EXCITEMENT":
            return "OVERLAY"  # 强烈效果
        elif music_emotion == "JOY":
            return "SOFT_LIGHT"  # 柔和效果
        else:
            return "NORMAL"  # 标准效果
    
    def _build_beauty_parameters(self, filter_type: BeautyFilterType, segment_type: str) -> Dict[str, Any]:
        """构建美颜参数"""
        try:
            parameters = {}
            
            if filter_type == BeautyFilterType.SKIN_SMOOTH:
                parameters["kernel_size"] = 15 if segment_type == "CLOSE_UP" else 9
                parameters["sigma"] = 2.0
                
            elif filter_type == BeautyFilterType.HAIR_COLOR:
                parameters["target_colors"] = ["red", "blue", "purple"]
                parameters["saturation_boost"] = 1.3
                
            elif filter_type == BeautyFilterType.GLOW_EFFECT:
                parameters["glow_radius"] = 20
                parameters["glow_intensity"] = 0.3
            
            return parameters
            
        except Exception as e:
            self.logger.error(f"美颜参数构建失败: {e}")
            return {}
    
    def _calculate_sync_precision(self, beat_point: BeatPoint, beat_analysis: BeatAnalysisResult) -> float:
        """计算同步精度要求"""
        try:
            # 基础精度要求
            base_precision = 0.8
            
            # 根据节拍置信度调整
            confidence_factor = beat_point.confidence
            
            # 根据节拍稳定性调整
            stability_factor = beat_analysis.tempo_stability
            
            # 根据节拍类型调整
            beat_type_factors = {
                "STRONG": 1.2,  # 强拍要求更高精度
                "MEDIUM": 1.0,
                "WEAK": 0.8
            }
            
            beat_factor = beat_type_factors.get(beat_point.beat_type, 1.0)
            
            precision = base_precision * confidence_factor * stability_factor * beat_factor
            
            return float(np.clip(precision, 0.5, 1.0))
            
        except Exception as e:
            self.logger.error(f"同步精度计算失败: {e}")
            return 0.8
