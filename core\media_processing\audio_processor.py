#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频处理模块
"""

import os
import subprocess
import json
import numpy as np
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass

from ..common.logger import get_logger
from ..common.exceptions import AudioProcessingError, BeatDetectionError, handle_exception


@dataclass
class AudioInfo:
    """音频信息"""
    duration: float
    sample_rate: int
    channels: int
    codec: str
    bitrate: Optional[int] = None


@dataclass
class BeatPoint:
    """节拍点"""
    timestamp: float
    confidence: float
    strength: float


class AudioProcessor:
    """音频处理器"""
    
    def __init__(self, config):
        self.config = config
        self.ffmpeg_path = config.get('ffmpeg.path', 'ffmpeg')
        self.ffprobe_path = config.get('ffprobe.path', 'ffprobe')
        self.logger = get_logger('audio_processor')
        
        # 检查依赖
        self._check_dependencies()
    
    def _check_dependencies(self):
        """检查音频处理依赖"""
        try:
            # 检查FFmpeg
            result = subprocess.run([self.ffmpeg_path, '-version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                raise AudioProcessingError("FFmpeg not found or not working")
            
            # 检查FFprobe
            result = subprocess.run([self.ffprobe_path, '-version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                raise AudioProcessingError("FFprobe not found or not working")
                
            self.logger.info("Audio processing dependencies checked successfully")
            
        except subprocess.TimeoutExpired:
            raise AudioProcessingError("Audio processing tools timeout")
        except Exception as e:
            raise AudioProcessingError(f"Failed to check audio dependencies: {str(e)}")
    
    @handle_exception
    def get_audio_info(self, file_path: str) -> AudioInfo:
        """获取音频文件信息"""
        try:
            cmd = [
                self.ffprobe_path,
                '-v', 'quiet',
                '-threads', '1',  # 强制单线程
                '-print_format', 'json',
                '-show_format',
                '-show_streams',
                str(file_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, 
                                  encoding='utf-8', errors='ignore', timeout=30)
            
            if result.returncode != 0:
                raise AudioProcessingError(f"Failed to get audio info: {result.stderr}")
            
            data = json.loads(result.stdout)
            
            # 查找音频流
            audio_stream = None
            for stream in data.get('streams', []):
                if stream.get('codec_type') == 'audio':
                    audio_stream = stream
                    break
            
            if not audio_stream:
                raise AudioProcessingError("No audio stream found")
            
            # 提取信息
            duration = float(data['format'].get('duration', 0))
            sample_rate = int(audio_stream.get('sample_rate', 0))
            channels = int(audio_stream.get('channels', 0))
            codec = audio_stream.get('codec_name', '')
            bitrate = audio_stream.get('bit_rate')
            if bitrate:
                bitrate = int(bitrate)
            
            return AudioInfo(
                duration=duration,
                sample_rate=sample_rate,
                channels=channels,
                codec=codec,
                bitrate=bitrate
            )
            
        except json.JSONDecodeError as e:
            raise AudioProcessingError(f"Failed to parse audio info: {str(e)}")
        except subprocess.TimeoutExpired:
            raise AudioProcessingError("Audio info extraction timeout")
    
    @handle_exception
    def extract_audio_from_video(self, video_path: str, output_path: str, 
                                start_time: float = 0, duration: Optional[float] = None) -> bool:
        """从视频中提取音频"""
        try:
            cmd = [
                self.ffmpeg_path,
                '-threads', '1',  # 强制单线程，避免多线程问题
                '-thread_type', 'slice',  # 使用slice线程类型
                '-i', str(video_path),
                '-vn',  # 不要视频
                '-acodec', 'pcm_s16le',  # 使用PCM格式便于分析
                '-ar', '44100',  # 标准采样率
                '-ac', '2',  # 立体声
                '-ss', str(start_time)
            ]
            
            if duration:
                cmd.extend(['-t', str(duration)])
            
            cmd.extend(['-y', str(output_path)])
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode != 0:
                raise AudioProcessingError(f"Failed to extract audio: {result.stderr}")
            
            self.logger.info(f"Audio extracted successfully: {output_path}")
            return True
            
        except subprocess.TimeoutExpired:
            raise AudioProcessingError("Audio extraction timeout")
    
    @handle_exception
    def detect_beats_simple(self, audio_path: str, bpm: Optional[int] = None) -> List[BeatPoint]:
        """简单的节拍检测（基于固定BPM）"""
        try:
            audio_info = self.get_audio_info(audio_path)
            
            # 如果没有指定BPM，使用默认值
            if bpm is None:
                bpm = 120  # 默认120 BPM
            
            beat_interval = 60.0 / bpm
            beat_points = []
            
            current_time = 0.0
            while current_time < audio_info.duration:
                beat_points.append(BeatPoint(
                    timestamp=current_time,
                    confidence=0.8,  # 固定置信度
                    strength=1.0     # 固定强度
                ))
                current_time += beat_interval
            
            self.logger.info(f"Detected {len(beat_points)} beat points (simple method)")
            return beat_points
            
        except Exception as e:
            raise BeatDetectionError(f"Simple beat detection failed: {str(e)}")
    
    @handle_exception
    def mix_audio_tracks(self, tracks: List[Dict[str, Any]], output_path: str) -> bool:
        """混合多个音频轨道"""
        try:
            if not tracks:
                raise AudioProcessingError("No audio tracks to mix")
            
            # 构建FFmpeg命令
            cmd = [self.ffmpeg_path]
            
            # 添加输入文件
            for track in tracks:
                cmd.extend(['-i', str(track['path'])])
            
            # 构建滤镜链
            filter_parts = []
            for i, track in enumerate(tracks):
                volume = track.get('volume', 1.0)
                start_time = track.get('start_time', 0.0)
                
                filter_part = f"[{i}:a]"
                
                # 音量调整
                if volume != 1.0:
                    filter_part += f"volume={volume},"
                
                # 延迟（如果需要）
                if start_time > 0:
                    filter_part += f"adelay={int(start_time * 1000)},"
                
                filter_part = filter_part.rstrip(',')
                filter_parts.append(filter_part)
            
            # 混合所有轨道
            filter_complex = ';'.join(filter_parts) + f";{''.join([f'[{i}]' for i in range(len(tracks))])}amix=inputs={len(tracks)}[out]"
            
            cmd.extend([
                '-filter_complex', filter_complex,
                '-map', '[out]',
                '-y', str(output_path)
            ])
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode != 0:
                raise AudioProcessingError(f"Failed to mix audio tracks: {result.stderr}")
            
            self.logger.info(f"Audio tracks mixed successfully: {output_path}")
            return True
            
        except subprocess.TimeoutExpired:
            raise AudioProcessingError("Audio mixing timeout")
    
    @handle_exception
    def apply_audio_effects(self, input_path: str, output_path: str, 
                           effects: Dict[str, Any]) -> bool:
        """应用音频效果"""
        try:
            cmd = [
                self.ffmpeg_path,
                '-threads', '1',  # 强制单线程
                '-thread_type', 'slice',  # 使用slice线程类型
                '-i', str(input_path)
            ]
            
            # 构建音频滤镜
            filters = []
            
            # 音量调整
            if 'volume' in effects:
                filters.append(f"volume={effects['volume']}")
            
            # 淡入效果
            if 'fade_in' in effects:
                filters.append(f"afade=t=in:ss=0:d={effects['fade_in']}")
            
            # 淡出效果
            if 'fade_out' in effects and 'duration' in effects:
                start_time = effects['duration'] - effects['fade_out']
                filters.append(f"afade=t=out:st={start_time}:d={effects['fade_out']}")
            
            # 均衡器
            if 'equalizer' in effects:
                eq = effects['equalizer']
                filters.append(f"equalizer=f={eq.get('frequency', 1000)}:width_type=h:width={eq.get('width', 100)}:g={eq.get('gain', 0)}")
            
            if filters:
                cmd.extend(['-af', ','.join(filters)])
            
            cmd.extend(['-y', str(output_path)])
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode != 0:
                raise AudioProcessingError(f"Failed to apply audio effects: {result.stderr}")
            
            self.logger.info(f"Audio effects applied successfully: {output_path}")
            return True
            
        except subprocess.TimeoutExpired:
            raise AudioProcessingError("Audio effects application timeout")

    @handle_exception
    def detect_beats_advanced(self, audio_path: str, hop_length: int = 512,
                             units: str = 'time') -> List[BeatPoint]:
        """使用librosa进行高级节拍检测"""
        try:
            # 尝试导入librosa
            try:
                import librosa
                import librosa.display
            except ImportError:
                self.logger.warning("librosa not available, falling back to simple beat detection")
                return self.detect_beats_simple(audio_path)

            self.logger.info(f"Starting advanced beat detection for: {audio_path}")

            # 加载音频文件
            y, sr = librosa.load(audio_path, sr=None)

            # 计算节拍跟踪
            tempo, beats = librosa.beat.beat_track(
                y=y,
                sr=sr,
                hop_length=hop_length,
                units=units
            )

            # 计算节拍强度
            onset_envelope = librosa.onset.onset_strength(y=y, sr=sr, hop_length=hop_length)

            beat_points = []
            for i, beat_time in enumerate(beats):
                # 计算置信度（基于onset强度）
                frame_idx = int(beat_time * sr / hop_length)
                if frame_idx < len(onset_envelope):
                    strength = onset_envelope[frame_idx]
                    confidence = min(1.0, strength / np.max(onset_envelope))
                else:
                    strength = 0.5
                    confidence = 0.5

                beat_points.append(BeatPoint(
                    timestamp=float(beat_time),
                    confidence=float(confidence),
                    strength=float(strength)
                ))

            self.logger.info(f"Detected {len(beat_points)} beats with tempo {tempo:.1f} BPM")
            return beat_points

        except Exception as e:
            self.logger.error(f"Advanced beat detection failed: {str(e)}")
            # 回退到简单检测
            return self.detect_beats_simple(audio_path)

    @handle_exception
    def analyze_audio_features(self, audio_path: str) -> Dict[str, Any]:
        """分析音频特征"""
        try:
            # 尝试导入librosa
            try:
                import librosa
                import librosa.feature
            except ImportError:
                self.logger.warning("librosa not available, returning basic features")
                audio_info = self.get_audio_info(audio_path)
                return {
                    'duration': audio_info.duration,
                    'sample_rate': audio_info.sample_rate,
                    'channels': audio_info.channels,
                    'tempo': 120.0,  # 默认值
                    'key': 'C',      # 默认值
                    'energy': 0.5    # 默认值
                }

            self.logger.info(f"Analyzing audio features for: {audio_path}")

            # 加载音频
            y, sr = librosa.load(audio_path, sr=None)

            # 基本信息
            duration = len(y) / sr

            # 节拍和节奏分析
            tempo, _ = librosa.beat.beat_track(y=y, sr=sr)

            # 音调分析
            chroma = librosa.feature.chroma_stft(y=y, sr=sr)
            key_profile = np.mean(chroma, axis=1)
            key_idx = np.argmax(key_profile)
            keys = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']
            estimated_key = keys[key_idx]

            # 能量分析
            rms = librosa.feature.rms(y=y)[0]
            energy = np.mean(rms)

            # 频谱质心（音色亮度）
            spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
            brightness = np.mean(spectral_centroids)

            # 零交叉率（音色粗糙度）
            zcr = librosa.feature.zero_crossing_rate(y)[0]
            roughness = np.mean(zcr)

            features = {
                'duration': float(duration),
                'sample_rate': int(sr),
                'tempo': float(tempo),
                'key': estimated_key,
                'energy': float(energy),
                'brightness': float(brightness),
                'roughness': float(roughness),
                'dynamic_range': float(np.max(rms) - np.min(rms))
            }

            self.logger.info(f"Audio features analyzed: tempo={tempo:.1f}, key={estimated_key}")
            return features

        except Exception as e:
            raise AudioProcessingError(f"Audio feature analysis failed: {str(e)}")

    @handle_exception
    def detect_music_segments(self, audio_path: str, segment_length: float = 4.0) -> List[Dict[str, Any]]:
        """检测音乐段落（如副歌、主歌等）"""
        try:
            # 尝试导入librosa
            try:
                import librosa
                import librosa.segment
            except ImportError:
                self.logger.warning("librosa not available, using simple segmentation")
                # 简单的等长分段
                audio_info = self.get_audio_info(audio_path)
                segments = []
                current_time = 0.0
                segment_id = 0

                while current_time < audio_info.duration:
                    end_time = min(current_time + segment_length, audio_info.duration)
                    segments.append({
                        'id': segment_id,
                        'start_time': current_time,
                        'end_time': end_time,
                        'duration': end_time - current_time,
                        'type': 'segment',
                        'confidence': 0.5
                    })
                    current_time = end_time
                    segment_id += 1

                return segments

            self.logger.info(f"Detecting music segments for: {audio_path}")

            # 加载音频
            y, sr = librosa.load(audio_path, sr=None)

            # 计算色度特征用于结构分析
            chroma = librosa.feature.chroma_stft(y=y, sr=sr, hop_length=512)

            # 使用结构分割算法
            bounds = librosa.segment.agglomerative(chroma, k=None)
            bound_times = librosa.frames_to_time(bounds, sr=sr, hop_length=512)

            segments = []
            for i in range(len(bound_times) - 1):
                start_time = float(bound_times[i])
                end_time = float(bound_times[i + 1])
                duration = end_time - start_time

                # 简单的段落类型分类（基于时长和位置）
                if i == 0:
                    segment_type = 'intro'
                elif i == len(bound_times) - 2:
                    segment_type = 'outro'
                elif duration > segment_length * 1.5:
                    segment_type = 'chorus'
                else:
                    segment_type = 'verse'

                segments.append({
                    'id': i,
                    'start_time': start_time,
                    'end_time': end_time,
                    'duration': duration,
                    'type': segment_type,
                    'confidence': 0.8  # 基于算法的置信度
                })

            self.logger.info(f"Detected {len(segments)} music segments")
            return segments

        except Exception as e:
            raise AudioProcessingError(f"Music segmentation failed: {str(e)}")

    @handle_exception
    def sync_to_beats(self, beat_points: List[BeatPoint], video_segments: List[Dict[str, Any]],
                     tolerance: float = 0.5) -> List[Dict[str, Any]]:
        """将视频片段同步到音乐节拍"""
        try:
            if not beat_points:
                self.logger.warning("No beat points available for synchronization")
                return video_segments

            synced_segments = []
            beat_times = [bp.timestamp for bp in beat_points]

            for segment in video_segments:
                original_start = segment.get('start_time', 0.0)
                original_duration = segment.get('duration', 0.0)

                # 找到最接近的节拍点
                closest_beat_idx = min(range(len(beat_times)),
                                     key=lambda i: abs(beat_times[i] - original_start))
                closest_beat_time = beat_times[closest_beat_idx]

                # 如果在容差范围内，同步到节拍点
                if abs(closest_beat_time - original_start) <= tolerance:
                    synced_segment = segment.copy()
                    synced_segment['start_time'] = closest_beat_time
                    synced_segment['synced_to_beat'] = True
                    synced_segment['beat_confidence'] = beat_points[closest_beat_idx].confidence

                    self.logger.debug(f"Synced segment to beat: {original_start:.2f}s -> {closest_beat_time:.2f}s")
                else:
                    synced_segment = segment.copy()
                    synced_segment['synced_to_beat'] = False

                    self.logger.debug(f"Segment not synced (too far from beat): {original_start:.2f}s")

                synced_segments.append(synced_segment)

            synced_count = sum(1 for s in synced_segments if s.get('synced_to_beat', False))
            self.logger.info(f"Synchronized {synced_count}/{len(video_segments)} segments to beats")

            return synced_segments

        except Exception as e:
            raise AudioProcessingError(f"Beat synchronization failed: {str(e)}")
