# 通用功能模块 (Common)

## 📋 模块概述

通用功能模块提供整个应用程序的基础设施服务，包括配置管理、日志系统、异常处理和增强功能管理。这些组件为其他模块提供统一的基础服务，确保系统的稳定性和可维护性。

## 📁 模块文件

### ⚙️ config.py
**功能**: 配置管理系统
- **Config类**: 应用程序配置管理器

**主要功能**:
- 配置文件加载和解析
- 环境变量支持
- 配置验证和默认值
- 动态配置更新
- 配置备份和恢复
- 多环境配置支持

**配置类别**:
- **应用程序配置**: 基本应用设置
- **媒体处理配置**: FFmpeg、编解码器设置
- **AI功能配置**: 模型路径、GPU设置
- **界面配置**: 主题、语言、布局
- **性能配置**: 缓存、并发、内存限制

### 📝 logger.py
**功能**: 日志系统
- **get_logger函数**: 获取日志记录器

**日志特性**:
- 多级别日志记录（DEBUG, INFO, WARNING, ERROR, CRITICAL）
- 多输出目标（控制台、文件、网络）
- 日志格式自定义
- 日志轮转和压缩
- 性能监控日志
- 错误追踪日志

**日志配置**:
```python
logging_config = {
    'version': 1,
    'formatters': {
        'detailed': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        }
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'detailed'
        },
        'file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'app.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5
        }
    }
}
```

### ⚠️ exceptions.py
**功能**: 异常处理系统
- 自定义异常类定义
- 异常处理装饰器
- 错误恢复机制

**异常类型**:
- **VideoProcessingError**: 视频处理相关错误
- **AudioProcessingError**: 音频处理相关错误
- **BeatDetectionError**: 节拍检测错误
- **FFmpegError**: FFmpeg执行错误
- **TemplateError**: 模板相关错误
- **ConfigurationError**: 配置错误

**异常处理装饰器**:
```python
@handle_exception
def risky_operation():
    # 可能出错的操作
    pass
```

### ✨ enhanced_features.py
**功能**: 增强功能管理器
- **EnhancedFeatureManager类**: 增强功能管理

**增强功能**:
- AI字母识别功能
- 自动踩点功能
- 智能视频分析
- 高级音频处理
- 批量处理优化

## 🚀 使用示例

### 配置管理
```python
from core.common import Config

# 初始化配置
config = Config()

# 获取配置值
ffmpeg_path = config.get('ffmpeg.path', 'ffmpeg')
max_threads = config.get('processing.max_threads', 4)

# 设置配置值
config.set('video.quality', 'high')
config.set('audio.bitrate', '192k')

# 保存配置
config.save()
```

### 日志记录
```python
from core.common import get_logger

# 获取日志记录器
logger = get_logger('video_processor')

# 记录不同级别的日志
logger.debug('开始处理视频文件')
logger.info('视频处理完成')
logger.warning('检测到低质量音频')
logger.error('视频处理失败')
logger.critical('系统资源不足')
```

### 异常处理
```python
from core.common import VideoProcessingError, handle_exception

# 抛出自定义异常
try:
    # 视频处理代码
    pass
except Exception as e:
    raise VideoProcessingError(f"视频处理失败: {e}")

# 使用异常处理装饰器
@handle_exception
def process_video(video_path):
    # 处理视频的代码
    pass
```

### 增强功能管理
```python
from core.common import EnhancedFeatureManager

# 初始化增强功能管理器
enhanced_manager = EnhancedFeatureManager()

# 检查功能可用性
if enhanced_manager.is_feature_available('ocr'):
    # 使用OCR功能
    text_result = enhanced_manager.extract_video_text(video_path)

if enhanced_manager.is_feature_available('auto_sync'):
    # 使用自动同步功能
    sync_result = enhanced_manager.auto_sync_video(video_path, audio_path)
```

## 🔧 配置文件结构

### 主配置文件 (config.json)
```json
{
    "app": {
        "name": "SWANKSALON Video Editor",
        "version": "1.0.0",
        "language": "zh-CN",
        "theme": "dark"
    },
    "ffmpeg": {
        "path": "ffmpeg",
        "threads": -1,
        "hardware_acceleration": "auto"
    },
    "processing": {
        "max_concurrent_tasks": 4,
        "temp_directory": "./temp",
        "cache_size": "1GB",
        "quality_preset": "high"
    },
    "ai_features": {
        "face_detection": {
            "enabled": true,
            "model_path": "./models/face_detection.model",
            "confidence_threshold": 0.8
        },
        "music_analysis": {
            "enabled": true,
            "sample_rate": 44100,
            "hop_length": 512
        }
    },
    "ui": {
        "window_size": [1400, 900],
        "auto_save_interval": 300,
        "recent_files_count": 10
    }
}
```

### 环境配置文件 (.env)
```env
# 开发环境配置
DEBUG=true
LOG_LEVEL=DEBUG
TEMP_DIR=./temp_dev

# 生产环境配置
# DEBUG=false
# LOG_LEVEL=INFO
# TEMP_DIR=/var/tmp/videoeditor
```

## 📊 日志分析

### 日志级别说明
- **DEBUG**: 详细的调试信息，仅在开发时使用
- **INFO**: 一般信息，记录程序正常运行状态
- **WARNING**: 警告信息，程序可以继续运行但需要注意
- **ERROR**: 错误信息，程序遇到错误但可以恢复
- **CRITICAL**: 严重错误，程序可能无法继续运行

### 日志文件组织
```
logs/
├── app.log              # 主应用日志
├── video_processing.log # 视频处理日志
├── audio_processing.log # 音频处理日志
├── ai_features.log      # AI功能日志
├── batch_processing.log # 批量处理日志
└── error.log           # 错误日志汇总
```

## ⚡ 性能监控

### 系统指标监控
```python
# 性能监控示例
performance_metrics = {
    'cpu_usage': 65.2,
    'memory_usage': '3.2GB',
    'disk_usage': '45%',
    'network_io': '15MB/s',
    'processing_speed': '2.1x realtime',
    'cache_hit_rate': 0.85,
    'error_rate': 0.02
}
```

### 自动性能调优
- **动态线程调整**: 根据CPU使用率调整线程数
- **内存管理**: 自动清理不必要的缓存
- **磁盘空间监控**: 自动清理临时文件
- **网络优化**: 智能下载和上传策略

## 🔍 故障诊断

### 常见问题诊断
1. **配置问题**: 配置文件格式错误、路径不存在
2. **依赖问题**: 缺少必要的库或工具
3. **权限问题**: 文件读写权限不足
4. **资源问题**: 内存不足、磁盘空间不足

### 诊断工具
```python
from core.common import SystemDiagnostics

# 系统诊断
diagnostics = SystemDiagnostics()

# 检查系统状态
system_status = diagnostics.check_system_health()
print(f"系统状态: {system_status}")

# 检查依赖
dependencies_status = diagnostics.check_dependencies()
print(f"依赖状态: {dependencies_status}")
```

## 🛡️ 安全性

### 配置安全
- 敏感信息加密存储
- 配置文件权限控制
- 环境变量安全管理

### 日志安全
- 敏感信息过滤
- 日志文件访问控制
- 日志传输加密

### 异常安全
- 异常信息脱敏
- 错误堆栈过滤
- 安全错误报告
