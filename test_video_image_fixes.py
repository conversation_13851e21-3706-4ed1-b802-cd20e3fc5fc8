#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试VideoImageBlock的修复
"""

import sys
from pathlib import Path
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont

def test_video_image_block():
    """测试VideoImageBlock的显示和交互"""
    
    try:
        from gui.components.video_image_block import VideoImageBlock
        from core.media_processing.video_processor import VideoProcessor
        
        # 创建测试窗口
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        window = QMainWindow()
        window.setWindowTitle("VideoImageBlock 修复测试")
        window.setGeometry(100, 100, 800, 600)
        
        central_widget = QWidget()
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("VideoImageBlock 修复测试")
        title.setFont(QFont("Arial", 16))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 说明
        info = QLabel(
            "测试内容:\n"
            "1. 图片显示 - 应该看到占位符图片\n"
            "2. 游标裁剪 - 鼠标悬停在边缘应该看到游标\n"
            "3. 拖动流畅 - 拖动应该不闪烁"
        )
        info.setWordWrap(True)
        layout.addWidget(info)
        
        # 创建测试容器
        test_container = QWidget()
        test_container.setMinimumHeight(200)
        test_container.setStyleSheet("background-color: #2A2A2A; border: 1px solid #555;")
        
        # 模拟数据
        media_items = [
            {
                'file_path': 'placeholder_test1.mp4',
                'name': '测试视频1',
                'duration': 10.0,
                'start_time': 0.0,
                'trim_start': 0.0,
                'is_placeholder': True
            },
            {
                'file_path': 'placeholder_test2.mp4', 
                'name': '测试视频2',
                'duration': 15.0,
                'start_time': 12.0,
                'trim_start': 0.0,
                'is_placeholder': True
            }
        ]
        
        class MockTimeline:
            def __init__(self):
                self.pixels_per_second = 100
                self.tracks = [{'type': 'video', 'media_files': []}]
        
        mock_timeline = MockTimeline()
        
        try:
            video_processor = VideoProcessor({})
        except:
            video_processor = None
        
        # 创建VideoImageBlock实例
        blocks = []
        for i, media_item in enumerate(media_items):
            block = VideoImageBlock(media_item, 0, i, mock_timeline, video_processor)
            block.setParent(test_container)
            block.setGeometry(i * 220 + 10, 10, 200, 64)
            block.show()
            blocks.append(block)
            print(f"✅ 创建VideoImageBlock {i+1}: {media_item['name']}")
        
        layout.addWidget(test_container)
        
        # 测试按钮
        button_layout = QHBoxLayout()
        
        def test_trim():
            """测试裁剪功能"""
            if blocks:
                block = blocks[0]
                block.left_trim_pos = 20
                block.right_trim_pos = 30
                if hasattr(block, 'trim_overlay'):
                    block.trim_overlay.update()
                print("🔧 应用测试裁剪")
        
        def reset_trim():
            """重置裁剪"""
            if blocks:
                block = blocks[0]
                block.left_trim_pos = 0
                block.right_trim_pos = 0
                if hasattr(block, 'trim_overlay'):
                    block.trim_overlay.update()
                print("🔄 重置裁剪")
        
        trim_btn = QPushButton("测试裁剪")
        trim_btn.clicked.connect(test_trim)
        button_layout.addWidget(trim_btn)
        
        reset_btn = QPushButton("重置裁剪")
        reset_btn.clicked.connect(reset_trim)
        button_layout.addWidget(reset_btn)
        
        layout.addLayout(button_layout)
        
        # 状态显示
        status_label = QLabel("状态: 组件已创建，测试图片显示、游标和拖动功能")
        layout.addWidget(status_label)
        
        central_widget.setLayout(layout)
        window.setCentralWidget(central_widget)
        
        # 显示窗口
        window.show()
        
        print("🎉 测试窗口已显示")
        print("📝 测试说明:")
        print("   1. 检查是否显示占位符图片")
        print("   2. 鼠标悬停在块的左右边缘，检查游标变化")
        print("   3. 拖动块，检查是否流畅无闪烁")
        print("   4. 点击'测试裁剪'按钮，检查游标显示")
        
        return window, blocks
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def main():
    """主函数"""
    print("🚀 VideoImageBlock 修复测试")
    print("=" * 50)
    
    window, blocks = test_video_image_block()
    
    if window and blocks:
        print("✅ 测试环境创建成功")
        
        # 运行应用
        app = QApplication.instance()
        if app:
            sys.exit(app.exec())
    else:
        print("❌ 测试环境创建失败")
        sys.exit(1)

if __name__ == '__main__':
    main()
