#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import <PERSON><PERSON><PERSON><PERSON>, QColor

class TrackLabel(QWidget):
    """轨道标签 - 包含实用图标按钮"""

    delete_requested = Signal(int)

    def __init__(self, name: str, track_type: str, track_index: int, timeline=None):
        super().__init__()
        self.track_type = track_type
        self.track_index = track_index
        self.timeline = timeline
        self.selected = False

        # 设置轨道标签样式 - 背景色#333333
        self.setStyleSheet("""
            TrackLabel {
                background-color: #333333;
                border: none;
            }
            TrackLabel:hover {
                background-color: #444444;
            }
        """)

        # 创建布局
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(2, 2, 2, 2)  # 进一步减少边距
        layout.setSpacing(1)

        # 轨道类型图标 - 只显示V/A
        if self.track_type == 'video':
            type_icon = "V"  # 视频图标
        else:
            type_icon = "A"  # 音频图标

        type_label = QLabel(type_icon)
        type_label.setStyleSheet("""
            QLabel {
                color: #F2ECFF;
                font-size: 16px;
                font-weight: bold;
                background-color: transparent;
                border: 1px solid #F2ECFF;
                border-radius: 3px;
                padding: 2px;
                min-width: 20px;
                text-align: center;
            }
        """)
        type_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(type_label)

        # 控制按钮区域
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(2)

        # 可见性切换按钮
        self.visibility_btn = QPushButton("👁")  # 保持emoji，如果不显示会在后面修复
        self.visibility_btn.setFixedSize(24, 24)
        self.visibility_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(242, 236, 255, 0.1);
                border: 1px solid rgba(242, 236, 255, 0.3);
                border-radius: 3px;
                color: #F2ECFF;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(242, 236, 255, 0.2);
                border: 1px solid rgba(242, 236, 255, 0.5);
            }
        """)
        self.visibility_btn.setToolTip("显示/隐藏轨道")
        self.visibility_btn.clicked.connect(self.toggle_visibility)
        controls_layout.addWidget(self.visibility_btn)

        # 锁定按钮
        self.lock_btn = QPushButton("L")  # 使用L表示Lock
        self.lock_btn.setFixedSize(24, 24)
        self.lock_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(242, 236, 255, 0.1);
                border: 1px solid rgba(242, 236, 255, 0.3);
                border-radius: 3px;
                color: #F2ECFF;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(242, 236, 255, 0.2);
                border: 1px solid rgba(242, 236, 255, 0.5);
            }
        """)
        self.lock_btn.setToolTip("锁定/解锁轨道")
        self.lock_btn.clicked.connect(self.toggle_lock)
        controls_layout.addWidget(self.lock_btn)

        # 🔧 修改：视频轨道和音频轨道都添加静音按钮
        if self.track_type in ['audio', 'video']:
            # 静音按钮
            self.mute_btn = QPushButton("🔊")  # 使用音量图标
            self.mute_btn.setFixedSize(24, 24)
            self.mute_btn.setStyleSheet("""
                QPushButton {
                    background-color: rgba(242, 236, 255, 0.1);
                    border: 1px solid rgba(242, 236, 255, 0.3);
                    border-radius: 3px;
                    color: #F2ECFF;
                    font-size: 12px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: rgba(242, 236, 255, 0.2);
                    border: 1px solid rgba(242, 236, 255, 0.5);
                }
            """)
            tooltip_text = "静音/取消静音视频音频" if self.track_type == 'video' else "静音/取消静音音频"
            self.mute_btn.setToolTip(tooltip_text)
            self.mute_btn.clicked.connect(self.toggle_mute)
            controls_layout.addWidget(self.mute_btn)

        controls_layout.addStretch()

        # 删除按钮
        self.delete_btn = QPushButton("X")  # 使用X表示删除
        self.delete_btn.setFixedSize(24, 24)
        self.delete_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(255, 107, 107, 0.1);
                border: 1px solid rgba(255, 107, 107, 0.3);
                border-radius: 3px;
                color: #FF6B6B;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(255, 107, 107, 0.2);
                border: 1px solid rgba(255, 107, 107, 0.5);
            }
        """)
        self.delete_btn.setToolTip("删除轨道")
        self.delete_btn.clicked.connect(self.on_delete_clicked)
        controls_layout.addWidget(self.delete_btn)

        layout.addLayout(controls_layout)

        # 状态变量
        self.is_visible = True
        self.is_locked = False
        self.is_muted = False

    def paintEvent(self, event):
        """绘制背景色"""
        painter = QPainter(self)
        # 强制绘制#333333背景色
        painter.fillRect(self.rect(), QColor(51, 51, 51))  # #333333
        super().paintEvent(event)

    def mousePressEvent(self, event):
        """鼠标点击选择轨道"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.toggle_selection()

    def toggle_selection(self):
        """切换选择状态"""
        self.selected = not self.selected
        self.update_style()

    def toggle_visibility(self):
        """切换轨道可见性"""
        self.is_visible = not self.is_visible
        if self.is_visible:
            self.visibility_btn.setText("👁")
            self.visibility_btn.setToolTip("隐藏轨道")
        else:
            self.visibility_btn.setText("🙈")
            self.visibility_btn.setToolTip("显示轨道")
        print(f"轨道 {self.track_index} 可见性: {self.is_visible}")

    def toggle_lock(self):
        """切换轨道锁定状态"""
        self.is_locked = not self.is_locked
        if self.is_locked:
            self.lock_btn.setText("🔒")
            self.lock_btn.setToolTip("解锁轨道")
        else:
            self.lock_btn.setText("🔓")
            self.lock_btn.setToolTip("锁定轨道")
        print(f"轨道 {self.track_index} 锁定状态: {self.is_locked}")

    def toggle_mute(self):
        """切换轨道静音状态"""
        if self.track_type in ['audio', 'video']:
            self.is_muted = not self.is_muted
            if self.is_muted:
                self.mute_btn.setText("🔇")
                tooltip_text = "取消静音视频音频" if self.track_type == 'video' else "取消静音音频"
                self.mute_btn.setToolTip(tooltip_text)
            else:
                self.mute_btn.setText("🔊")
                tooltip_text = "静音视频音频" if self.track_type == 'video' else "静音音频"
                self.mute_btn.setToolTip(tooltip_text)

            track_type_name = "视频" if self.track_type == 'video' else "音频"
            print(f"{track_type_name}轨道 {self.track_index} 静音状态: {self.is_muted}")

            # 🔧 新增：通知主窗口更新音频状态
            if self.timeline and hasattr(self.timeline, 'parent_window'):
                self.timeline.parent_window.on_track_mute_changed(self.track_index, self.track_type, self.is_muted)

    def on_delete_clicked(self):
        """删除按钮点击事件"""
        self.delete_requested.emit(self.track_index)

    def update_style(self):
        """更新样式"""
        if self.selected:
            self.setStyleSheet("""
                TrackLabel {
                    background-color: #555555;
                    border: 1px solid #00C896;
                }
            """)
        else:
            self.setStyleSheet("""
                TrackLabel {
                    background-color: #333333;
                    border: none;
                }
                TrackLabel:hover {
                    background-color: #444444;
                }
            """)
