#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能欢迎向导 - 让用户快速上手的人性化引导系统
"""

from PySide6.QtWidgets import (QWizard, QWizardPage, QVBoxLayout, QHBoxLayout,
                               QLabel, QPushButton, QRadioButton, QButtonGroup,
                               QTextEdit, QProgressBar, QGroupBox, QGridLayout,
                               QCheckBox, QComboBox, QSpinBox, QFileDialog,
                               QListWidget, QListWidgetItem, QMessageBox,
                               QFrame, QScrollArea, QWidget)
from PySide6.QtCore import Qt, QTimer, Signal, QThread
from PySide6.QtGui import QFont, QPixmap, QIcon, QMovie

from core.common.logger import get_logger


class WelcomeIntroPage(QWizardPage):
    """欢迎介绍页面"""
    
    def __init__(self):
        super().__init__()
        self.setTitle("🎬 欢迎使用 SWANKSALON 智能视频编辑器")
        self.setSubTitle("让我们用几分钟时间，帮您快速上手专业的理发店视频制作")
        
        layout = QVBoxLayout(self)
        
        # 欢迎动画区域
        welcome_frame = QFrame()
        welcome_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 15px;
                margin: 10px;
            }
        """)
        welcome_layout = QVBoxLayout(welcome_frame)
        
        # 大标题
        title_label = QLabel("🚀 智能理发店视频制作系统")
        title_font = QFont()
        title_font.setPointSize(24)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: white; margin: 20px;")
        welcome_layout.addWidget(title_label)
        
        # 功能亮点
        features_label = QLabel("""
        ✨ 一键批量处理客户视频
        🎵 智能音乐节拍同步
        📝 AI文字识别和字幕
        🎨 专业模板和效果
        ⚡ 自动化工作流程
        """)
        features_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        features_label.setStyleSheet("color: white; font-size: 16px; line-height: 1.8;")
        welcome_layout.addWidget(features_label)
        
        layout.addWidget(welcome_frame)
        
        # 使用场景说明
        scenario_group = QGroupBox("💼 适用场景")
        scenario_layout = QVBoxLayout(scenario_group)
        
        scenarios = [
            "🏪 理发店日常客户视频制作",
            "📱 社交媒体内容批量生成", 
            "🎬 美发过程记录和展示",
            "💎 高端沙龙品牌宣传",
            "📊 客户满意度展示视频"
        ]
        
        for scenario in scenarios:
            scenario_label = QLabel(scenario)
            scenario_label.setStyleSheet("font-size: 14px; margin: 5px; padding: 8px;")
            scenario_layout.addWidget(scenario_label)
        
        layout.addWidget(scenario_group)
        
        # 预计时间
        time_label = QLabel("⏱️ 预计设置时间：3-5分钟")
        time_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        time_label.setStyleSheet("font-size: 14px; color: #666; margin: 10px;")
        layout.addWidget(time_label)


class UserTypeSelectionPage(QWizardPage):
    """用户类型选择页面"""
    
    def __init__(self):
        super().__init__()
        self.setTitle("👤 告诉我们您的身份")
        self.setSubTitle("我们将根据您的身份提供个性化的设置建议")
        
        layout = QVBoxLayout(self)
        
        # 用户类型选择
        self.user_group = QButtonGroup()
        
        user_types = [
            ("salon_owner", "🏪 理发店老板", "我经营一家理发店，需要制作客户视频用于宣传"),
            ("hairstylist", "✂️ 发型师", "我是专业发型师，想要展示我的作品"),
            ("content_creator", "📱 内容创作者", "我制作美发相关的社交媒体内容"),
            ("marketing", "📢 营销人员", "我负责美发品牌的市场推广"),
            ("beginner", "🔰 新手用户", "我是视频编辑新手，需要简单易用的工具")
        ]
        
        for i, (value, title, description) in enumerate(user_types):
            user_frame = QFrame()
            user_frame.setStyleSheet("""
                QFrame {
                    border: 2px solid #ddd;
                    border-radius: 10px;
                    margin: 5px;
                    padding: 10px;
                }
                QFrame:hover {
                    border-color: #4CAF50;
                    background-color: #f8f9fa;
                }
            """)
            
            user_layout = QHBoxLayout(user_frame)
            
            radio = QRadioButton()
            radio.setProperty("user_type", value)
            self.user_group.addButton(radio, i)
            user_layout.addWidget(radio)
            
            content_layout = QVBoxLayout()
            title_label = QLabel(title)
            title_label.setStyleSheet("font-weight: bold; font-size: 16px;")
            content_layout.addWidget(title_label)
            
            desc_label = QLabel(description)
            desc_label.setStyleSheet("color: #666; font-size: 14px;")
            desc_label.setWordWrap(True)
            content_layout.addWidget(desc_label)
            
            user_layout.addLayout(content_layout)
            layout.addWidget(user_frame)
        
        # 默认选择第一个
        self.user_group.button(0).setChecked(True)
    
    def get_selected_user_type(self):
        """获取选中的用户类型"""
        checked_button = self.user_group.checkedButton()
        if checked_button:
            return checked_button.property("user_type")
        return "salon_owner"


class WorkflowPreferencePage(QWizardPage):
    """工作流程偏好页面"""
    
    def __init__(self):
        super().__init__()
        self.setTitle("⚙️ 工作流程偏好设置")
        self.setSubTitle("根据您的需求定制最适合的工作流程")
        
        layout = QVBoxLayout(self)
        
        # 自动化程度选择
        automation_group = QGroupBox("🤖 自动化程度")
        automation_layout = QVBoxLayout(automation_group)
        
        self.automation_group = QButtonGroup()
        automation_options = [
            ("full_auto", "🚀 完全自动化", "系统自动处理所有步骤，您只需要提供视频文件"),
            ("semi_auto", "⚖️ 半自动化", "系统处理大部分工作，您可以调整关键参数"),
            ("manual", "🎛️ 手动控制", "您完全控制每个处理步骤和参数")
        ]
        
        for i, (value, title, description) in enumerate(automation_options):
            radio = QRadioButton(f"{title}\n{description}")
            radio.setProperty("automation_level", value)
            radio.setStyleSheet("QRadioButton { margin: 10px; padding: 10px; }")
            self.automation_group.addButton(radio, i)
            automation_layout.addWidget(radio)
        
        # 默认选择半自动化
        self.automation_group.button(1).setChecked(True)
        layout.addWidget(automation_group)
        
        # 处理优先级
        priority_group = QGroupBox("🎯 处理优先级")
        priority_layout = QVBoxLayout(priority_group)
        
        self.priority_checkboxes = {}
        priorities = [
            ("speed", "⚡ 处理速度", "优先考虑快速完成批量处理"),
            ("quality", "💎 视频质量", "优先考虑最佳的视频输出质量"),
            ("automation", "🤖 自动化程度", "优先考虑减少手动操作"),
            ("customization", "🎨 个性化定制", "优先考虑客户专属的定制效果")
        ]
        
        for key, title, description in priorities:
            checkbox = QCheckBox(f"{title} - {description}")
            checkbox.setStyleSheet("QCheckBox { margin: 5px; padding: 5px; }")
            self.priority_checkboxes[key] = checkbox
            priority_layout.addWidget(checkbox)
        
        # 默认选择速度和自动化
        self.priority_checkboxes["speed"].setChecked(True)
        self.priority_checkboxes["automation"].setChecked(True)
        
        layout.addWidget(priority_group)
    
    def get_automation_level(self):
        """获取自动化程度"""
        checked_button = self.automation_group.checkedButton()
        if checked_button:
            return checked_button.property("automation_level")
        return "semi_auto"
    
    def get_priorities(self):
        """获取优先级设置"""
        return {key: checkbox.isChecked() 
                for key, checkbox in self.priority_checkboxes.items()}


class QuickSetupPage(QWizardPage):
    """快速设置页面"""
    
    def __init__(self):
        super().__init__()
        self.setTitle("🚀 快速设置")
        self.setSubTitle("让我们为您配置基本的工作环境")
        
        layout = QVBoxLayout(self)
        
        # 工作目录设置
        workspace_group = QGroupBox("📁 工作目录设置")
        workspace_layout = QGridLayout(workspace_group)
        
        workspace_layout.addWidget(QLabel("视频输入目录:"), 0, 0)
        self.input_dir_edit = QLabel("点击选择...")
        self.input_dir_edit.setStyleSheet("""
            QLabel {
                border: 1px solid #ccc;
                padding: 8px;
                background: white;
                border-radius: 4px;
            }
        """)
        self.input_dir_edit.mousePressEvent = self.select_input_dir
        workspace_layout.addWidget(self.input_dir_edit, 0, 1)
        
        workspace_layout.addWidget(QLabel("视频输出目录:"), 1, 0)
        self.output_dir_edit = QLabel("点击选择...")
        self.output_dir_edit.setStyleSheet("""
            QLabel {
                border: 1px solid #ccc;
                padding: 8px;
                background: white;
                border-radius: 4px;
            }
        """)
        self.output_dir_edit.mousePressEvent = self.select_output_dir
        workspace_layout.addWidget(self.output_dir_edit, 1, 1)
        
        layout.addWidget(workspace_group)
        
        # 默认模板选择
        template_group = QGroupBox("🎨 默认模板风格")
        template_layout = QVBoxLayout(template_group)
        
        self.template_combo = QComboBox()
        template_styles = [
            ("modern", "🌟 现代时尚风格"),
            ("elegant", "💎 优雅经典风格"),
            ("dynamic", "⚡ 动感活力风格"),
            ("minimal", "🎯 简约清新风格"),
            ("luxury", "👑 奢华高端风格")
        ]
        
        for value, title in template_styles:
            self.template_combo.addItem(title, value)
        
        template_layout.addWidget(self.template_combo)
        layout.addWidget(template_group)
        
        # 品牌信息
        brand_group = QGroupBox("🏪 品牌信息")
        brand_layout = QGridLayout(brand_group)
        
        brand_layout.addWidget(QLabel("店铺名称:"), 0, 0)
        self.shop_name_edit = QLabel("点击输入店铺名称...")
        self.shop_name_edit.setStyleSheet("""
            QLabel {
                border: 1px solid #ccc;
                padding: 8px;
                background: white;
                border-radius: 4px;
            }
        """)
        brand_layout.addWidget(self.shop_name_edit, 0, 1)
        
        brand_layout.addWidget(QLabel("联系方式:"), 1, 0)
        self.contact_edit = QLabel("点击输入联系方式...")
        self.contact_edit.setStyleSheet("""
            QLabel {
                border: 1px solid #ccc;
                padding: 8px;
                background: white;
                border-radius: 4px;
            }
        """)
        brand_layout.addWidget(self.contact_edit, 1, 1)
        
        layout.addWidget(brand_group)
    
    def select_input_dir(self, event):
        """选择输入目录"""
        from PySide6.QtWidgets import QFileDialog
        dir_path = QFileDialog.getExistingDirectory(self, "选择视频输入目录")
        if dir_path:
            self.input_dir_edit.setText(dir_path)
    
    def select_output_dir(self, event):
        """选择输出目录"""
        from PySide6.QtWidgets import QFileDialog
        dir_path = QFileDialog.getExistingDirectory(self, "选择视频输出目录")
        if dir_path:
            self.output_dir_edit.setText(dir_path)


class CompletionPage(QWizardPage):
    """完成页面"""
    
    def __init__(self):
        super().__init__()
        self.setTitle("🎉 设置完成")
        self.setSubTitle("您的智能视频编辑器已经准备就绪！")
        
        layout = QVBoxLayout(self)
        
        # 成功动画
        success_frame = QFrame()
        success_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #56ab2f, stop:1 #a8e6cf);
                border-radius: 15px;
                margin: 10px;
            }
        """)
        success_layout = QVBoxLayout(success_frame)
        
        success_label = QLabel("🎊 恭喜！设置已完成")
        success_font = QFont()
        success_font.setPointSize(20)
        success_font.setBold(True)
        success_label.setFont(success_font)
        success_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        success_label.setStyleSheet("color: white; margin: 20px;")
        success_layout.addWidget(success_label)
        
        layout.addWidget(success_frame)
        
        # 下一步建议
        next_steps_group = QGroupBox("🚀 建议的下一步操作")
        next_steps_layout = QVBoxLayout(next_steps_group)
        
        steps = [
            "1. 📁 准备您的客户视频文件（改造前、过程、改造后）",
            "2. 🎵 选择合适的背景音乐",
            "3. 🚀 使用智能批量处理功能开始制作",
            "4. 📱 将生成的视频分享到社交媒体"
        ]
        
        for step in steps:
            step_label = QLabel(step)
            step_label.setStyleSheet("font-size: 14px; margin: 8px; padding: 5px;")
            next_steps_layout.addWidget(step_label)
        
        layout.addWidget(next_steps_group)
        
        # 快速启动按钮
        quick_start_group = QGroupBox("⚡ 快速启动")
        quick_start_layout = QVBoxLayout(quick_start_group)
        
        self.demo_button = QPushButton("🎬 观看演示视频")
        self.demo_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 12px;
                font-size: 14px;
                border-radius: 6px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        quick_start_layout.addWidget(self.demo_button)
        
        self.tutorial_button = QPushButton("📚 查看使用教程")
        self.tutorial_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 12px;
                font-size: 14px;
                border-radius: 6px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        quick_start_layout.addWidget(self.tutorial_button)
        
        self.start_button = QPushButton("🚀 立即开始制作视频")
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 15px;
                font-size: 16px;
                font-weight: bold;
                border-radius: 8px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        quick_start_layout.addWidget(self.start_button)
        
        layout.addWidget(quick_start_group)


class SmartWelcomeWizard(QWizard):
    """智能欢迎向导"""
    
    # 信号
    wizard_completed = Signal(dict)  # 向导完成信号，传递配置数据
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger('welcome_wizard')
        
        self.setWindowTitle("🎬 SWANKSALON 智能设置向导")
        self.setWizardStyle(QWizard.WizardStyle.ModernStyle)
        self.setMinimumSize(800, 600)
        self.resize(900, 700)
        
        # 设置向导样式
        self.setStyleSheet("""
            QWizard {
                background-color: #f8f9fa;
            }
            QWizardPage {
                background-color: white;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        # 添加页面
        self.intro_page = WelcomeIntroPage()
        self.user_type_page = UserTypeSelectionPage()
        self.workflow_page = WorkflowPreferencePage()
        self.setup_page = QuickSetupPage()
        self.completion_page = CompletionPage()
        
        self.addPage(self.intro_page)
        self.addPage(self.user_type_page)
        self.addPage(self.workflow_page)
        self.addPage(self.setup_page)
        self.addPage(self.completion_page)
        
        # 连接信号
        self.finished.connect(self.on_wizard_finished)
        
        # 设置按钮文本
        self.setButtonText(QWizard.WizardButton.NextButton, "下一步 →")
        self.setButtonText(QWizard.WizardButton.BackButton, "← 上一步")
        self.setButtonText(QWizard.WizardButton.FinishButton, "完成设置 🎉")
        self.setButtonText(QWizard.WizardButton.CancelButton, "取消")
    
    def on_wizard_finished(self, result):
        """向导完成处理"""
        if result == QWizard.DialogCode.Accepted:
            # 收集所有配置数据
            config_data = {
                'user_type': self.user_type_page.get_selected_user_type(),
                'automation_level': self.workflow_page.get_automation_level(),
                'priorities': self.workflow_page.get_priorities(),
                'input_dir': self.setup_page.input_dir_edit.text(),
                'output_dir': self.setup_page.output_dir_edit.text(),
                'template_style': self.setup_page.template_combo.currentData(),
                'shop_name': self.setup_page.shop_name_edit.text(),
                'contact': self.setup_page.contact_edit.text()
            }
            
            self.wizard_completed.emit(config_data)
            self.logger.info("Welcome wizard completed successfully")
        else:
            self.logger.info("Welcome wizard cancelled")
    
    @staticmethod
    def should_show_wizard(config):
        """判断是否应该显示向导"""
        # 检查是否是首次运行或用户要求重新设置
        return not config.get('wizard_completed', False)
    
    @staticmethod
    def mark_wizard_completed(config):
        """标记向导已完成"""
        config.set('wizard_completed', True)
        config.save()
