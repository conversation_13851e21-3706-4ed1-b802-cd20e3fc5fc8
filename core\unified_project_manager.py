#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一项目参数管理器 - 数据驱动的视频编辑架构
"""

import threading
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from PySide6.QtCore import QObject, Signal
import uuid
from enum import Enum
from contextlib import contextmanager

# 导入全局常量管理器
from .unified_constants_manager import UnifiedConstantsManager

class RWLock:
    """读写锁实现"""
    def __init__(self):
        self._read_ready = threading.Condition(threading.RLock())
        self._readers = 0

    @contextmanager
    def r_locked(self):
        """读锁上下文管理器"""
        self.acquire_read()
        try:
            yield
        finally:
            self.release_read()

    @contextmanager
    def w_locked(self):
        """写锁上下文管理器"""
        self.acquire_write()
        try:
            yield
        finally:
            self.release_write()

    def acquire_read(self):
        """获取读锁"""
        self._read_ready.acquire()
        try:
            self._readers += 1
        finally:
            self._read_ready.release()

    def release_read(self):
        """释放读锁"""
        self._read_ready.acquire()
        try:
            self._readers -= 1
            if self._readers == 0:
                self._read_ready.notifyAll()
        finally:
            self._read_ready.release()

    def acquire_write(self):
        """获取写锁"""
        self._read_ready.acquire()
        while self._readers > 0:
            self._read_ready.wait()

    def release_write(self):
        """释放写锁"""
        self._read_ready.release()

class PlaybackState(Enum):
    """播放状态"""
    STOPPED = "stopped"
    PLAYING = "playing"
    PAUSED = "paused"

class TrackType(Enum):
    """轨道类型"""
    VIDEO = "video"
    AUDIO = "audio"

@dataclass
class MediaEffect:
    """媒体效果参数"""
    effect_id: str
    effect_type: str  # brightness, contrast, saturation, etc.
    value: float
    enabled: bool = True
    keyframes: Dict[float, float] = field(default_factory=dict)  # 时间点 -> 值

@dataclass
class MediaItem:
    """媒体素材项"""
    # 基础信息
    item_id: str
    file_path: str
    track_id: str
    
    # 时间信息
    timeline_start: float  # 在时间轴上的开始位置
    timeline_duration: float  # 在时间轴上的持续时间
    
    # 裁剪信息
    source_start: float = 0.0  # 源文件中的开始位置（裁剪起点）
    source_duration: float = 0.0  # 源文件的总时长
    trim_start: float = 0.0  # 裁剪的开始位置
    trim_end: float = 0.0  # 裁剪的结束位置
    
    # 效果参数
    effects: List[MediaEffect] = field(default_factory=list)
    
    # 显示属性
    opacity: float = 1.0
    volume: float = 1.0
    enabled: bool = True
    
    @property
    def timeline_end(self) -> float:
        """时间轴结束位置"""
        return self.timeline_start + self.timeline_duration
    
    @property
    def trimmed_duration(self) -> float:
        """裁剪后的实际时长"""
        return self.source_duration - self.trim_start - self.trim_end

@dataclass
class Track:
    """轨道"""
    track_id: str
    track_type: TrackType
    track_index: int  # 轨道顺序（从0开始）
    name: str
    
    # 轨道属性
    height: int = 64
    enabled: bool = True
    muted: bool = False
    solo: bool = False
    
    # 媒体项列表（按时间轴位置排序）
    media_items: List[MediaItem] = field(default_factory=list)
    
    def add_media_item(self, item: MediaItem):
        """添加媒体项并保持排序"""
        item.track_id = self.track_id
        self.media_items.append(item)
        self.media_items.sort(key=lambda x: x.timeline_start)
    
    def remove_media_item(self, item_id: str):
        """移除媒体项"""
        self.media_items = [item for item in self.media_items if item.item_id != item_id]
    
    def get_media_at_position(self, position: float) -> Optional[MediaItem]:
        """获取指定位置的媒体项"""
        for item in self.media_items:
            if item.timeline_start <= position < item.timeline_end:
                return item
        return None

@dataclass
class PreviewWindow:
    """预览窗口配置"""
    # 窗口位置和尺寸
    x: int = 0
    y: int = 0
    width: int = 1024
    height: int = 576
    
    # 裁剪区域（相对于原始视频）
    crop_x: float = 0.0
    crop_y: float = 0.0
    crop_width: float = 1.0  # 1.0 = 100%
    crop_height: float = 1.0
    
    # 显示比例
    aspect_ratio: str = "16:9"  # "16:9", "9:16", "1:1", "4:3"
    
    # 预览质量
    quality: str = "original"  # "original", "high", "smooth"

class UnifiedProjectManager(QObject):
    """
    统一项目参数管理器
    - 管理所有轨道、素材、播放状态
    - 提供线程安全的读写操作
    - 统一的播放控制接口
    """
    
    # 信号定义
    playback_state_changed = Signal(PlaybackState)
    playhead_position_changed = Signal(float)
    track_added = Signal(str)  # track_id
    track_removed = Signal(str)
    media_item_added = Signal(str, str)  # track_id, item_id
    media_item_removed = Signal(str, str)
    media_item_updated = Signal(str, str)
    project_duration_changed = Signal(float)
    
    def __init__(self):
        super().__init__()
        
        # 读写锁
        self._lock = RWLock()
        
        # 项目基础信息
        self.project_id = str(uuid.uuid4())
        self.project_name = "新项目"
        
        # 轨道管理
        self._tracks: Dict[str, Track] = {}
        self._track_order: List[str] = []  # 轨道显示顺序
        
        # 播放控制
        self._playback_state = PlaybackState.STOPPED
        self._playhead_position = 0.0
        self._playback_speed = 1.0
        
        # 预览窗口
        self.preview_window = PreviewWindow()
        
        # 项目设置
        self.frame_rate = 30.0
        self.sample_rate = 44100
        
        print(f"✅ 统一项目管理器初始化完成: {self.project_id}")
    
    # ==================== 读写锁装饰器 ====================
    
    def _read_lock(func):
        """读锁装饰器"""
        def wrapper(self, *args, **kwargs):
            with self._lock.r_locked():
                return func(self, *args, **kwargs)
        return wrapper
    
    def _write_lock(func):
        """写锁装饰器"""
        def wrapper(self, *args, **kwargs):
            with self._lock.w_locked():
                return func(self, *args, **kwargs)
        return wrapper
    
    # ==================== 轨道管理 ====================
    
    @_write_lock
    def add_track(self, track_type: TrackType, name: str = None) -> str:
        """添加轨道"""
        track_id = str(uuid.uuid4())
        
        if name is None:
            track_count = len([t for t in self._tracks.values() if t.track_type == track_type])
            name = f"{track_type.value.title()}{track_count + 1}"
        
        track = Track(
            track_id=track_id,
            track_type=track_type,
            track_index=len(self._tracks),
            name=name
        )
        
        self._tracks[track_id] = track
        self._track_order.append(track_id)
        
        self.track_added.emit(track_id)
        print(f"➕ 添加轨道: {name} ({track_id})")
        
        return track_id
    
    @_write_lock
    def remove_track(self, track_id: str) -> bool:
        """移除轨道"""
        if track_id not in self._tracks:
            return False
        
        # 移除轨道及其所有媒体项
        track = self._tracks[track_id]
        del self._tracks[track_id]
        self._track_order.remove(track_id)
        
        # 重新计算轨道索引
        for i, tid in enumerate(self._track_order):
            self._tracks[tid].track_index = i
        
        self.track_removed.emit(track_id)
        self._update_project_duration()
        
        print(f"➖ 移除轨道: {track.name} ({track_id})")
        return True
    
    @_read_lock
    def get_track(self, track_id: str) -> Optional[Track]:
        """获取轨道"""
        return self._tracks.get(track_id)
    
    @_read_lock
    def get_all_tracks(self) -> List[Track]:
        """获取所有轨道（按顺序）"""
        return [self._tracks[track_id] for track_id in self._track_order]
    
    @_read_lock
    def get_tracks_by_type(self, track_type: TrackType) -> List[Track]:
        """按类型获取轨道"""
        return [track for track in self._tracks.values() if track.track_type == track_type]
    
    # ==================== 媒体项管理 ====================
    
    @_write_lock
    def add_media_item(self, track_id: str, file_path: str, timeline_start: float, 
                      timeline_duration: float, source_duration: float = None) -> str:
        """添加媒体项"""
        if track_id not in self._tracks:
            raise ValueError(f"轨道不存在: {track_id}")
        
        item_id = str(uuid.uuid4())
        
        # 如果没有提供源时长，尝试从文件获取
        if source_duration is None:
            source_duration = timeline_duration  # 默认值
        
        media_item = MediaItem(
            item_id=item_id,
            file_path=file_path,
            track_id=track_id,
            timeline_start=timeline_start,
            timeline_duration=timeline_duration,
            source_duration=source_duration
        )
        
        track = self._tracks[track_id]
        track.add_media_item(media_item)
        
        self.media_item_added.emit(track_id, item_id)
        self._update_project_duration()
        
        print(f"➕ 添加媒体项: {file_path} -> {track.name}")
        return item_id
    
    @_write_lock
    def remove_media_item(self, track_id: str, item_id: str) -> bool:
        """移除媒体项"""
        if track_id not in self._tracks:
            return False
        
        track = self._tracks[track_id]
        track.remove_media_item(item_id)
        
        self.media_item_removed.emit(track_id, item_id)
        self._update_project_duration()
        
        print(f"➖ 移除媒体项: {item_id}")
        return True
    
    @_read_lock
    def get_media_item(self, track_id: str, item_id: str) -> Optional[MediaItem]:
        """获取媒体项"""
        if track_id not in self._tracks:
            return None
        
        track = self._tracks[track_id]
        for item in track.media_items:
            if item.item_id == item_id:
                return item
        return None
    
    @_read_lock
    def get_media_items_at_position(self, position: float) -> List[Tuple[Track, MediaItem]]:
        """获取指定位置的所有媒体项"""
        result = []
        for track in self._tracks.values():
            item = track.get_media_at_position(position)
            if item:
                result.append((track, item))
        return result
    
    # ==================== 播放控制 ====================
    
    @_read_lock
    def get_playback_state(self) -> PlaybackState:
        """获取播放状态"""
        return self._playback_state
    
    @_read_lock
    def get_playhead_position(self) -> float:
        """获取播放头位置"""
        return self._playhead_position
    
    @_write_lock
    def set_playhead_position(self, position: float):
        """设置播放头位置"""
        old_position = self._playhead_position
        self._playhead_position = max(0.0, min(position, self.get_project_duration()))
        
        if abs(old_position - self._playhead_position) > 0.001:
            self.playhead_position_changed.emit(self._playhead_position)
    
    @_write_lock
    def set_playback_state(self, state: PlaybackState):
        """设置播放状态"""
        if self._playback_state != state:
            self._playback_state = state
            self.playback_state_changed.emit(state)
    
    @_read_lock
    def get_playback_speed(self) -> float:
        """获取播放速度"""
        return self._playback_speed
    
    @_write_lock
    def set_playback_speed(self, speed: float):
        """设置播放速度"""
        self._playback_speed = max(0.1, min(speed, 4.0))
    
    # ==================== 项目信息 ====================
    
    @_read_lock
    def get_project_duration(self) -> float:
        """获取项目总时长"""
        max_end = 0.0
        for track in self._tracks.values():
            for item in track.media_items:
                max_end = max(max_end, item.timeline_end)
        return max_end
    
    def _update_project_duration(self):
        """更新项目时长并发射信号"""
        duration = self.get_project_duration()
        self.project_duration_changed.emit(duration)
    
    @_read_lock
    def get_track_count(self) -> int:
        """获取轨道数量"""
        return len(self._tracks)
    
    @_read_lock
    def get_media_count(self) -> int:
        """获取媒体项总数"""
        return sum(len(track.media_items) for track in self._tracks.values())
    
    # ==================== 初始化方法 ====================
    
    def initialize_default_project(self):
        """初始化默认项目"""
        with self._lock.w_locked():
            # 清空现有数据
            self._tracks.clear()
            self._track_order.clear()
            
            # 创建默认轨道
            video_track_id = self.add_track(TrackType.VIDEO, "视频轨道1")
            audio_track_id = self.add_track(TrackType.AUDIO, "音频轨道1")
            
            # 重置播放状态
            self._playback_state = PlaybackState.STOPPED
            self._playhead_position = 0.0
            self._playback_speed = 1.0
            
            print("🔄 项目已初始化为默认状态")
    
    def load_project_data(self, project_data: Dict[str, Any]):
        """从数据加载项目"""
        with self._lock.w_locked():
            # TODO: 实现项目数据加载逻辑
            pass
    
    def export_project_data(self) -> Dict[str, Any]:
        """导出项目数据"""
        with self._lock.r_locked():
            # TODO: 实现项目数据导出逻辑
            return {
                'project_id': self.project_id,
                'project_name': self.project_name,
                'tracks': {},  # 轨道数据
                'playhead_position': self._playhead_position,
                'preview_window': self.preview_window,
            }
    
    # ==================== 资源清理 ====================
    
    def cleanup(self):
        """清理资源"""
        with self._lock.w_locked():
            self._tracks.clear()
            self._track_order.clear()
            self._playback_state = PlaybackState.STOPPED
            print("🧹 项目管理器资源已清理")
