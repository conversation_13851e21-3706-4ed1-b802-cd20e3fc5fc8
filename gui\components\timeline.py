#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间轴组件
"""

import math
from typing import Op<PERSON>, Tuple, List

from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QScrollArea
from PySide6.QtCore import Qt, Signal, QTimer, QRect, QPoint
from PySide6.QtGui import QPainter, QPen, QBrush, QColor, QFont, QFontMetrics, QPolygon

class TimelineWidget(QWidget):
    """时间轴绘制组件"""
    
    position_changed = Signal(float)
    selection_changed = Signal(float, float)
    
    def __init__(self, video_processor=None):
        super().__init__()
        self.setMinimumHeight(150)
        self.setMouseTracking(True)
        
        # 视频信息
        self.video_info = None
        self.video_path = ""
        self.video_processor = video_processor
        
        # 时间轴参数
        self.zoom_factor = 1.0  # 缩放因子
        self.pixels_per_second = 50  # 每秒像素数
        self.current_position = 0.0  # 当前播放位置
        
        # 选择区域
        self.selection_start = -1.0
        self.selection_end = -1.0
        self.selecting = False
        
        # 拖拽状态
        self.dragging_playhead = False
        self.dragging_selection = False
        
        # 鼠标位置
        self.mouse_x = 0
        
        # 标尺高度
        self.ruler_height = 30
        
        # 缩略图缓存
        self._thumbnail_cache = {}
        
    def set_video_processor(self, video_processor):
        """设置视频处理器"""
        self.video_processor = video_processor
    
    def load_video(self, video_path: str, video_info):
        """加载视频"""
        self.video_path = video_path
        self.video_info = video_info
        self.current_position = 0.0
        self.selection_start = -1.0
        self.selection_end = -1.0
        
        # 调整组件大小
        if video_info:
            width = int(video_info.duration * self.pixels_per_second * self.zoom_factor)
            self.setMinimumWidth(max(width, 800))
        
        self.update()
    
    def set_position(self, position: float):
        """设置播放位置"""
        if self.video_info and 0 <= position <= self.video_info.duration:
            self.current_position = position
            self.update()
    
    def get_selection(self) -> Optional[Tuple[float, float]]:
        """获取选择区域"""
        if self.selection_start >= 0 and self.selection_end >= 0:
            return (min(self.selection_start, self.selection_end), 
                   max(self.selection_start, self.selection_end))
        return None
    
    def clear_selection(self):
        """清除选择"""
        self.selection_start = -1.0
        self.selection_end = -1.0
        self.update()
    
    def time_to_x(self, time: float) -> int:
        """时间转换为X坐标"""
        return int(time * self.pixels_per_second * self.zoom_factor)
    
    def x_to_time(self, x: int) -> float:
        """X坐标转换为时间"""
        return x / (self.pixels_per_second * self.zoom_factor)
    
    def paintEvent(self, event):
        """绘制事件"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 绘制背景
        painter.fillRect(self.rect(), QColor(60, 60, 60))
        
        if not self.video_info:
            painter.setPen(Qt.GlobalColor.white)
            painter.drawText(self.rect(), Qt.AlignmentFlag.AlignCenter, "无视频")
            painter.end()
            return
        
        # 绘制标尺
        self.draw_ruler(painter)
        
        # 绘制视频轨道
        self.draw_video_track(painter)
        
        # 绘制选择区域
        self.draw_selection(painter)
        
        # 绘制播放头
        self.draw_playhead(painter)

        # 结束绘制
        painter.end()
    
    def draw_ruler(self, painter):
        """绘制标尺"""
        painter.fillRect(0, 0, self.width(), self.ruler_height, QColor(80, 80, 80))
        
        painter.setPen(QPen(Qt.GlobalColor.white, 1))
        font = QFont("Arial", 8)
        painter.setFont(font)
        
        # 计算时间间隔
        if self.video_info and hasattr(self.video_info, 'duration'):
            duration = self.video_info.duration
        else:
            return
            
        interval = self.calculate_time_interval(duration)
        
        # 绘制时间刻度
        time = 0.0
        while time <= duration:
            x = self.time_to_x(time)
            if x > self.width():
                break
            
            # 绘制刻度线
            painter.drawLine(x, 0, x, self.ruler_height)
            
            # 绘制时间文本
            time_text = self.format_time(time)
            painter.drawText(x + 2, 12, time_text)
            
            time += interval
    
    def draw_video_track(self, painter):
        """绘制视频轨道"""
        if not self.video_info or not hasattr(self.video_info, 'duration'):
            return
            
        track_rect = QRect(0, self.ruler_height, 
                          self.time_to_x(self.video_info.duration), 
                          self.height() - self.ruler_height)
        
        # 绘制轨道背景
        painter.fillRect(track_rect, QColor(40, 40, 40))
        
        # 绘制视频块
        video_rect = QRect(0, self.ruler_height + 10,
                          self.time_to_x(self.video_info.duration),
                          self.height() - self.ruler_height - 20)
        
        # 如果有视频处理器，绘制缩略图；否则绘制单色块
        if self.video_processor and self.video_path:
            self.draw_video_thumbnails(painter, video_rect)
        else:
            painter.fillRect(video_rect, QColor(100, 150, 200))
            
            # 绘制视频信息
            painter.setPen(Qt.GlobalColor.white)
            painter.drawText(video_rect, Qt.AlignmentFlag.AlignCenter, 
                            f"视频片段 ({self.format_time(self.video_info.duration)})")
    
    def draw_video_thumbnails(self, painter, video_rect):
        """绘制视频缩略图"""
        if not self.video_info or not self.video_path or not self.video_processor:
            return
        
        # 计算缩略图参数
        track_width = video_rect.width()
        track_height = video_rect.height()
        duration = self.video_info.duration
        
        # 根据轨道宽度和缩放级别决定显示多少帧
        # 每个缩略图最小宽度为80像素，最大160像素
        min_thumb_width = 80
        max_thumb_width = 160
        
        # 计算理想的缩略图数量
        ideal_thumb_count = max(1, track_width // min_thumb_width)
        max_thumb_count = max(1, track_width // max_thumb_width)
        
        # 限制缩略图数量，避免太多
        thumb_count = min(ideal_thumb_count, max(max_thumb_count, 10))
        
        # 如果轨道太短，减少缩略图数量
        if track_width < 200:
            thumb_count = max(1, thumb_count // 2)
        
        # 计算每个缩略图的时间间隔
        time_interval = duration / thumb_count if thumb_count > 1 else 0
        
        # 计算时间戳
        timestamps = []
        for i in range(thumb_count):
            if thumb_count == 1:
                timestamp = duration / 2  # 单个缩略图显示中间帧
            else:
                timestamp = i * time_interval + time_interval / 2
            timestamps.append(min(timestamp, duration - 0.1))  # 确保不超过视频长度
        
        # 计算缩略图尺寸
        thumb_width = track_width // thumb_count
        thumb_height = min(track_height, int(thumb_width * 9 / 16))  # 保持16:9比例
        
        # 获取缩略图
        cache_key = f"{self.video_path}_{thumb_count}_{thumb_width}x{thumb_height}_{self.zoom_factor}"
        
        if cache_key not in self._thumbnail_cache:
            try:
                thumbnails = self.video_processor.get_thumbnail_frames(
                    self.video_path, timestamps, thumb_width, thumb_height
                )
                self._thumbnail_cache[cache_key] = thumbnails
            except Exception as e:
                print(f"获取缩略图失败: {e}")
                thumbnails = []
        else:
            thumbnails = self._thumbnail_cache[cache_key]
        
        # 绘制缩略图
        if thumbnails:
            for i, thumbnail in enumerate(thumbnails):
                if i >= thumb_count:
                    break
                
                thumb_x = video_rect.x() + i * thumb_width
                thumb_y = video_rect.y() + (track_height - thumb_height) // 2
                
                thumb_rect = QRect(thumb_x, thumb_y, thumb_width, thumb_height)
                
                # 绘制缩略图
                if not thumbnail.isNull():
                    painter.drawPixmap(thumb_rect, thumbnail)
                else:
                    # 如果缩略图无效，绘制占位符
                    painter.fillRect(thumb_rect, QColor(60, 60, 60))
                    painter.setPen(Qt.GlobalColor.white)
                    painter.drawText(thumb_rect, Qt.AlignmentFlag.AlignCenter, "...")
                
                # 绘制缩略图边框
                painter.setPen(QPen(QColor(80, 80, 80), 1))
                painter.drawRect(thumb_rect)
        else:
            # 如果没有缩略图，回退到单色显示
            painter.fillRect(video_rect, QColor(100, 150, 200))
            painter.setPen(Qt.GlobalColor.white)
            painter.drawText(video_rect, Qt.AlignmentFlag.AlignCenter, 
                            f"视频片段 ({self.format_time(self.video_info.duration)})")
    
    def clear_thumbnail_cache(self):
        """清除缩略图缓存"""
        self._thumbnail_cache.clear()
    
    def draw_selection(self, painter):
        """绘制选择区域"""
        if self.selection_start >= 0 and self.selection_end >= 0:
            start_x = self.time_to_x(min(self.selection_start, self.selection_end))
            end_x = self.time_to_x(max(self.selection_start, self.selection_end))
            
            # 绘制选择区域背景
            selection_rect = QRect(start_x, self.ruler_height,
                                 end_x - start_x, self.height() - self.ruler_height)
            
            painter.fillRect(selection_rect, QColor(255, 255, 0, 80))
            
            # 绘制选择区域边框
            painter.setPen(QPen(Qt.GlobalColor.yellow, 2))
            painter.drawRect(selection_rect)
    
    def draw_playhead(self, painter):
        """绘制播放头"""
        x = self.time_to_x(self.current_position)
        
        # 绘制播放头线
        painter.setPen(QPen(Qt.GlobalColor.red, 2))
        painter.drawLine(x, 0, x, self.height())
        
        # 绘制播放头三角形
        points = [
            QPoint(x - 5, 0),
            QPoint(x + 5, 0),
            QPoint(x, 10)
        ]
        polygon = QPolygon(points)
        
        painter.setBrush(QBrush(Qt.GlobalColor.red))
        painter.drawPolygon(polygon)
    
    def calculate_time_interval(self, duration: float) -> float:
        """计算时间间隔"""
        if duration <= 60:
            return 5.0  # 5秒
        elif duration <= 300:
            return 15.0  # 15秒
        elif duration <= 1800:
            return 60.0  # 1分钟
        else:
            return 300.0  # 5分钟
    
    def format_time(self, seconds: float) -> str:
        """格式化时间"""
        minutes = int(seconds // 60)
        secs = int(seconds % 60)
        return f"{minutes:02d}:{secs:02d}"
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if not self.video_info:
            return
        
        self.mouse_x = event.x()
        time = self.x_to_time(self.mouse_x)
        
        if event.button() == Qt.MouseButton.LeftButton:
            # 检查是否点击在播放头上
            playhead_x = self.time_to_x(self.current_position)
            if abs(self.mouse_x - playhead_x) <= 5:
                self.dragging_playhead = True
            else:
                # 开始选择
                self.selecting = True
                self.selection_start = time
                self.selection_end = time
                
                # 移动播放头
                self.current_position = max(0, min(time, self.video_info.duration))
                self.position_changed.emit(self.current_position)
        
        self.update()
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if not self.video_info:
            return
        
        self.mouse_x = event.x()
        time = self.x_to_time(self.mouse_x)
        
        if self.dragging_playhead:
            # 拖拽播放头
            self.current_position = max(0, min(time, self.video_info.duration))
            self.position_changed.emit(self.current_position)
        elif self.selecting:
            # 更新选择区域
            self.selection_end = max(0, min(time, self.video_info.duration))
        
        self.update()
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            if self.selecting:
                self.selecting = False
                if abs(self.selection_start - self.selection_end) > 0.1:  # 最小选择0.1秒
                    self.selection_changed.emit(self.selection_start, self.selection_end)
                else:
                    self.selection_start = -1.0
                    self.selection_end = -1.0
            
            self.dragging_playhead = False
        
        self.update()
    
    def keyPressEvent(self, event):
        """键盘按下事件"""
        if event.key() == Qt.Key.Key_Delete:
            self.clear_selection()
        elif event.key() == Qt.Key.Key_Escape:
            self.clear_selection()

class Timeline(QWidget):
    """时间轴容器"""
    
    position_changed = Signal(float)
    selection_changed = Signal(float, float)
    
    def __init__(self, video_processor=None):
        super().__init__()
        self.video_processor = video_processor
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setWidgetResizable(True)
        
        # 创建时间轴组件
        self.timeline_widget = TimelineWidget(self.video_processor)
        self.timeline_widget.position_changed.connect(self.position_changed)
        self.timeline_widget.selection_changed.connect(self.selection_changed)
        
        scroll_area.setWidget(self.timeline_widget)
        layout.addWidget(scroll_area)
    
    def load_video(self, video_path: str, video_info):
        """加载视频"""
        self.timeline_widget.load_video(video_path, video_info)
    
    def set_position(self, position: float):
        """设置播放位置"""
        self.timeline_widget.set_position(position)
    
    def get_selection(self) -> Optional[Tuple[float, float]]:
        """获取选择区域"""
        return self.timeline_widget.get_selection()
    
    def clear_selection(self):
        """清除选择"""
        self.timeline_widget.clear_selection() 