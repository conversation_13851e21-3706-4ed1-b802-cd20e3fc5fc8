#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试防重叠功能的简单脚本
"""

def test_overlap_detection():
    """测试重叠检测逻辑"""
    
    # 模拟轨道上的媒体文件
    track_media = [
        {'start_time': 0.0, 'duration': 5.0},    # 0-5秒
        {'start_time': 10.0, 'duration': 3.0},   # 10-13秒
        {'start_time': 20.0, 'duration': 4.0},   # 20-24秒
    ]
    
    def check_overlap(desired_time, duration, current_media=None):
        """检查重叠并返回合适位置"""
        desired_end = desired_time + duration
        
        for media in track_media:
            if media == current_media:
                continue  # 跳过自己
            
            media_start = media['start_time']
            media_end = media_start + media['duration']
            
            # 检查重叠
            if not (desired_end <= media_start or desired_time >= media_end):
                # 有重叠，找到最近的非重叠位置
                distance_to_start = abs(desired_time - media_start)
                distance_to_end = abs(desired_time - media_end)
                
                if distance_to_start < distance_to_end:
                    # 移动到该素材之前
                    return max(0, media_start - duration)
                else:
                    # 移动到该素材之后
                    return media_end
        
        # 没有重叠，返回原位置
        return max(0, desired_time)
    
    # 测试用例
    test_cases = [
        (2.0, 2.0),   # 期望2-4秒，与第一个素材重叠
        (8.0, 3.0),   # 期望8-11秒，与第二个素材重叠
        (6.0, 2.0),   # 期望6-8秒，不重叠
        (15.0, 3.0),  # 期望15-18秒，不重叠
        (22.0, 2.0),  # 期望22-24秒，与第三个素材重叠
    ]
    
    print("🔍 防重叠测试:")
    print("现有素材: 0-5s, 10-13s, 20-24s")
    print()
    
    for desired_time, duration in test_cases:
        result = check_overlap(desired_time, duration)
        desired_end = desired_time + duration
        result_end = result + duration
        
        print(f"期望位置: {desired_time:.1f}-{desired_end:.1f}s")
        print(f"实际位置: {result:.1f}-{result_end:.1f}s")
        
        if result != desired_time:
            print("✅ 检测到重叠，已调整位置")
        else:
            print("✅ 无重叠，保持原位置")
        print()

if __name__ == "__main__":
    test_overlap_detection()
