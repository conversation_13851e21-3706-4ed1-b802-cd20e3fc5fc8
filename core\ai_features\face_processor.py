#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人脸处理模块
专门用于SWANKSALON的人脸检测、美颜滤镜和发色增强
"""

import cv2
import numpy as np
from typing import Dict, List, Any, Tuple, Optional
from pathlib import Path
import json
from core.logger import get_logger
from core.exceptions import VideoProcessingError

try:
    import dlib
    DLIB_AVAILABLE = True
except ImportError:
    DLIB_AVAILABLE = False
    print("⚠️ dlib未安装，人脸检测功能将被禁用")


class FaceProcessor:
    """人脸处理器 - 专门用于SWANKSALON美颜和发色处理"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = get_logger('face_processor')
        
        # 初始化人脸检测器
        self._init_face_detector()
        
        # 美颜参数
        self.beauty_params = self.config.get('beauty_params', {
            'skin_smooth': 0.7,
            'face_slim': 0.5,
            'eye_brighten': 0.3,
            'teeth_whiten': 0.4,
            'hair_boost': ['red', 'blue', 'purple'],
            'min_face_confidence': 0.8
        })
        
        # 缓存
        self._face_cache = {}
        
        self.logger.info("人脸处理器初始化完成")
    
    def _init_face_detector(self):
        """初始化人脸检测器"""
        if not DLIB_AVAILABLE:
            self.face_detector = None
            self.face_predictor = None
            self.logger.warning("dlib不可用，将使用OpenCV人脸检测")
            
            # 使用OpenCV的Haar级联检测器作为备选
            try:
                self.cv_face_cascade = cv2.CascadeClassifier(
                    cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
                )
                self.logger.info("OpenCV人脸检测器加载成功")
            except Exception as e:
                self.logger.error(f"OpenCV人脸检测器加载失败: {e}")
                self.cv_face_cascade = None
            return
        
        try:
            self.face_detector = dlib.get_frontal_face_detector()
            
            # 尝试加载人脸关键点检测器
            predictor_path = "shape_predictor_68_face_landmarks.dat"
            if Path(predictor_path).exists():
                self.face_predictor = dlib.shape_predictor(predictor_path)
                self.logger.info("dlib人脸关键点检测器加载成功")
            else:
                self.face_predictor = None
                self.logger.warning("未找到dlib人脸关键点检测器文件")
                
        except Exception as e:
            self.logger.error(f"初始化dlib人脸检测器失败: {e}")
            self.face_detector = None
            self.face_predictor = None
    
    def detect_faces(self, frame: np.ndarray) -> List[Dict[str, Any]]:
        """
        检测人脸
        
        Args:
            frame: 输入图像帧
            
        Returns:
            人脸信息列表
        """
        try:
            faces = []
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            if self.face_detector:
                # 使用dlib检测
                dlib_faces = self.face_detector(gray)
                
                for face in dlib_faces:
                    face_info = {
                        'bbox': (face.left(), face.top(), face.right(), face.bottom()),
                        'confidence': 1.0,  # dlib不提供置信度
                        'landmarks': None
                    }
                    
                    # 获取关键点
                    if self.face_predictor:
                        landmarks = self.face_predictor(gray, face)
                        face_info['landmarks'] = [(landmarks.part(i).x, landmarks.part(i).y) 
                                                for i in range(68)]
                    
                    faces.append(face_info)
            
            elif hasattr(self, 'cv_face_cascade') and self.cv_face_cascade is not None:
                # 使用OpenCV检测
                cv_faces = self.cv_face_cascade.detectMultiScale(
                    gray, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30)
                )
                
                for (x, y, w, h) in cv_faces:
                    face_info = {
                        'bbox': (x, y, x + w, y + h),
                        'confidence': 0.8,  # 假设置信度
                        'landmarks': None
                    }
                    faces.append(face_info)
            
            return faces
            
        except Exception as e:
            self.logger.error(f"人脸检测失败: {e}")
            return []
    
    def apply_beauty_filter(self, frame: np.ndarray, intensity: float = 1.0) -> np.ndarray:
        """
        应用美颜滤镜
        
        Args:
            frame: 输入帧
            intensity: 美颜强度 (0.0-1.0)
            
        Returns:
            处理后的帧
        """
        try:
            if intensity <= 0:
                return frame
            
            result = frame.copy()
            faces = self.detect_faces(frame)
            
            if not faces:
                return frame
            
            for face_info in faces:
                bbox = face_info['bbox']
                landmarks = face_info.get('landmarks')
                
                # 应用各种美颜效果
                result = self._apply_skin_smoothing(result, bbox, intensity)
                
                if landmarks:
                    result = self._apply_face_slimming(result, landmarks, intensity)
                    result = self._apply_eye_brightening(result, landmarks, intensity)
                    result = self._apply_teeth_whitening(result, landmarks, intensity)
            
            return result
            
        except Exception as e:
            self.logger.error(f"美颜滤镜应用失败: {e}")
            return frame
    
    def _apply_skin_smoothing(self, frame: np.ndarray, bbox: Tuple[int, int, int, int], 
                            intensity: float) -> np.ndarray:
        """磨皮处理"""
        try:
            x1, y1, x2, y2 = bbox
            
            # 扩展人脸区域
            h, w = frame.shape[:2]
            margin = int((x2 - x1) * 0.2)
            x1 = max(0, x1 - margin)
            y1 = max(0, y1 - margin)
            x2 = min(w, x2 + margin)
            y2 = min(h, y2 + margin)
            
            # 提取人脸区域
            face_region = frame[y1:y2, x1:x2]
            
            if face_region.size == 0:
                return frame
            
            # 计算磨皮强度
            smooth_strength = self.beauty_params.get('skin_smooth', 0.7) * intensity
            
            # 高斯模糊磨皮
            kernel_size = max(3, int(15 * smooth_strength))
            if kernel_size % 2 == 0:
                kernel_size += 1
            
            smoothed = cv2.GaussianBlur(face_region, (kernel_size, kernel_size), 0)
            
            # 保留细节的磨皮
            # 使用双边滤波保持边缘
            bilateral_filtered = cv2.bilateralFilter(face_region, 9, 75, 75)
            
            # 混合原图和磨皮效果
            alpha = smooth_strength * 0.8
            blended = cv2.addWeighted(face_region, 1 - alpha, bilateral_filtered, alpha, 0)
            
            # 将处理后的区域放回原图
            result = frame.copy()
            result[y1:y2, x1:x2] = blended
            
            return result
            
        except Exception as e:
            self.logger.error(f"磨皮处理失败: {e}")
            return frame
    
    def _apply_face_slimming(self, frame: np.ndarray, landmarks: List[Tuple[int, int]], 
                           intensity: float) -> np.ndarray:
        """瘦脸处理"""
        try:
            if not landmarks or len(landmarks) < 68:
                return frame
            
            slim_strength = self.beauty_params.get('face_slim', 0.5) * intensity
            
            if slim_strength <= 0:
                return frame
            
            # 获取脸部轮廓关键点 (0-16)
            face_contour = landmarks[0:17]
            
            # 计算脸部中心
            center_x = np.mean([p[0] for p in face_contour])
            center_y = np.mean([p[1] for p in face_contour])
            
            # 简化的瘦脸算法：向内收缩脸颊区域
            h, w = frame.shape[:2]
            result = frame.copy()
            
            # 创建变形映射
            map_x = np.arange(w, dtype=np.float32)
            map_y = np.arange(h, dtype=np.float32)
            map_x, map_y = np.meshgrid(map_x, map_y)
            
            # 对脸颊区域进行收缩
            for i in [3, 4, 5, 11, 12, 13]:  # 脸颊关键点
                if i < len(landmarks):
                    px, py = landmarks[i]
                    
                    # 计算收缩向量
                    dx = center_x - px
                    dy = center_y - py
                    
                    # 应用局部变形
                    radius = 30  # 影响半径
                    mask = ((map_x - px) ** 2 + (map_y - py) ** 2) < radius ** 2
                    
                    factor = slim_strength * 0.3
                    map_x[mask] += dx * factor
                    map_y[mask] += dy * factor
            
            # 应用变形
            result = cv2.remap(frame, map_x, map_y, cv2.INTER_LINEAR)
            
            return result
            
        except Exception as e:
            self.logger.error(f"瘦脸处理失败: {e}")
            return frame
    
    def _apply_eye_brightening(self, frame: np.ndarray, landmarks: List[Tuple[int, int]], 
                             intensity: float) -> np.ndarray:
        """眼部提亮"""
        try:
            if not landmarks or len(landmarks) < 68:
                return frame
            
            brighten_strength = self.beauty_params.get('eye_brighten', 0.3) * intensity
            
            if brighten_strength <= 0:
                return frame
            
            result = frame.copy()
            
            # 左眼区域 (36-41) 和右眼区域 (42-47)
            left_eye = landmarks[36:42]
            right_eye = landmarks[42:48]
            
            for eye_points in [left_eye, right_eye]:
                if len(eye_points) >= 6:
                    # 创建眼部区域掩码
                    eye_center_x = int(np.mean([p[0] for p in eye_points]))
                    eye_center_y = int(np.mean([p[1] for p in eye_points]))
                    
                    # 提亮眼部区域
                    radius = 15
                    y1 = max(0, eye_center_y - radius)
                    y2 = min(frame.shape[0], eye_center_y + radius)
                    x1 = max(0, eye_center_x - radius)
                    x2 = min(frame.shape[1], eye_center_x + radius)
                    
                    eye_region = result[y1:y2, x1:x2]
                    
                    if eye_region.size > 0:
                        # 转换到HSV空间进行亮度调整
                        hsv = cv2.cvtColor(eye_region, cv2.COLOR_BGR2HSV)
                        hsv[:, :, 2] = np.clip(hsv[:, :, 2] * (1 + brighten_strength), 0, 255)
                        brightened = cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)
                        
                        result[y1:y2, x1:x2] = brightened
            
            return result
            
        except Exception as e:
            self.logger.error(f"眼部提亮失败: {e}")
            return frame
    
    def _apply_teeth_whitening(self, frame: np.ndarray, landmarks: List[Tuple[int, int]], 
                             intensity: float) -> np.ndarray:
        """牙齿美白"""
        try:
            if not landmarks or len(landmarks) < 68:
                return frame
            
            whiten_strength = self.beauty_params.get('teeth_whiten', 0.4) * intensity
            
            if whiten_strength <= 0:
                return frame
            
            result = frame.copy()
            
            # 嘴部区域 (48-67)
            mouth_points = landmarks[48:68]
            
            if len(mouth_points) >= 12:
                # 计算嘴部中心
                mouth_center_x = int(np.mean([p[0] for p in mouth_points]))
                mouth_center_y = int(np.mean([p[1] for p in mouth_points]))
                
                # 牙齿区域（嘴部内侧）
                radius = 8
                y1 = max(0, mouth_center_y - radius)
                y2 = min(frame.shape[0], mouth_center_y + radius)
                x1 = max(0, mouth_center_x - radius)
                x2 = min(frame.shape[1], mouth_center_x + radius)
                
                teeth_region = result[y1:y2, x1:x2]
                
                if teeth_region.size > 0:
                    # 检测白色区域（可能是牙齿）
                    gray = cv2.cvtColor(teeth_region, cv2.COLOR_BGR2GRAY)
                    _, teeth_mask = cv2.threshold(gray, 180, 255, cv2.THRESH_BINARY)
                    
                    # 美白处理
                    hsv = cv2.cvtColor(teeth_region, cv2.COLOR_BGR2HSV)
                    
                    # 增加亮度和降低饱和度
                    mask_3d = cv2.cvtColor(teeth_mask, cv2.COLOR_GRAY2BGR) / 255.0
                    hsv[:, :, 1] = hsv[:, :, 1] * (1 - mask_3d[:, :, 0] * whiten_strength * 0.3)  # 降低饱和度
                    hsv[:, :, 2] = np.clip(hsv[:, :, 2] * (1 + mask_3d[:, :, 0] * whiten_strength * 0.2), 0, 255)  # 增加亮度
                    
                    whitened = cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)
                    result[y1:y2, x1:x2] = whitened
            
            return result
            
        except Exception as e:
            self.logger.error(f"牙齿美白失败: {e}")
            return frame
    
    def enhance_hair_color(self, frame: np.ndarray, colors: List[str], intensity: float = 1.0) -> np.ndarray:
        """
        发色增强
        
        Args:
            frame: 输入帧
            colors: 要增强的颜色列表
            intensity: 增强强度
            
        Returns:
            处理后的帧
        """
        try:
            if intensity <= 0 or not colors:
                return frame
            
            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
            result_hsv = hsv.copy()
            
            for color in colors:
                mask = self._create_hair_color_mask(hsv, color)
                
                if np.any(mask):
                    # 增强饱和度
                    saturation_boost = 1.0 + intensity * 0.5
                    result_hsv[:, :, 1] = np.where(
                        mask > 0,
                        np.clip(result_hsv[:, :, 1] * saturation_boost, 0, 255),
                        result_hsv[:, :, 1]
                    )
                    
                    # 轻微调整色相以增强颜色
                    if color == "red":
                        hue_shift = 5
                    elif color == "blue":
                        hue_shift = -3
                    elif color == "purple":
                        hue_shift = 2
                    else:
                        hue_shift = 0
                    
                    if hue_shift != 0:
                        result_hsv[:, :, 0] = np.where(
                            mask > 0,
                            np.clip(result_hsv[:, :, 0] + hue_shift, 0, 179),
                            result_hsv[:, :, 0]
                        )
            
            return cv2.cvtColor(result_hsv, cv2.COLOR_HSV2BGR)
            
        except Exception as e:
            self.logger.error(f"发色增强失败: {e}")
            return frame
    
    def _create_hair_color_mask(self, hsv: np.ndarray, color: str) -> np.ndarray:
        """创建发色掩码"""
        try:
            if color == "red":
                # 红色范围
                lower1 = np.array([0, 50, 50])
                upper1 = np.array([10, 255, 255])
                lower2 = np.array([170, 50, 50])
                upper2 = np.array([180, 255, 255])
                
                mask1 = cv2.inRange(hsv, lower1, upper1)
                mask2 = cv2.inRange(hsv, lower2, upper2)
                return mask1 + mask2
                
            elif color == "blue":
                # 蓝色范围
                lower = np.array([100, 50, 50])
                upper = np.array([130, 255, 255])
                return cv2.inRange(hsv, lower, upper)
                
            elif color == "purple":
                # 紫色范围
                lower = np.array([130, 50, 50])
                upper = np.array([160, 255, 255])
                return cv2.inRange(hsv, lower, upper)
                
            elif color == "green":
                # 绿色范围
                lower = np.array([40, 50, 50])
                upper = np.array([80, 255, 255])
                return cv2.inRange(hsv, lower, upper)
                
            elif color == "yellow":
                # 黄色范围
                lower = np.array([20, 50, 50])
                upper = np.array([40, 255, 255])
                return cv2.inRange(hsv, lower, upper)
                
            else:
                return np.zeros(hsv.shape[:2], dtype=np.uint8)
                
        except Exception as e:
            self.logger.error(f"创建发色掩码失败: {e}")
            return np.zeros(hsv.shape[:2], dtype=np.uint8)
    
    def analyze_face_quality(self, frame: np.ndarray) -> Dict[str, Any]:
        """
        分析人脸质量
        
        Args:
            frame: 输入帧
            
        Returns:
            人脸质量分析结果
        """
        try:
            faces = self.detect_faces(frame)
            
            if not faces:
                return {
                    'has_face': False,
                    'face_count': 0,
                    'quality_score': 0.0,
                    'recommendations': ['未检测到人脸']
                }
            
            # 分析第一个人脸
            face = faces[0]
            bbox = face['bbox']
            
            # 计算人脸大小
            face_width = bbox[2] - bbox[0]
            face_height = bbox[3] - bbox[1]
            face_area = face_width * face_height
            frame_area = frame.shape[0] * frame.shape[1]
            face_ratio = face_area / frame_area
            
            # 计算人脸位置（是否居中）
            face_center_x = (bbox[0] + bbox[2]) / 2
            face_center_y = (bbox[1] + bbox[3]) / 2
            frame_center_x = frame.shape[1] / 2
            frame_center_y = frame.shape[0] / 2
            
            center_distance = np.sqrt(
                (face_center_x - frame_center_x) ** 2 + 
                (face_center_y - frame_center_y) ** 2
            )
            max_distance = np.sqrt(frame_center_x ** 2 + frame_center_y ** 2)
            center_score = 1.0 - (center_distance / max_distance)
            
            # 计算清晰度（基于边缘检测）
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            face_region = gray[bbox[1]:bbox[3], bbox[0]:bbox[2]]
            
            if face_region.size > 0:
                laplacian_var = cv2.Laplacian(face_region, cv2.CV_64F).var()
                sharpness_score = min(1.0, laplacian_var / 1000.0)
            else:
                sharpness_score = 0.0
            
            # 综合质量评分
            size_score = min(1.0, face_ratio * 10)  # 人脸占比
            quality_score = (size_score * 0.4 + center_score * 0.3 + sharpness_score * 0.3)
            
            # 生成建议
            recommendations = []
            if size_score < 0.3:
                recommendations.append("人脸过小，建议放大")
            if center_score < 0.5:
                recommendations.append("人脸不够居中")
            if sharpness_score < 0.3:
                recommendations.append("图像不够清晰")
            
            if not recommendations:
                recommendations.append("人脸质量良好")
            
            return {
                'has_face': True,
                'face_count': len(faces),
                'quality_score': float(quality_score),
                'size_score': float(size_score),
                'center_score': float(center_score),
                'sharpness_score': float(sharpness_score),
                'face_ratio': float(face_ratio),
                'recommendations': recommendations
            }
            
        except Exception as e:
            self.logger.error(f"人脸质量分析失败: {e}")
            return {
                'has_face': False,
                'face_count': 0,
                'quality_score': 0.0,
                'recommendations': ['分析失败']
            }
