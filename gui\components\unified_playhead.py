"""
统一播放头系统 - 一根完整贯穿时间轴和所有轨道的播放头线
"""

from PySide6.QtWidgets import QWidget
from PySide6.QtGui import QPainter, QPen, QBrush, QColor, QPolygon
from PySide6.QtCore import Qt, QPoint


class UnifiedPlayheadOverlay(QWidget):
    """统一播放头覆盖层 - 绘制一根完整贯穿时间轴和所有轨道的播放头线"""

    def __init__(self, parent=None, global_params=None):
        super().__init__(parent)

        # 设置为透明鼠标事件，不阻挡底层组件的交互
        self.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground, True)
        self.setAutoFillBackground(False)

        # 使用全局参数管理器
        if global_params is None:
            # 如果没有提供全局参数，创建一个临时的（向后兼容）
            from gui.main.multi_track_timeline import TimelineGlobalParams
            self.global_params = TimelineGlobalParams()
            self._owns_global_params = True
        else:
            self.global_params = global_params
            self._owns_global_params = False

        # 时间轴区域参数
        self.timeline_start_x = self.global_params.track_label_width
        self.timeline_width = self.global_params.track_content_width
        self.ruler_height = self.global_params.ruler_height

        # 轨道区域参数
        self.control_bar_height = self.global_params.ruler_height
        self.track_start_y = self.global_params.ruler_height
        self.track_count = 0         # 轨道数量

        # 播放头样式
        self.playhead_color = QColor(0, 200, 150)  # #00c896
        self.playhead_width = 3

        # 连接全局参数变化信号
        self.global_params.position_changed.connect(self.on_position_changed)
        self.global_params.zoom_changed.connect(self.on_zoom_changed)
        self.global_params.duration_changed.connect(self.on_duration_changed)
        self.global_params.track_count_changed.connect(self.on_track_count_changed)

        # 确保在最顶层
        self.raise_()

        print(f"🎯 UnifiedPlayheadOverlay 初始化完成，使用全局参数管理器")

    def on_position_changed(self, position: float):
        """响应全局位置变化"""
        self.update()

    def on_zoom_changed(self, pixels_per_second: float):
        """响应全局缩放变化"""
        self.update()

    def on_duration_changed(self, duration: float):
        """响应全局时长变化"""
        self.update()

    def on_track_count_changed(self, count: int):
        """响应全局轨道数量变化"""
        self.track_count = count
        self.update()

    def set_position(self, position: float):
        """设置播放头位置（时间，秒） - 委托给全局参数管理器"""
        self.global_params.set_current_position(position)

    def set_timeline_params(self, start_x: int, width: int, duration: float, pixels_per_second: float):
        """设置时间轴参数 - 委托给全局参数管理器"""
        self.timeline_start_x = start_x
        self.timeline_width = width
        self.global_params.set_total_duration(duration, emit_signal=False)
        self.global_params.set_pixels_per_second(pixels_per_second, emit_signal=False)
        self.update()

    def set_track_params(self, track_count: int, track_height: int = 64, track_spacing: int = 12):
        """设置轨道参数"""
        self.track_count = track_count
        # 轨道高度和间距由全局参数管理，这里只更新数量
        self.update()

    def set_control_bar_height(self, height: int):
        """设置控制栏高度"""
        self.control_bar_height = height
        self.update()

    def paintEvent(self, event):
        """绘制播放头 - 从时间轴刻度开始贯穿到最底部轨道的完整线条"""
        if self.global_params.current_position < 0:
            return

        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 🔧 修复：计算播放头X位置时考虑滚动偏移
        # 播放头在时间轴内容中的绝对位置
        timeline_x = self.global_params.time_to_pixels(self.global_params.current_position)

        # 获取滚动偏移量
        scroll_offset = 0
        if hasattr(self.parent(), 'scroll_area') and self.parent().scroll_area:
            scroll_bar = self.parent().scroll_area.horizontalScrollBar()
            if scroll_bar:
                scroll_offset = scroll_bar.value()

        # 播放头在窗口中的实际显示位置 = 轨道标签宽度 + 时间轴位置 - 滚动偏移
        x_pos = self.timeline_start_x + timeline_x - scroll_offset

        # 🔧 修复：基于组件可见区域和滚动状态来判断是否显示
        # 获取组件的可见区域
        visible_rect = self.visibleRegion().boundingRect()
        if visible_rect.isEmpty():
            visible_rect = self.rect()  # 如果没有可见区域，使用整个组件区域

        # 检查播放头是否在可见区域内（允许一定的边界容差）
        margin = 20  # 减小边界容差，避免过度绘制
        if not (visible_rect.left() - margin <= x_pos <= visible_rect.right() + margin):
            return

        # 设置播放头颜色和宽度
        pen = QPen(self.playhead_color, self.playhead_width)
        painter.setPen(pen)

        # 计算绘制起点：从控制栏下方的时间轴刻度开始
        start_y = self.control_bar_height  # 从控制栏下方开始（时间轴刻度区域顶部）

        # 🔧 修复：使用实际组件高度而不是计算值
        # 使用覆盖层的实际高度作为终点，确保占满整个空间
        end_y = self.height()  # 使用覆盖层的实际高度

        # 🔧 修复：绘制三角形指示器，确保线条从三角形底部开始
        # 先绘制三角形指示器（在时间轴刻度区域）
        painter.setBrush(QBrush(self.playhead_color))
        painter.setPen(QPen(self.playhead_color, 1))

        # 三角形指向下方，位置在时间轴刻度区域顶部
        triangle_y = start_y
        triangle_height = 12
        points = [
            QPoint(x_pos - 6, triangle_y),
            QPoint(x_pos + 6, triangle_y),
            QPoint(x_pos, triangle_y + triangle_height)
        ]
        polygon = QPolygon(points)
        painter.drawPolygon(polygon)

        # 🔧 修复：播放头线从三角形底部开始，避免超出
        line_start_y = triangle_y + triangle_height

        # 绘制完整的播放头线 - 从三角形底部到组件底部
        painter.setPen(pen)  # 重新设置线条样式
        painter.drawLine(x_pos, line_start_y, x_pos, end_y)


class UnifiedPlayheadManager:
    """统一播放头管理器 - 管理一根完整贯穿时间轴和所有轨道的播放头线"""

    def __init__(self, parent_widget, global_params=None):
        self.parent_widget = parent_widget

        # 获取全局参数管理器
        if global_params is None and hasattr(parent_widget, 'global_params'):
            global_params = parent_widget.global_params

        # 创建统一播放头覆盖层
        self.playhead_overlay = UnifiedPlayheadOverlay(parent_widget, global_params)

        # 设置覆盖层几何 - 覆盖整个父组件
        self.playhead_overlay.setGeometry(parent_widget.rect())

        # 🔧 修复：连接滚动事件，确保播放头在滚动时正确更新
        self.connect_scroll_events()

        # 确保覆盖层正确显示
        self.playhead_overlay.setVisible(True)
        self.playhead_overlay.raise_()
        self.playhead_overlay.show()

    def connect_scroll_events(self):
        """连接滚动事件，确保播放头在滚动时正确更新"""
        try:
            if hasattr(self.parent_widget, 'scroll_area') and self.parent_widget.scroll_area:
                scroll_bar = self.parent_widget.scroll_area.horizontalScrollBar()
                if scroll_bar:
                    # 连接滚动条值变化信号
                    scroll_bar.valueChanged.connect(self.on_scroll_changed)
                    print("✅ 播放头管理器已连接滚动事件")
        except Exception as e:
            print(f"❌ 连接滚动事件失败: {e}")

    def on_scroll_changed(self, value):
        """滚动条值变化时更新播放头显示"""
        # 强制重绘播放头，使其根据新的滚动位置正确显示
        if self.playhead_overlay:
            self.playhead_overlay.update()
            # print(f"🔄 滚动更新播放头显示: scroll={value}")  # 减少日志输出

    def set_timeline_params(self, start_x: int, width: int, duration: float, pixels_per_second: float):
        """设置时间轴参数"""
        self.playhead_overlay.set_timeline_params(start_x, width, duration, pixels_per_second)

    def set_track_params(self, track_count: int, track_height: int = 64, track_spacing: int = 12):
        """设置轨道参数"""
        self.playhead_overlay.set_track_params(track_count, track_height, track_spacing)

    def set_control_bar_height(self, height: int):
        """设置控制栏高度"""
        self.playhead_overlay.set_control_bar_height(height)

    def set_position(self, time_seconds: float):
        """设置播放头时间位置"""
        self.playhead_overlay.set_position(time_seconds)

        # 🔧 修复：设置位置后立即更新显示，确保在滚动窗口中正确显示
        self.playhead_overlay.update()

    def update_geometry(self, geometry):
        """更新覆盖层几何位置"""
        self.playhead_overlay.setGeometry(geometry)
        self.playhead_overlay.setVisible(True)
        self.playhead_overlay.raise_()
        self.playhead_overlay.show()
        # 🔧 修复：强制重绘以确保滚动时播放头可见
        self.playhead_overlay.update()

    def get_position(self) -> float:
        """获取当前时间位置"""
        return self.playhead_overlay.current_position

    def set_visible(self, visible: bool):
        """设置播放头是否可见"""
        self.playhead_overlay.setVisible(visible)
