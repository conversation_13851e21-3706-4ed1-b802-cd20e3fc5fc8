# 媒体处理模块 (Media Processing)

## 📋 模块概述

媒体处理模块是视频编辑器的核心处理引擎，负责音频、视频和OCR文字识别的底层处理功能。该模块提供了完整的媒体文件处理能力，包括格式转换、编解码、特效应用等功能。

## 📁 模块文件

### 🎵 audio_processor.py
**功能**: 音频处理核心引擎
- **AudioProcessor类**: 主要的音频处理器
- **AudioInfo类**: 音频文件信息封装
- **BeatPoint类**: 音频节拍点数据结构

**主要功能**:
- 音频格式转换和编解码
- 音频特效应用（音量调节、淡入淡出、均衡器）
- 音频信息提取和分析
- 音频剪切、合并、混音
- 节拍检测和音乐结构分析

### 🎵 audio_utils.py
**功能**: 音频处理工具函数
- 音频格式验证
- 音频文件元数据提取
- 音频质量分析工具
- 音频格式转换辅助函数

### 🎬 video_processor.py
**功能**: 视频处理核心引擎
- **VideoProcessor类**: 主要的视频处理器
- **VideoInfo类**: 视频文件信息封装

**主要功能**:
- 视频格式转换和编解码
- 视频剪切、合并、调整尺寸
- 视频特效和滤镜应用
- 缩略图生成和帧提取
- 视频质量分析和优化
- 转场效果处理
- 水印添加功能

### 📝 ocr_processor.py
**功能**: OCR文字识别处理
- 视频帧文字提取
- 多引擎OCR支持（EasyOCR、Tesseract）
- 字幕文件生成（SRT、VTT、TXT格式）
- 文字区域智能合并

## 🔧 技术特性

### 支持的音频格式
- **输入**: MP3, WAV, AAC, M4A, FLAC, OGG
- **输出**: WAV, MP3, AAC, M4A

### 支持的视频格式
- **输入**: MP4, AVI, MOV, MKV, WMV, FLV
- **输出**: MP4, AVI, MOV

### 核心依赖
- **FFmpeg**: 音视频编解码核心
- **OpenCV**: 视频处理和计算机视觉
- **NumPy**: 数值计算和数组处理
- **Librosa**: 音频分析（可选）
- **EasyOCR/Tesseract**: 文字识别（可选）

## 🚀 使用示例

### 音频处理示例
```python
from core.media_processing import AudioProcessor
from core.common import Config

config = Config()
audio_processor = AudioProcessor(config)

# 提取音频信息
audio_info = audio_processor.get_audio_info("input.mp3")
print(f"时长: {audio_info.duration}秒")

# 应用音频效果
effects = {
    'volume': 0.8,
    'fade_in': 2.0,
    'fade_out': 3.0
}
audio_processor.apply_audio_effects("input.wav", "output.wav", effects)
```

### 视频处理示例
```python
from core.media_processing import VideoProcessor

video_processor = VideoProcessor(config)

# 获取视频信息
video_info = video_processor.get_video_info("input.mp4")
print(f"分辨率: {video_info.width}x{video_info.height}")

# 剪切视频
video_processor.cut_video("input.mp4", "output.mp4", 10.0, 30.0)

# 生成缩略图
thumbnails = video_processor.get_thumbnail_frames("input.mp4", [5.0, 15.0, 25.0])
```

## ⚡ 性能优化

### 多线程处理
- 音频和视频处理支持多线程并发
- 批量文件处理优化
- 内存使用优化

### 缓存机制
- 缩略图智能缓存
- 音频分析结果缓存
- 视频信息缓存

### 硬件加速
- 支持GPU加速的视频编码（如果可用）
- 多核CPU并行处理
- 内存映射文件处理

## 🔍 错误处理

模块提供完整的错误处理机制：
- **AudioProcessingError**: 音频处理相关错误
- **VideoProcessingError**: 视频处理相关错误
- **FFmpegError**: FFmpeg执行错误
- 详细的错误日志和调试信息

## 📊 监控和日志

- 处理进度实时监控
- 详细的操作日志记录
- 性能指标统计
- 错误追踪和报告
