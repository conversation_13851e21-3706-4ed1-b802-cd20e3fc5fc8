#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SWANKSALON 专用界面
理发店视频自动剪辑系统的用户界面
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QLabel, QLineEdit, QPushButton, QTextEdit, QProgressBar,
    QGroupBox, QTabWidget, QWidget, QFileDialog, QMessageBox,
    QSpinBox, QDoubleSpinBox, QComboBox, QCheckBox, QSlider,
    QListWidget, QListWidgetItem, QSplitter, QFrame
)
from PySide6.QtCore import Qt, QThread, Signal, QTimer
from PySide6.QtGui import QFont, QPixmap, QIcon

from core.swanksalon.swanksalon_engine import SwankSalonEngine, validate_swanksalon_config, create_default_swanksalon_config
from core.common.logger import get_logger
from ..styles.dialog_styles import DialogStyleManager, CustomDialog


class SwankSalonProcessingThread(QThread):
    """SWANKSALON处理线程"""
    
    progress_updated = Signal(int)
    status_updated = Signal(str)
    finished_processing = Signal(bool, str)
    
    def __init__(self, config: Dict[str, Any], output_dir: str):
        super().__init__()
        self.config = config
        self.output_dir = output_dir
        self.logger = get_logger('swanksalon_thread')
    
    def run(self):
        """运行处理"""
        try:
            self.status_updated.emit("初始化SWANKSALON引擎...")
            self.progress_updated.emit(10)
            
            # 初始化引擎
            engine = SwankSalonEngine(self.config)
            
            self.status_updated.emit("开始批量处理...")
            self.progress_updated.emit(20)
            
            # 批量处理
            successful_outputs = engine.process_batch(self.output_dir)
            
            self.progress_updated.emit(90)
            
            # 清理临时文件
            self.status_updated.emit("清理临时文件...")
            engine.cleanup_temp_files()
            
            self.progress_updated.emit(100)
            
            if successful_outputs:
                message = f"处理完成！成功生成 {len(successful_outputs)} 个视频文件。"
                self.finished_processing.emit(True, message)
            else:
                self.finished_processing.emit(False, "处理失败，未生成任何视频文件。")
                
        except Exception as e:
            self.logger.error(f"处理线程异常: {e}")
            self.finished_processing.emit(False, f"处理失败: {str(e)}")


class SwankSalonDialog(CustomDialog):
    """SWANKSALON主对话框"""

    def __init__(self, parent=None):
        super().__init__(parent, "SWANKSALON - 理发店视频自动剪辑系统")
        self.logger = get_logger('swanksalon_dialog')

        # 配置数据
        self.config = create_default_swanksalon_config()

        # 处理线程
        self.processing_thread = None

        self.init_ui()
        self.load_default_config()

        self.logger.info("SWANKSALON界面初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        self.setMinimumSize(900, 700)

        # 🔧 修改：使用自定义对话框的内容布局
        main_layout = self.content_layout
        
        # 标题
        title_label = QLabel("SWANKSALON")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        
        subtitle_label = QLabel("理发店视频自动剪辑系统")
        subtitle_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(subtitle_label)
        
        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(line)
        
        # 标签页
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 创建各个标签页
        self.create_project_tab()
        self.create_music_tab()
        self.create_videos_tab()
        self.create_template_tab()
        self.create_output_tab()
        self.create_processing_tab()
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.load_config_btn = QPushButton("加载配置")
        self.save_config_btn = QPushButton("保存配置")
        self.start_processing_btn = QPushButton("开始处理")
        self.close_btn = QPushButton("关闭")
        
        self.load_config_btn.clicked.connect(self.load_config)
        self.save_config_btn.clicked.connect(self.save_config)
        self.start_processing_btn.clicked.connect(self.start_processing)
        self.close_btn.clicked.connect(self.close)
        
        button_layout.addWidget(self.load_config_btn)
        button_layout.addWidget(self.save_config_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.start_processing_btn)
        button_layout.addWidget(self.close_btn)
        
        main_layout.addLayout(button_layout)
    
    def create_project_tab(self):
        """创建项目设置标签页"""
        tab = QWidget()
        layout = QFormLayout(tab)
        
        # 项目名称
        self.project_name_edit = QLineEdit()
        layout.addRow("项目名称:", self.project_name_edit)
        
        # 项目描述
        self.project_desc_edit = QTextEdit()
        self.project_desc_edit.setMaximumHeight(100)
        layout.addRow("项目描述:", self.project_desc_edit)
        
        self.tab_widget.addTab(tab, "项目设置")
    
    def create_music_tab(self):
        """创建音乐设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 音乐文件选择
        music_group = QGroupBox("音乐文件")
        music_layout = QHBoxLayout(music_group)
        
        self.music_path_edit = QLineEdit()
        self.music_browse_btn = QPushButton("浏览...")
        self.music_browse_btn.clicked.connect(self.browse_music_file)
        
        music_layout.addWidget(self.music_path_edit)
        music_layout.addWidget(self.music_browse_btn)
        layout.addWidget(music_group)
        
        # 音乐参数
        params_group = QGroupBox("音乐参数")
        params_layout = QFormLayout(params_group)
        
        self.music_volume_slider = QSlider(Qt.Horizontal)
        self.music_volume_slider.setRange(0, 100)
        self.music_volume_slider.setValue(60)
        self.music_volume_label = QLabel("60%")
        self.music_volume_slider.valueChanged.connect(
            lambda v: self.music_volume_label.setText(f"{v}%")
        )
        
        volume_layout = QHBoxLayout()
        volume_layout.addWidget(self.music_volume_slider)
        volume_layout.addWidget(self.music_volume_label)
        params_layout.addRow("音量:", volume_layout)
        
        self.bpm_spin = QSpinBox()
        self.bpm_spin.setRange(60, 200)
        self.bpm_spin.setValue(120)
        params_layout.addRow("BPM (可选):", self.bpm_spin)
        
        layout.addWidget(params_group)
        
        # 音乐分析结果
        analysis_group = QGroupBox("音乐分析结果")
        self.music_analysis_text = QTextEdit()
        self.music_analysis_text.setMaximumHeight(150)
        self.music_analysis_text.setReadOnly(True)
        
        analysis_layout = QVBoxLayout(analysis_group)
        analysis_layout.addWidget(self.music_analysis_text)
        
        self.analyze_music_btn = QPushButton("分析音乐")
        self.analyze_music_btn.clicked.connect(self.analyze_music)
        analysis_layout.addWidget(self.analyze_music_btn)
        
        layout.addWidget(analysis_group)
        
        self.tab_widget.addTab(tab, "音乐设置")
    
    def create_videos_tab(self):
        """创建视频设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 视频文件列表
        videos_group = QGroupBox("视频文件列表")
        videos_layout = QVBoxLayout(videos_group)
        
        # 按钮行
        btn_layout = QHBoxLayout()
        self.add_video_btn = QPushButton("添加视频")
        self.remove_video_btn = QPushButton("移除视频")
        self.clear_videos_btn = QPushButton("清空列表")
        
        self.add_video_btn.clicked.connect(self.add_video_files)
        self.remove_video_btn.clicked.connect(self.remove_selected_video)
        self.clear_videos_btn.clicked.connect(self.clear_video_list)
        
        btn_layout.addWidget(self.add_video_btn)
        btn_layout.addWidget(self.remove_video_btn)
        btn_layout.addWidget(self.clear_videos_btn)
        btn_layout.addStretch()
        
        videos_layout.addLayout(btn_layout)
        
        # 视频列表
        self.video_list = QListWidget()
        videos_layout.addWidget(self.video_list)
        
        layout.addWidget(videos_group)
        
        self.tab_widget.addTab(tab, "视频文件")
    
    def create_template_tab(self):
        """创建模板设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 模板基本信息
        basic_group = QGroupBox("模板基本信息")
        basic_layout = QFormLayout(basic_group)
        
        self.template_name_edit = QLineEdit()
        basic_layout.addRow("模板名称:", self.template_name_edit)
        
        self.template_duration_spin = QDoubleSpinBox()
        self.template_duration_spin.setRange(10.0, 300.0)
        self.template_duration_spin.setValue(30.0)
        self.template_duration_spin.setSuffix(" 秒")
        basic_layout.addRow("目标时长:", self.template_duration_spin)
        
        layout.addWidget(basic_group)
        
        # 美颜参数
        beauty_group = QGroupBox("美颜参数")
        beauty_layout = QFormLayout(beauty_group)
        
        self.skin_smooth_slider = QSlider(Qt.Horizontal)
        self.skin_smooth_slider.setRange(0, 100)
        self.skin_smooth_slider.setValue(70)
        self.skin_smooth_label = QLabel("70%")
        self.skin_smooth_slider.valueChanged.connect(
            lambda v: self.skin_smooth_label.setText(f"{v}%")
        )
        
        smooth_layout = QHBoxLayout()
        smooth_layout.addWidget(self.skin_smooth_slider)
        smooth_layout.addWidget(self.skin_smooth_label)
        beauty_layout.addRow("磨皮强度:", smooth_layout)
        
        self.face_slim_slider = QSlider(Qt.Horizontal)
        self.face_slim_slider.setRange(0, 100)
        self.face_slim_slider.setValue(50)
        self.face_slim_label = QLabel("50%")
        self.face_slim_slider.valueChanged.connect(
            lambda v: self.face_slim_label.setText(f"{v}%")
        )
        
        slim_layout = QHBoxLayout()
        slim_layout.addWidget(self.face_slim_slider)
        slim_layout.addWidget(self.face_slim_label)
        beauty_layout.addRow("瘦脸强度:", slim_layout)
        
        layout.addWidget(beauty_group)
        
        # 发色增强
        hair_group = QGroupBox("发色增强")
        hair_layout = QVBoxLayout(hair_group)
        
        self.hair_red_check = QCheckBox("红色增强")
        self.hair_blue_check = QCheckBox("蓝色增强")
        self.hair_purple_check = QCheckBox("紫色增强")
        self.hair_green_check = QCheckBox("绿色增强")
        
        self.hair_red_check.setChecked(True)
        self.hair_blue_check.setChecked(True)
        self.hair_purple_check.setChecked(True)
        
        hair_layout.addWidget(self.hair_red_check)
        hair_layout.addWidget(self.hair_blue_check)
        hair_layout.addWidget(self.hair_purple_check)
        hair_layout.addWidget(self.hair_green_check)
        
        layout.addWidget(hair_group)
        
        self.tab_widget.addTab(tab, "模板设置")
    
    def create_output_tab(self):
        """创建输出设置标签页"""
        tab = QWidget()
        layout = QFormLayout(tab)
        
        # 输出目录
        output_layout = QHBoxLayout()
        self.output_dir_edit = QLineEdit()
        self.output_browse_btn = QPushButton("浏览...")
        self.output_browse_btn.clicked.connect(self.browse_output_dir)
        
        output_layout.addWidget(self.output_dir_edit)
        output_layout.addWidget(self.output_browse_btn)
        layout.addRow("输出目录:", output_layout)
        
        # 视频参数
        self.resolution_combo = QComboBox()
        self.resolution_combo.addItems(["720p", "1080p", "1440p", "4K"])
        self.resolution_combo.setCurrentText("1080p")
        layout.addRow("分辨率:", self.resolution_combo)
        
        self.fps_spin = QSpinBox()
        self.fps_spin.setRange(24, 60)
        self.fps_spin.setValue(30)
        layout.addRow("帧率:", self.fps_spin)
        
        self.tab_widget.addTab(tab, "输出设置")
    
    def create_processing_tab(self):
        """创建处理进度标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 进度显示
        progress_group = QGroupBox("处理进度")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        progress_layout.addWidget(self.progress_bar)
        
        self.status_label = QLabel("就绪")
        progress_layout.addWidget(self.status_label)
        
        layout.addWidget(progress_group)
        
        # 日志显示
        log_group = QGroupBox("处理日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        self.tab_widget.addTab(tab, "处理进度")
    
    def load_default_config(self):
        """加载默认配置到界面"""
        try:
            # 项目设置
            self.project_name_edit.setText(self.config.get('project', ''))
            
            # 音乐设置
            music_config = self.config.get('music', {})
            self.music_path_edit.setText(music_config.get('path', ''))
            volume = int(music_config.get('volume', 0.6) * 100)
            self.music_volume_slider.setValue(volume)
            self.bpm_spin.setValue(music_config.get('bpm', 120))
            
            # 模板设置
            template_config = self.config.get('template', {})
            self.template_name_edit.setText(template_config.get('name', ''))
            self.template_duration_spin.setValue(template_config.get('duration', 30.0))
            
            beauty_params = template_config.get('beauty_params', {})
            self.skin_smooth_slider.setValue(int(beauty_params.get('skin_smooth', 0.7) * 100))
            self.face_slim_slider.setValue(int(beauty_params.get('face_slim', 0.5) * 100))
            
            hair_boost = beauty_params.get('hair_boost', [])
            self.hair_red_check.setChecked('red' in hair_boost)
            self.hair_blue_check.setChecked('blue' in hair_boost)
            self.hair_purple_check.setChecked('purple' in hair_boost)
            self.hair_green_check.setChecked('green' in hair_boost)
            
            # 输出设置
            output_config = self.config.get('output', {})
            self.output_dir_edit.setText(output_config.get('directory', 'output_videos'))
            self.fps_spin.setValue(output_config.get('frame_rate', 30))
            
        except Exception as e:
            self.logger.error(f"加载默认配置失败: {e}")
    
    def update_config_from_ui(self):
        """从界面更新配置"""
        try:
            # 项目设置
            self.config['project'] = self.project_name_edit.text()
            
            # 音乐设置
            self.config['music']['path'] = self.music_path_edit.text()
            self.config['music']['volume'] = self.music_volume_slider.value() / 100.0
            self.config['music']['bpm'] = self.bpm_spin.value()
            
            # 视频列表
            videos = []
            for i in range(self.video_list.count()):
                item = self.video_list.item(i)
                videos.append({'path': item.text()})
            self.config['videos'] = videos
            
            # 模板设置
            self.config['template']['name'] = self.template_name_edit.text()
            self.config['template']['duration'] = self.template_duration_spin.value()
            
            # 美颜参数
            beauty_params = self.config['template']['beauty_params']
            beauty_params['skin_smooth'] = self.skin_smooth_slider.value() / 100.0
            beauty_params['face_slim'] = self.face_slim_slider.value() / 100.0
            
            # 发色增强
            hair_boost = []
            if self.hair_red_check.isChecked():
                hair_boost.append('red')
            if self.hair_blue_check.isChecked():
                hair_boost.append('blue')
            if self.hair_purple_check.isChecked():
                hair_boost.append('purple')
            if self.hair_green_check.isChecked():
                hair_boost.append('green')
            beauty_params['hair_boost'] = hair_boost
            
            # 输出设置
            self.config['output']['directory'] = self.output_dir_edit.text()
            self.config['output']['frame_rate'] = self.fps_spin.value()
            self.config['output']['resolution'] = self.resolution_combo.currentText()
            
        except Exception as e:
            self.logger.error(f"更新配置失败: {e}")
    
    def browse_music_file(self):
        """浏览音乐文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择音乐文件", "",
            "音频文件 (*.mp3 *.wav *.aac *.m4a *.flac);;所有文件 (*)"
        )
        if file_path:
            self.music_path_edit.setText(file_path)
    
    def browse_output_dir(self):
        """浏览输出目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.output_dir_edit.setText(dir_path)
    
    def add_video_files(self):
        """添加视频文件"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "选择视频文件", "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv);;所有文件 (*)"
        )
        for file_path in file_paths:
            self.video_list.addItem(file_path)
    
    def remove_selected_video(self):
        """移除选中的视频"""
        current_row = self.video_list.currentRow()
        if current_row >= 0:
            self.video_list.takeItem(current_row)
    
    def clear_video_list(self):
        """清空视频列表"""
        self.video_list.clear()
    
    def analyze_music(self):
        """分析音乐"""
        music_path = self.music_path_edit.text()
        if not music_path or not os.path.exists(music_path):
            QMessageBox.warning(self, "警告", "请先选择有效的音乐文件")
            return
        
        try:
            self.music_analysis_text.setText("正在分析音乐...")
            
            # 这里可以调用音乐分析器进行分析
            # 暂时显示简单信息
            analysis_result = f"音乐文件: {os.path.basename(music_path)}\n"
            analysis_result += f"文件大小: {os.path.getsize(music_path) / 1024 / 1024:.1f} MB\n"
            analysis_result += "分析完成，可以开始处理。"
            
            self.music_analysis_text.setText(analysis_result)
            
        except Exception as e:
            self.music_analysis_text.setText(f"分析失败: {str(e)}")
    
    def load_config(self):
        """加载配置文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "加载配置文件", "", "JSON文件 (*.json);;所有文件 (*)"
        )
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                self.load_default_config()
                QMessageBox.information(self, "成功", "配置文件加载成功")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"加载配置文件失败: {str(e)}")
    
    def save_config(self):
        """保存配置文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存配置文件", "swanksalon_config.json", "JSON文件 (*.json);;所有文件 (*)"
        )
        if file_path:
            try:
                self.update_config_from_ui()
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.config, f, indent=4, ensure_ascii=False)
                QMessageBox.information(self, "成功", "配置文件保存成功")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存配置文件失败: {str(e)}")
    
    def start_processing(self):
        """开始处理"""
        try:
            # 更新配置
            self.update_config_from_ui()
            
            # 验证配置
            if not validate_swanksalon_config(self.config):
                QMessageBox.warning(self, "配置错误", "请检查配置设置")
                return
            
            # 检查输出目录
            output_dir = self.config['output']['directory']
            if not output_dir:
                QMessageBox.warning(self, "配置错误", "请设置输出目录")
                return
            
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            # 切换到处理进度标签页
            self.tab_widget.setCurrentIndex(5)
            
            # 重置进度
            self.progress_bar.setValue(0)
            self.status_label.setText("准备开始处理...")
            self.log_text.clear()
            
            # 禁用开始按钮
            self.start_processing_btn.setEnabled(False)
            
            # 启动处理线程
            self.processing_thread = SwankSalonProcessingThread(self.config, output_dir)
            self.processing_thread.progress_updated.connect(self.progress_bar.setValue)
            self.processing_thread.status_updated.connect(self.status_label.setText)
            self.processing_thread.status_updated.connect(self.log_text.append)
            self.processing_thread.finished_processing.connect(self.on_processing_finished)
            self.processing_thread.start()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动处理失败: {str(e)}")
            self.start_processing_btn.setEnabled(True)
    
    def on_processing_finished(self, success: bool, message: str):
        """处理完成回调"""
        self.start_processing_btn.setEnabled(True)
        
        if success:
            QMessageBox.information(self, "处理完成", message)
        else:
            QMessageBox.critical(self, "处理失败", message)
        
        self.log_text.append(message)
        self.status_label.setText("处理完成" if success else "处理失败")
