#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心模块 - 重新组织后的模块结构
"""

# 导入各个子模块
from . import media_processing
from . import ai_features
from . import swanksalon
from . import templates
from . import batch_processing
from . import common

# 导出常用类和函数
from .common.config import Config
from .common.logger import get_logger
from .media_processing.video_processor import VideoProcessor
from .media_processing.audio_processor import AudioProcessor