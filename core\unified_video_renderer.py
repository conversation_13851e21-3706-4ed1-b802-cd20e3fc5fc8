#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一视频渲染器 - 整合跳转、播放、效果处理的统一架构
"""

import cv2
import numpy as np
from typing import Dict, List, Optional, Any, Callable
from PySide6.QtCore import QObject, Signal, QTimer, QThread
from PySide6.QtGui import QImage, QPixmap
from dataclasses import dataclass
from pathlib import Path

@dataclass
class VideoEffect:
    """视频效果配置"""
    effect_type: str  # 'brightness', 'contrast', 'saturation', etc.
    value: float
    enabled: bool = True

@dataclass
class RenderRequest:
    """渲染请求"""
    video_path: str
    position: float  # 时间位置（秒）
    effects: List[VideoEffect]
    quality: str = 'original'  # 'original', 'high', 'smooth'
    
class UnifiedVideoRenderer(QObject):
    """
    统一视频渲染器
    - 统一处理跳转、播放、效果的帧渲染
    - 支持实时效果调整
    - 智能缓存和性能优化
    """
    
    # 信号
    frame_ready = Signal(str, np.ndarray)  # video_path, processed_frame
    position_changed = Signal(str, float)  # video_path, position
    
    def __init__(self):
        super().__init__()
        
        # 视频捕获器缓存
        self._video_captures: Dict[str, cv2.VideoCapture] = {}
        
        # 效果处理器
        self._effect_processors = {
            'brightness': self._apply_brightness,
            'contrast': self._apply_contrast,
            'saturation': self._apply_saturation,
            'blur': self._apply_blur,
            'sharpen': self._apply_sharpen,
        }
        
        # 播放状态管理
        self._playing_videos: Dict[str, bool] = {}
        self._playback_timers: Dict[str, QTimer] = {}
        self._current_positions: Dict[str, float] = {}
        
        # 帧缓存（可选优化）
        self._frame_cache: Dict[str, np.ndarray] = {}
        self._cache_enabled = True
        self._max_cache_size = 50
        
        print("✅ 统一视频渲染器初始化完成")
    
    def get_video_capture(self, video_path: str) -> Optional[cv2.VideoCapture]:
        """获取或创建视频捕获器"""
        if video_path not in self._video_captures:
            if not Path(video_path).exists():
                return None
            
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return None
            
            self._video_captures[video_path] = cap
            print(f"📹 创建视频捕获器: {video_path}")
        
        return self._video_captures[video_path]
    
    def render_frame_at_position(self, request: RenderRequest) -> bool:
        """
        渲染指定位置的帧（统一入口）
        - 用于跳转预览
        - 用于播放中的帧
        - 用于效果实时调整
        """
        try:
            cap = self.get_video_capture(request.video_path)
            if not cap:
                return False
            
            # 跳转到指定位置
            fps = cap.get(cv2.CAP_PROP_FPS) or 30.0
            frame_number = int(request.position * fps)
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            
            # 读取原始帧
            ret, raw_frame = cap.read()
            if not ret:
                return False
            
            # 应用效果处理
            processed_frame = self._apply_effects(raw_frame, request.effects)
            
            # 应用质量缩放
            final_frame = self._apply_quality_scaling(processed_frame, request.quality)
            
            # 更新位置记录
            self._current_positions[request.video_path] = request.position
            
            # 发射信号
            self.frame_ready.emit(request.video_path, final_frame)
            self.position_changed.emit(request.video_path, request.position)
            
            return True
            
        except Exception as e:
            print(f"❌ 渲染帧失败: {e}")
            return False
    
    def start_playback(self, video_path: str, effects: List[VideoEffect], 
                      start_position: float = 0.0, quality: str = 'original'):
        """开始播放（使用统一渲染）"""
        try:
            # 停止之前的播放
            self.stop_playback(video_path)
            
            cap = self.get_video_capture(video_path)
            if not cap:
                return False
            
            # 设置播放状态
            self._playing_videos[video_path] = True
            self._current_positions[video_path] = start_position
            
            # 创建播放定时器
            fps = cap.get(cv2.CAP_PROP_FPS) or 30.0
            frame_interval = int(1000 / fps)  # 毫秒
            
            timer = QTimer()
            timer.timeout.connect(lambda: self._on_playback_timer(video_path, effects, quality))
            timer.start(frame_interval)
            
            self._playback_timers[video_path] = timer
            
            print(f"▶️ 开始播放: {video_path} (FPS: {fps})")
            return True
            
        except Exception as e:
            print(f"❌ 开始播放失败: {e}")
            return False
    
    def stop_playback(self, video_path: str):
        """停止播放"""
        if video_path in self._playback_timers:
            self._playback_timers[video_path].stop()
            del self._playback_timers[video_path]
        
        self._playing_videos[video_path] = False
        print(f"⏹️ 停止播放: {video_path}")
    
    def seek_to_position(self, video_path: str, position: float, effects: List[VideoEffect], 
                        quality: str = 'original'):
        """跳转到指定位置（使用统一渲染）"""
        request = RenderRequest(
            video_path=video_path,
            position=position,
            effects=effects,
            quality=quality
        )
        return self.render_frame_at_position(request)
    
    def update_effects(self, video_path: str, effects: List[VideoEffect], quality: str = 'original'):
        """实时更新效果（重新渲染当前帧）"""
        if video_path in self._current_positions:
            current_pos = self._current_positions[video_path]
            request = RenderRequest(
                video_path=video_path,
                position=current_pos,
                effects=effects,
                quality=quality
            )
            return self.render_frame_at_position(request)
        return False
    
    def _on_playback_timer(self, video_path: str, effects: List[VideoEffect], quality: str):
        """播放定时器回调"""
        if not self._playing_videos.get(video_path, False):
            return
        
        cap = self.get_video_capture(video_path)
        if not cap:
            return
        
        # 读取下一帧
        ret, raw_frame = cap.read()
        if not ret:
            # 播放结束
            self.stop_playback(video_path)
            return
        
        # 应用效果处理
        processed_frame = self._apply_effects(raw_frame, effects)
        final_frame = self._apply_quality_scaling(processed_frame, quality)
        
        # 更新位置
        current_pos = cap.get(cv2.CAP_PROP_POS_FRAMES) / (cap.get(cv2.CAP_PROP_FPS) or 30.0)
        self._current_positions[video_path] = current_pos
        
        # 发射信号
        self.frame_ready.emit(video_path, final_frame)
        self.position_changed.emit(video_path, current_pos)
    
    def _apply_effects(self, frame: np.ndarray, effects: List[VideoEffect]) -> np.ndarray:
        """应用效果处理"""
        result_frame = frame.copy()
        
        for effect in effects:
            if not effect.enabled:
                continue
            
            processor = self._effect_processors.get(effect.effect_type)
            if processor:
                result_frame = processor(result_frame, effect.value)
        
        return result_frame
    
    def _apply_brightness(self, frame: np.ndarray, value: float) -> np.ndarray:
        """应用亮度调整"""
        return cv2.convertScaleAbs(frame, alpha=1.0, beta=value)
    
    def _apply_contrast(self, frame: np.ndarray, value: float) -> np.ndarray:
        """应用对比度调整"""
        return cv2.convertScaleAbs(frame, alpha=value, beta=0)
    
    def _apply_saturation(self, frame: np.ndarray, value: float) -> np.ndarray:
        """应用饱和度调整"""
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        hsv[:, :, 1] = cv2.multiply(hsv[:, :, 1], value)
        return cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)
    
    def _apply_blur(self, frame: np.ndarray, value: float) -> np.ndarray:
        """应用模糊效果"""
        kernel_size = int(value * 10) | 1  # 确保为奇数
        return cv2.GaussianBlur(frame, (kernel_size, kernel_size), 0)
    
    def _apply_sharpen(self, frame: np.ndarray, value: float) -> np.ndarray:
        """应用锐化效果"""
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]]) * value
        return cv2.filter2D(frame, -1, kernel)
    
    def _apply_quality_scaling(self, frame: np.ndarray, quality: str) -> np.ndarray:
        """应用质量缩放"""
        if quality == 'smooth':
            # 流畅模式：50%
            height, width = frame.shape[:2]
            new_size = (int(width * 0.5), int(height * 0.5))
            return cv2.resize(frame, new_size, interpolation=cv2.INTER_LINEAR)
        elif quality == 'high':
            # 高清模式：75%
            height, width = frame.shape[:2]
            new_size = (int(width * 0.75), int(height * 0.75))
            return cv2.resize(frame, new_size, interpolation=cv2.INTER_CUBIC)
        else:
            # 原画模式
            return frame
    
    def cleanup(self):
        """清理资源"""
        # 停止所有播放
        for video_path in list(self._playing_videos.keys()):
            self.stop_playback(video_path)
        
        # 释放视频捕获器
        for cap in self._video_captures.values():
            cap.release()
        self._video_captures.clear()
        
        print("🧹 统一视频渲染器资源已清理")
