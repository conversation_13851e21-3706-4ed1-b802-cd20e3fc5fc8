#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SWANKSALON 视频处理引擎
专业理发店视频自动剪辑系统
"""

import cv2
import numpy as np
import json
import os
import concurrent.futures
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from ..common.logger import get_logger
from ..common.exceptions import VideoProcessingError

try:
    import librosa
    LIBROSA_AVAILABLE = True
except ImportError:
    LIBROSA_AVAILABLE = False
    print("⚠️ librosa未安装，音乐分析功能将被限制")

try:
    from moviepy.editor import VideoFileClip, concatenate_videoclips
    MOVIEPY_AVAILABLE = True
except ImportError:
    MOVIEPY_AVAILABLE = False
    print("⚠️ moviepy未安装，视频编辑功能将被限制")

try:
    from core.music_analyzer import MusicAnalyzer
    MUSIC_ANALYZER_AVAILABLE = True
except ImportError:
    MUSIC_ANALYZER_AVAILABLE = False
    print("⚠️ 音乐分析器不可用")

try:
    from core.face_processor import FaceProcessor
    FACE_PROCESSOR_AVAILABLE = True
except ImportError:
    FACE_PROCESSOR_AVAILABLE = False
    print("⚠️ 人脸处理器不可用")

try:
    from core.salon_effects import SalonEffects
    SALON_EFFECTS_AVAILABLE = True
except ImportError:
    SALON_EFFECTS_AVAILABLE = False
    print("⚠️ 理发店特效库不可用")

try:
    from core.beat_sync_processing_engine import BeatSyncProcessingEngine, ProcessingConfig
    from core.beat_detection_engine import HighPrecisionBeatDetector
    from core.precision_control_system import PrecisionControlSystem
    BEAT_SYNC_AVAILABLE = True
except ImportError:
    BEAT_SYNC_AVAILABLE = False
    print("⚠️ 踩点转场系统不可用")

try:
    import dlib
    DLIB_AVAILABLE = True
except ImportError:
    DLIB_AVAILABLE = False
    print("⚠️ dlib未安装，人脸检测功能将被禁用")

try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    print("⚠️ torch未安装，GPU加速功能将被禁用")

try:
    from transformers import BertTokenizer, BertModel
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    print("⚠️ transformers未安装，情感分析功能将被禁用")


class SwankSalonEngine:
    """SWANKSALON 视频处理引擎"""
    
    def __init__(self, config: Dict[str, Any], gpu_id: int = 0):
        """
        初始化SWANKSALON引擎
        
        Args:
            config: 完整配置字典
            gpu_id: GPU设备ID
        """
        self.config = config
        self.template = config.get('template', {})
        self.music = config.get('music', {})
        self.videos = config.get('videos', [])
        self.output_config = config.get('output', {})
        
        # 设备配置
        if TORCH_AVAILABLE:
            self.device = torch.device(f"cuda:{gpu_id}" if torch.cuda.is_available() else "cpu")
        else:
            self.device = "cpu"
        self.logger = get_logger('swanksalon_engine')
        
        # 初始化组件
        self._init_face_detector()
        self._init_emotion_analyzer()
        self._init_transition_effects()

        # 初始化专业模块
        self.music_analyzer = MusicAnalyzer() if MUSIC_ANALYZER_AVAILABLE else None
        self.face_processor = FaceProcessor(self.template.get('beauty_params', {})) if FACE_PROCESSOR_AVAILABLE else None
        self.salon_effects = SalonEffects(self.template) if SALON_EFFECTS_AVAILABLE else None

        # 初始化踩点转场系统
        if BEAT_SYNC_AVAILABLE:
            beat_sync_config = ProcessingConfig(
                target_accuracy_ms=50.0,
                enable_beauty_filters=True,
                enable_smart_transitions=True,
                output_resolution=(1920, 1080),
                output_fps=30,
                quality_preset="high"
            )
            self.beat_sync_engine = BeatSyncProcessingEngine(beat_sync_config)
            self.precision_control = PrecisionControlSystem(target_precision_ms=50.0)
        else:
            self.beat_sync_engine = None
            self.precision_control = None
        
        # 缓存
        self._thumbnail_cache = {}
        self._beat_cache = {}
        
        self.logger.info(f"SwankSalon引擎初始化完成，使用设备: {self.device}")
    
    def _init_face_detector(self):
        """初始化人脸检测器"""
        if not DLIB_AVAILABLE:
            self.face_detector = None
            self.face_predictor = None
            return
        
        try:
            self.face_detector = dlib.get_frontal_face_detector()
            
            # 尝试加载人脸关键点检测器
            predictor_path = "shape_predictor_68_face_landmarks.dat"
            if os.path.exists(predictor_path):
                self.face_predictor = dlib.shape_predictor(predictor_path)
                self.logger.info("人脸关键点检测器加载成功")
            else:
                self.face_predictor = None
                self.logger.warning("未找到人脸关键点检测器文件")
                
        except Exception as e:
            self.logger.error(f"初始化人脸检测器失败: {e}")
            self.face_detector = None
            self.face_predictor = None
    
    def _init_emotion_analyzer(self):
        """初始化情感分析器"""
        if not TRANSFORMERS_AVAILABLE:
            self.tokenizer = None
            self.bert_model = None
            return
        
        try:
            if TORCH_AVAILABLE:
                self.tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')
                self.bert_model = BertModel.from_pretrained('bert-base-uncased').to(self.device)
                self.logger.info("情感分析模型加载成功")
            else:
                self.tokenizer = None
                self.bert_model = None
        except Exception as e:
            self.logger.error(f"初始化情感分析器失败: {e}")
            self.tokenizer = None
            self.bert_model = None
    
    def _init_transition_effects(self):
        """初始化转场特效库"""
        self.transition_lib = {
            "scissor_light": self._scissor_light_effect,
            "hair_particle": self._hair_particle_effect,
            "color_dissolve": self._color_dissolve_effect,
            "fade": self._fade_effect,
            "slide": self._slide_effect,
            "zoom": self._zoom_effect,
            "spin": self._spin_effect,
            "wave": self._wave_effect,
            "sparkle": self._sparkle_effect,
            "glow": self._glow_effect
        }
        self.logger.info(f"转场特效库初始化完成，共{len(self.transition_lib)}种特效")
    
    def detect_beats(self, music_path: str) -> List[float]:
        """
        高精度节拍检测 - 使用专业音乐分析器

        Args:
            music_path: 音乐文件路径

        Returns:
            节拍时间点列表
        """
        if music_path in self._beat_cache:
            return self._beat_cache[music_path]

        try:
            self.logger.info(f"开始分析音乐节拍: {music_path}")

            # 使用专业音乐分析器
            if self.music_analyzer:
                music_structure = self.music_analyzer.analyze_music_structure(music_path)
                beat_times = music_structure['beat_info']['beat_times']

                self._beat_cache[music_path] = beat_times

                tempo = music_structure['features']['tempo']
                self.logger.info(f"节拍检测完成: {len(beat_times)}个节拍点, BPM: {tempo:.1f}")
                return beat_times
            else:
                # 降级到简单检测
                return self._simple_beat_detection(music_path)

        except Exception as e:
            self.logger.error(f"节拍检测失败: {e}")
            # 降级到简单检测
            return self._simple_beat_detection(music_path)

    def _simple_beat_detection(self, music_path: str) -> List[float]:
        """简单节拍检测作为备选方案"""
        try:
            if LIBROSA_AVAILABLE:
                y, sr = librosa.load(music_path)
                tempo, beats = librosa.beat.beat_track(y=y, sr=sr)
                beat_times = librosa.frames_to_time(beats, sr=sr)
                return beat_times.tolist()
            else:
                # 如果没有librosa，返回固定间隔的节拍点
                self.logger.warning("librosa不可用，使用固定节拍间隔")
                duration = 30.0  # 假设30秒
                bpm = 120  # 假设120 BPM
                beat_interval = 60.0 / bpm
                return [i * beat_interval for i in range(int(duration / beat_interval))]
        except Exception as e:
            self.logger.error(f"简单节拍检测失败: {e}")
            return []
    
    def detect_face_poses(self, video_path: str) -> List[float]:
        """
        人脸姿态检测 - 使用专业人脸处理器

        Args:
            video_path: 视频文件路径

        Returns:
            有效帧时间戳列表
        """
        try:
            self.logger.info(f"开始人脸姿态检测: {video_path}")

            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise VideoProcessingError(f"无法打开视频文件: {video_path}")

            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

            valid_frames = []
            frame_count = 0

            # 采样检测，每10帧检测一次以提高性能
            sample_interval = 10

            while cap.isOpened() and frame_count < total_frames:
                ret, frame = cap.read()
                if not ret:
                    break

                if frame_count % sample_interval == 0:
                    # 使用专业人脸处理器分析人脸质量
                    if self.face_processor:
                        face_quality = self.face_processor.analyze_face_quality(frame)

                        if face_quality['has_face'] and face_quality['quality_score'] > 0.5:
                            timestamp = frame_count / fps
                            valid_frames.append(timestamp)
                    else:
                        # 如果没有人脸处理器，使用简单的帧采样
                        timestamp = frame_count / fps
                        valid_frames.append(timestamp)

                frame_count += 1

            cap.release()

            self.logger.info(f"人脸检测完成: {len(valid_frames)}个有效帧")
            return valid_frames

        except Exception as e:
            self.logger.error(f"人脸姿态检测失败: {e}")
            return []
    
    def classify_beat(self, beat_index: int, total_beats: int, tempo: float) -> str:
        """
        节拍强度分类

        Args:
            beat_index: 节拍索引
            total_beats: 总节拍数
            tempo: BPM

        Returns:
            节拍类型: "STRONG", "MEDIUM", "WEAK"
        """
        # 基于位置和BPM的简化分类
        if beat_index % 4 == 0:  # 每4拍的第1拍
            return "STRONG"
        elif beat_index % 2 == 0:  # 每2拍的第1拍
            return "MEDIUM"
        else:
            return "WEAK"

    def apply_beauty_filter(self, frame: np.ndarray, beat_type: str) -> np.ndarray:
        """
        应用美颜滤镜 - 使用专业人脸处理器

        Args:
            frame: 输入帧
            beat_type: 节拍类型

        Returns:
            处理后的帧
        """
        try:
            # 计算美颜强度
            intensity = 1.0
            if beat_type == "STRONG":
                intensity = 1.3  # 强拍时增强美颜效果
            elif beat_type == "WEAK":
                intensity = 0.7  # 弱拍时减弱美颜效果

            # 使用专业人脸处理器应用美颜
            if self.face_processor:
                result = self.face_processor.apply_beauty_filter(frame, intensity)

                # 应用发色增强
                hair_colors = self.template.get('beauty_params', {}).get('hair_boost', [])
                if hair_colors and beat_type == "STRONG":
                    result = self.face_processor.enhance_hair_color(result, hair_colors, intensity * 0.5)
            else:
                # 如果没有人脸处理器，返回原帧
                result = frame

            return result

        except Exception as e:
            self.logger.error(f"美颜滤镜应用失败: {e}")
            return frame

    def enhance_hair_color(self, frame: np.ndarray, intensity: float) -> np.ndarray:
        """
        发色增强引擎

        Args:
            frame: 输入帧
            intensity: 增强强度 (0.0-1.0)

        Returns:
            处理后的帧
        """
        try:
            if intensity <= 0:
                return frame

            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
            hair_colors = self.template.get('beauty_params', {}).get('hair_boost', [])

            for color in hair_colors:
                if color == "red":
                    # 红色增强
                    lower1 = np.array([0, 50, 50])
                    upper1 = np.array([10, 255, 255])
                    lower2 = np.array([170, 50, 50])
                    upper2 = np.array([180, 255, 255])

                    mask1 = cv2.inRange(hsv, lower1, upper1)
                    mask2 = cv2.inRange(hsv, lower2, upper2)
                    mask = mask1 + mask2

                elif color == "blue":
                    # 蓝色增强
                    lower = np.array([100, 50, 50])
                    upper = np.array([130, 255, 255])
                    mask = cv2.inRange(hsv, lower, upper)

                elif color == "purple":
                    # 紫色增强
                    lower = np.array([130, 50, 50])
                    upper = np.array([160, 255, 255])
                    mask = cv2.inRange(hsv, lower, upper)

                else:
                    continue

                # 增强饱和度
                saturation_boost = 1.0 + intensity * 0.5
                hsv[:, :, 1] = np.where(mask > 0,
                                      np.clip(hsv[:, :, 1] * saturation_boost, 0, 255),
                                      hsv[:, :, 1])

            return cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)

        except Exception as e:
            self.logger.error(f"发色增强失败: {e}")
            return frame

    # ==================== 转场特效实现 - 使用专业特效库 ====================

    def apply_transition_effect(self, frame1: np.ndarray, frame2: np.ndarray,
                              effect_name: str, progress: float, **kwargs) -> np.ndarray:
        """
        应用转场特效 - 使用专业特效库

        Args:
            frame1: 第一帧
            frame2: 第二帧
            effect_name: 特效名称
            progress: 进度
            **kwargs: 额外参数

        Returns:
            处理后的帧
        """
        try:
            # 添加主题颜色到参数中
            if 'theme_colors' not in kwargs:
                kwargs['theme_colors'] = self.template.get('theme_colors', ['#FF3366', '#33CCFF'])

            if self.salon_effects:
                return self.salon_effects.apply_transition(frame1, frame2, effect_name, progress, **kwargs)
            else:
                # 如果没有特效库，使用简单的淡入淡出
                return cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)

        except Exception as e:
            self.logger.error(f"应用转场特效失败: {e}")
            return cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)

    def _scissor_light_effect(self, frame1: np.ndarray, frame2: np.ndarray, progress: float) -> np.ndarray:
        """剪刀光效转场"""
        try:
            h, w = frame1.shape[:2]

            # 创建剪刀形状遮罩
            mask = np.zeros((h, w), dtype=np.uint8)

            # 绘制交叉的剪刀线条
            line_thickness = max(2, int(w * 0.02))
            cv2.line(mask, (0, 0), (w, h), 255, line_thickness)
            cv2.line(mask, (w, 0), (0, h), 255, line_thickness)

            # 创建光效
            glow = np.zeros_like(frame1)
            glow_thickness = line_thickness * 3
            cv2.line(glow, (0, 0), (w, h), (0, 255, 255), glow_thickness)
            cv2.line(glow, (w, 0), (0, h), (0, 255, 255), glow_thickness)

            # 高斯模糊产生光晕效果
            glow = cv2.GaussianBlur(glow, (21, 21), 0)

            # 应用转场
            mask_3d = cv2.cvtColor(mask, cv2.COLOR_GRAY2BGR) / 255.0
            result = frame1 * (1 - mask_3d * progress) + frame2 * (mask_3d * progress)

            # 添加光效
            result = cv2.addWeighted(result.astype(np.uint8), 1.0, glow, progress * 0.5, 0)

            return result.astype(np.uint8)

        except Exception as e:
            self.logger.error(f"剪刀光效转场失败: {e}")
            return cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)

    def _hair_particle_effect(self, frame1: np.ndarray, frame2: np.ndarray, progress: float) -> np.ndarray:
        """发丝粒子消散转场"""
        try:
            h, w = frame1.shape[:2]
            result = frame1.copy()

            # 粒子数量随进度增加
            num_particles = int(200 * progress)

            for _ in range(num_particles):
                # 随机粒子位置
                x = np.random.randint(0, w)
                y = np.random.randint(0, h)

                # 粒子大小和颜色
                size = np.random.randint(1, 4)
                color = frame1[y, x].tolist()

                # 绘制粒子
                cv2.circle(result, (x, y), size, color, -1)

            # 混合两帧
            return cv2.addWeighted(result, 1-progress, frame2, progress, 0)

        except Exception as e:
            self.logger.error(f"发丝粒子转场失败: {e}")
            return cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)

    def _color_dissolve_effect(self, frame1: np.ndarray, frame2: np.ndarray, progress: float) -> np.ndarray:
        """品牌色溶解转场"""
        try:
            h, w = frame1.shape[:2]

            # 获取主题颜色
            theme_colors = self.template.get('theme_colors', ['#FF3366', '#33CCFF'])

            # 创建彩色噪声
            noise = np.random.rand(h, w)
            color_mask = np.zeros_like(frame1)

            for i, color_hex in enumerate(theme_colors):
                # 解析十六进制颜色
                color_hex = color_hex.lstrip('#')
                if len(color_hex) == 6:
                    r = int(color_hex[0:2], 16)
                    g = int(color_hex[2:4], 16)
                    b = int(color_hex[4:6], 16)
                    rgb_color = [b, g, r]  # OpenCV使用BGR
                else:
                    rgb_color = [255, 255, 255]  # 默认白色

                # 应用颜色到对应区域
                threshold_low = i / len(theme_colors)
                threshold_high = (i + 1) / len(theme_colors)
                mask_region = (noise >= threshold_low) & (noise < threshold_high)
                color_mask[mask_region] = rgb_color

            # 创建转场遮罩
            transition_mask = (noise < progress).astype(np.float32)
            transition_mask = np.stack([transition_mask] * 3, axis=2)

            # 应用转场
            result = frame1 * (1 - transition_mask) + frame2 * transition_mask

            # 添加颜色效果
            color_intensity = progress * 0.3
            result = result * (1 - color_intensity) + color_mask * color_intensity

            return result.astype(np.uint8)

        except Exception as e:
            self.logger.error(f"色彩溶解转场失败: {e}")
            return cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)

    def _fade_effect(self, frame1: np.ndarray, frame2: np.ndarray, progress: float) -> np.ndarray:
        """淡入淡出转场"""
        return cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)

    def _slide_effect(self, frame1: np.ndarray, frame2: np.ndarray, progress: float) -> np.ndarray:
        """滑动转场"""
        try:
            h, w = frame1.shape[:2]
            offset = int(w * progress)

            result = np.zeros_like(frame1)

            # 第一帧向左滑出
            if offset < w:
                result[:, :w-offset] = frame1[:, offset:]

            # 第二帧从右滑入
            if offset > 0:
                result[:, w-offset:] = frame2[:, :offset]

            return result

        except Exception as e:
            self.logger.error(f"滑动转场失败: {e}")
            return cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)

    def _zoom_effect(self, frame1: np.ndarray, frame2: np.ndarray, progress: float) -> np.ndarray:
        """缩放转场"""
        try:
            h, w = frame1.shape[:2]

            # 缩放第一帧
            scale = 1.0 + progress * 0.5
            new_h, new_w = int(h * scale), int(w * scale)

            if new_h > 0 and new_w > 0:
                scaled_frame1 = cv2.resize(frame1, (new_w, new_h))

                # 居中裁剪
                start_y = (new_h - h) // 2
                start_x = (new_w - w) // 2

                if start_y >= 0 and start_x >= 0:
                    cropped_frame1 = scaled_frame1[start_y:start_y+h, start_x:start_x+w]
                else:
                    cropped_frame1 = frame1
            else:
                cropped_frame1 = frame1

            return cv2.addWeighted(cropped_frame1, 1-progress, frame2, progress, 0)

        except Exception as e:
            self.logger.error(f"缩放转场失败: {e}")
            return cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)

    def _spin_effect(self, frame1: np.ndarray, frame2: np.ndarray, progress: float) -> np.ndarray:
        """旋转转场"""
        try:
            h, w = frame1.shape[:2]
            center = (w // 2, h // 2)
            angle = progress * 360

            # 旋转矩阵
            rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
            rotated_frame1 = cv2.warpAffine(frame1, rotation_matrix, (w, h))

            return cv2.addWeighted(rotated_frame1, 1-progress, frame2, progress, 0)

        except Exception as e:
            self.logger.error(f"旋转转场失败: {e}")
            return cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)

    def _wave_effect(self, frame1: np.ndarray, frame2: np.ndarray, progress: float) -> np.ndarray:
        """波浪转场"""
        try:
            h, w = frame1.shape[:2]
            result = frame1.copy()

            # 创建波浪效果
            amplitude = 20 * progress
            frequency = 0.1

            for y in range(h):
                offset = int(amplitude * np.sin(frequency * y))
                if 0 <= offset < w:
                    # 混合两帧
                    blend_ratio = (np.sin(frequency * y) + 1) / 2 * progress
                    result[y] = cv2.addWeighted(frame1[y], 1-blend_ratio, frame2[y], blend_ratio, 0)

            return result

        except Exception as e:
            self.logger.error(f"波浪转场失败: {e}")
            return cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)

    def _sparkle_effect(self, frame1: np.ndarray, frame2: np.ndarray, progress: float) -> np.ndarray:
        """闪光转场"""
        try:
            h, w = frame1.shape[:2]
            result = cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)

            # 添加闪光点
            num_sparkles = int(50 * progress)
            for _ in range(num_sparkles):
                x = np.random.randint(0, w)
                y = np.random.randint(0, h)
                size = np.random.randint(2, 8)

                # 白色闪光
                cv2.circle(result, (x, y), size, (255, 255, 255), -1)

            return result

        except Exception as e:
            self.logger.error(f"闪光转场失败: {e}")
            return cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)

    def _glow_effect(self, frame1: np.ndarray, frame2: np.ndarray, progress: float) -> np.ndarray:
        """光晕转场"""
        try:
            # 创建光晕效果
            glow_frame1 = cv2.GaussianBlur(frame1, (21, 21), 0)
            glow_frame2 = cv2.GaussianBlur(frame2, (21, 21), 0)

            # 混合原图和光晕
            enhanced_frame1 = cv2.addWeighted(frame1, 0.7, glow_frame1, 0.3, 0)
            enhanced_frame2 = cv2.addWeighted(frame2, 0.7, glow_frame2, 0.3, 0)

            return cv2.addWeighted(enhanced_frame1, 1-progress, enhanced_frame2, progress, 0)

        except Exception as e:
            self.logger.error(f"光晕转场失败: {e}")
            return cv2.addWeighted(frame1, 1-progress, frame2, progress, 0)

    # ==================== 主要处理方法 ====================

    def analyze_music_emotion(self, audio_path: str) -> str:
        """
        音乐情感分析 - 使用专业音乐分析器

        Args:
            audio_path: 音频文件路径

        Returns:
            情感类型: "JOY", "EXCITEMENT", "CALM"
        """
        try:
            # 使用专业音乐分析器
            if self.music_analyzer:
                music_structure = self.music_analyzer.analyze_music_structure(audio_path)
                emotion_info = music_structure['emotion']

                primary_emotion = emotion_info.get('primary_emotion', 'NEUTRAL')

                # 映射到SWANKSALON的情感类型
                emotion_mapping = {
                    'EXCITEMENT': 'EXCITEMENT',
                    'JOY': 'JOY',
                    'CALM': 'CALM',
                    'NEUTRAL': 'CALM'
                }

                return emotion_mapping.get(primary_emotion, 'CALM')
            else:
                # 降级到简单分析
                return self._simple_emotion_analysis(audio_path)

        except Exception as e:
            self.logger.error(f"音乐情感分析失败: {e}")
            # 降级到简单分析
            return self._simple_emotion_analysis(audio_path)

    def _simple_emotion_analysis(self, audio_path: str) -> str:
        """简单情感分析作为备选方案"""
        try:
            if LIBROSA_AVAILABLE:
                y, sr = librosa.load(audio_path)
                tempo, _ = librosa.beat.beat_track(y=y, sr=sr)

                if tempo > 130:
                    return "EXCITEMENT"
                elif tempo > 100:
                    return "JOY"
                else:
                    return "CALM"
            else:
                # 如果没有librosa，根据文件名或默认返回
                self.logger.warning("librosa不可用，使用默认情感分析")
                return "JOY"  # 默认返回愉悦
        except Exception as e:
            self.logger.error(f"简单情感分析失败: {e}")
            return "CALM"

    def select_transition(self, beat_type: str, emotion: str) -> str:
        """
        智能转场选择

        Args:
            beat_type: 节拍类型
            emotion: 情感类型

        Returns:
            转场效果名称
        """
        if beat_type == "STRONG":
            if emotion == "EXCITEMENT":
                return "scissor_light"
            elif emotion == "JOY":
                return "sparkle"
            else:
                return "glow"
        elif beat_type == "MEDIUM":
            if emotion == "EXCITEMENT":
                return "hair_particle"
            elif emotion == "JOY":
                return "color_dissolve"
            else:
                return "fade"
        else:  # WEAK
            if emotion == "EXCITEMENT":
                return "slide"
            elif emotion == "JOY":
                return "zoom"
            else:
                return "fade"

    def calculate_transition_duration(self, bpm: float) -> float:
        """
        根据BPM计算转场时长

        Args:
            bpm: 每分钟节拍数

        Returns:
            转场时长（秒）
        """
        if bpm <= 0:
            return 0.5

        beat_duration = 60.0 / bpm
        return max(0.2, min(beat_duration * 0.5, 1.0))

    def process_batch_with_beat_sync(self, output_dir: str) -> List[str]:
        """
        批量处理视频（使用踩点转场）

        Args:
            output_dir: 输出目录

        Returns:
            成功处理的文件列表
        """
        try:
            if not BEAT_SYNC_AVAILABLE or not self.beat_sync_engine:
                self.logger.warning("踩点转场系统不可用，降级到标准批量处理")
                return self.process_batch(output_dir)

            self.logger.info("开始批量踩点转场处理")

            # 准备批量任务
            videos = self.config.get('videos', [])
            music_path = self.config.get('music', {}).get('path', '')

            if not music_path or not os.path.exists(music_path):
                self.logger.error("音乐文件不存在，无法进行批量踩点处理")
                return []

            # 创建任务列表
            tasks = []
            for i, video_config in enumerate(videos):
                video_path = video_config.get('path', '')
                if os.path.exists(video_path):
                    tasks.append({
                        'video_path': video_path,
                        'music_path': music_path
                    })

            if not tasks:
                self.logger.warning("没有有效的视频文件")
                return []

            # 执行批量处理
            results = self.beat_sync_engine.batch_process(tasks, output_dir)

            # 统计结果
            successful_outputs = []
            for result in results:
                if result.success and os.path.exists(result.output_path):
                    successful_outputs.append(result.output_path)

            # 导出批量处理报告
            if results:
                report_path = os.path.join(output_dir, "batch_beat_sync_report.json")
                self.beat_sync_engine.export_processing_report(results, report_path)

            self.logger.info(f"批量踩点转场处理完成: {len(successful_outputs)}/{len(tasks)} 成功")
            return successful_outputs

        except Exception as e:
            self.logger.error(f"批量踩点转场处理失败: {e}")
            return []

    def process_batch(self, output_dir: str) -> List[str]:
        """
        批量处理主函数

        Args:
            output_dir: 输出目录

        Returns:
            成功处理的文件列表
        """
        try:
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)

            self.logger.info(f"开始批量处理，输出目录: {output_dir}")
            self.logger.info(f"待处理视频数量: {len(self.videos)}")

            successful_outputs = []

            # 并发处理
            max_workers = min(4, len(self.videos))  # 限制并发数

            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = []

                for i, video_config in enumerate(self.videos):
                    video_path = video_config.get('path', '')
                    if not video_path or not os.path.exists(video_path):
                        self.logger.warning(f"视频文件不存在: {video_path}")
                        continue

                    output_filename = f"swanksalon_output_{i+1:03d}.mp4"
                    output_path = os.path.join(output_dir, output_filename)

                    future = executor.submit(self.process_single, video_path, output_path)
                    futures.append((future, output_path, video_path))

                # 等待所有任务完成
                for future, output_path, video_path in futures:
                    try:
                        success = future.result(timeout=300)  # 5分钟超时
                        if success:
                            successful_outputs.append(output_path)
                            self.logger.info(f"✅ 处理成功: {os.path.basename(video_path)} -> {os.path.basename(output_path)}")
                        else:
                            self.logger.error(f"❌ 处理失败: {os.path.basename(video_path)}")
                    except concurrent.futures.TimeoutError:
                        self.logger.error(f"❌ 处理超时: {os.path.basename(video_path)}")
                    except Exception as e:
                        self.logger.error(f"❌ 处理异常: {os.path.basename(video_path)} - {e}")

            self.logger.info(f"批量处理完成，成功: {len(successful_outputs)}/{len(self.videos)}")
            return successful_outputs

        except Exception as e:
            self.logger.error(f"批量处理失败: {e}")
            return []

    def process_with_beat_sync(self, video_path: str, output_path: str) -> bool:
        """
        使用高精度踩点转场处理视频

        Args:
            video_path: 视频文件路径
            output_path: 输出文件路径

        Returns:
            是否成功
        """
        try:
            if not BEAT_SYNC_AVAILABLE or not self.beat_sync_engine:
                self.logger.warning("踩点转场系统不可用，降级到标准处理")
                return self.process_single(video_path, output_path)

            self.logger.info(f"开始高精度踩点转场处理: {video_path}")

            # 获取音乐文件路径
            music_path = self.config.get('music', {}).get('path', '')
            if not music_path or not os.path.exists(music_path):
                self.logger.error("音乐文件不存在，无法进行踩点处理")
                return False

            # 设置进度回调
            def progress_callback(progress, message):
                self.logger.info(f"踩点处理进度: {progress:.1%} - {message}")

            self.beat_sync_engine.set_progress_callback(progress_callback)

            # 执行踩点转场处理
            result = self.beat_sync_engine.process_video_with_music(
                video_path, music_path, output_path
            )

            if result.success:
                self.logger.info(
                    f"踩点转场处理成功: 用时={result.processing_time:.2f}s, "
                    f"质量评分={result.quality_metrics.get('overall_quality', 0):.2f}"
                )

                # 导出处理报告
                report_path = output_path.replace('.mp4', '_beat_sync_report.json')
                self.beat_sync_engine.export_processing_report([result], report_path)

                return True
            else:
                self.logger.error(f"踩点转场处理失败: {result.error_message}")
                return False

        except Exception as e:
            self.logger.error(f"踩点转场处理异常: {e}")
            return False

    def process_single(self, video_path: str, output_path: str) -> bool:
        """
        单个视频处理流程

        Args:
            video_path: 输入视频路径
            output_path: 输出视频路径

        Returns:
            处理是否成功
        """
        try:
            self.logger.info(f"开始处理视频: {os.path.basename(video_path)}")

            # 1. 检查输入文件
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"视频文件不存在: {video_path}")

            # 2. 分析视频素材
            self.logger.info("分析视频素材...")
            valid_frames = self.detect_face_poses(video_path)

            if not valid_frames:
                self.logger.warning("未检测到有效人脸帧，使用全部帧")
                # 获取视频总时长作为备选
                cap = cv2.VideoCapture(video_path)
                fps = cap.get(cv2.CAP_PROP_FPS)
                total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                cap.release()

                if fps > 0 and total_frames > 0:
                    duration = total_frames / fps
                    valid_frames = [i for i in np.linspace(0, duration-1, min(20, int(duration)))]

            # 3. 分析音乐
            music_path = self.music.get('path', '')
            if not music_path or not os.path.exists(music_path):
                self.logger.error("音乐文件不存在，无法进行踩点处理")
                return False

            self.logger.info("分析音乐结构...")
            beat_points = self.detect_beats(music_path)
            emotion = self.analyze_music_emotion(music_path)

            if not beat_points:
                self.logger.error("未检测到音乐节拍")
                return False

            # 4. 生成视频片段
            self.logger.info("生成视频片段...")
            segments = self._create_video_segments(video_path, valid_frames, beat_points)

            if not segments:
                self.logger.error("未能生成有效视频片段")
                return False

            # 5. 应用特效和转场
            self.logger.info("应用特效和转场...")
            processed_segments = self._apply_effects_to_segments(segments, beat_points, emotion)

            # 6. 合成最终视频
            self.logger.info("合成最终视频...")
            success = self._render_final_video(processed_segments, output_path, music_path)

            if success:
                self.logger.info(f"✅ 视频处理完成: {output_path}")
                return True
            else:
                self.logger.error("❌ 视频合成失败")
                return False

        except Exception as e:
            self.logger.error(f"单个视频处理失败: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            return False

    def _create_video_segments(self, video_path: str, valid_frames: List[float],
                             beat_points: List[float]) -> List[Dict[str, Any]]:
        """
        创建视频片段

        Args:
            video_path: 视频路径
            valid_frames: 有效帧时间戳
            beat_points: 节拍点

        Returns:
            视频片段列表
        """
        try:
            segments = []

            # 计算每个片段的时长
            for i in range(len(beat_points) - 1):
                start_beat = beat_points[i]
                end_beat = beat_points[i + 1]
                segment_duration = end_beat - start_beat

                # 从有效帧中选择最佳片段
                best_start_time = self._find_best_segment_start(valid_frames, segment_duration)

                segment = {
                    'video_path': video_path,
                    'start_time': best_start_time,
                    'duration': segment_duration,
                    'beat_index': i,
                    'beat_type': self.classify_beat(i, len(beat_points), 120)  # 假设120 BPM
                }

                segments.append(segment)

            self.logger.info(f"创建了 {len(segments)} 个视频片段")
            return segments

        except Exception as e:
            self.logger.error(f"创建视频片段失败: {e}")
            return []

    def _find_best_segment_start(self, valid_frames: List[float], duration: float) -> float:
        """
        找到最佳片段开始时间

        Args:
            valid_frames: 有效帧时间戳
            duration: 片段时长

        Returns:
            最佳开始时间
        """
        if not valid_frames:
            return 0.0

        # 随机选择一个有效帧作为开始点
        import random
        return random.choice(valid_frames)

    def _apply_effects_to_segments(self, segments: List[Dict[str, Any]],
                                 beat_points: List[float], emotion: str) -> List[Dict[str, Any]]:
        """
        为片段应用特效

        Args:
            segments: 视频片段列表
            beat_points: 节拍点
            emotion: 音乐情感

        Returns:
            处理后的片段列表
        """
        try:
            processed_segments = []

            for segment in segments:
                # 选择转场效果
                beat_type = segment['beat_type']
                transition_type = self.select_transition(beat_type, emotion)

                # 添加特效信息
                segment['transition_type'] = transition_type
                segment['beauty_filter'] = True
                segment['hair_enhancement'] = beat_type == "STRONG"

                processed_segments.append(segment)

            return processed_segments

        except Exception as e:
            self.logger.error(f"应用特效失败: {e}")
            return segments

    def _render_final_video(self, segments: List[Dict[str, Any]],
                          output_path: str, music_path: str) -> bool:
        """
        渲染最终视频

        Args:
            segments: 处理后的片段列表
            output_path: 输出路径
            music_path: 音乐路径

        Returns:
            渲染是否成功
        """
        try:
            if not segments:
                return False

            # 检查moviepy是否可用
            if not MOVIEPY_AVAILABLE:
                self.logger.error("moviepy不可用，无法进行视频合成")
                return False

            # 使用moviepy进行视频合成
            from moviepy.editor import VideoFileClip, AudioFileClip, concatenate_videoclips

            clips = []

            for segment in segments:
                # 加载视频片段
                video_clip = VideoFileClip(segment['video_path'])

                # 剪切片段
                start_time = segment['start_time']
                duration = segment['duration']
                end_time = start_time + duration

                # 确保不超出视频长度
                if end_time > video_clip.duration:
                    end_time = video_clip.duration
                    duration = end_time - start_time

                if duration <= 0:
                    continue

                clip = video_clip.subclip(start_time, end_time)

                # 调整时长以匹配节拍
                if abs(clip.duration - duration) > 0.1:
                    clip = clip.fx(lambda c: c.set_duration(duration))

                clips.append(clip)
                video_clip.close()

            if not clips:
                self.logger.error("没有有效的视频片段")
                return False

            # 合并视频片段
            final_video = concatenate_videoclips(clips, method="compose")

            # 添加背景音乐
            if os.path.exists(music_path):
                audio_clip = AudioFileClip(music_path)

                # 调整音频长度匹配视频
                if audio_clip.duration > final_video.duration:
                    audio_clip = audio_clip.subclip(0, final_video.duration)
                elif audio_clip.duration < final_video.duration:
                    # 循环音频
                    loops_needed = int(np.ceil(final_video.duration / audio_clip.duration))
                    audio_clips = [audio_clip] * loops_needed
                    audio_clip = concatenate_videoclips(audio_clips).subclip(0, final_video.duration)

                # 设置音频音量
                volume = self.music.get('volume', 0.6)
                audio_clip = audio_clip.volumex(volume)

                final_video = final_video.set_audio(audio_clip)
                audio_clip.close()

            # 输出视频
            output_config = self.output_config
            fps = output_config.get('frame_rate', 30)

            final_video.write_videofile(
                output_path,
                fps=fps,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True,
                verbose=False,
                logger=None
            )

            # 清理资源
            final_video.close()
            for clip in clips:
                clip.close()

            return True

        except Exception as e:
            self.logger.error(f"视频渲染失败: {e}")
            return False

    def get_processing_progress(self) -> Dict[str, Any]:
        """
        获取处理进度信息

        Returns:
            进度信息字典
        """
        return {
            'total_videos': len(self.videos),
            'completed_videos': 0,  # 这里需要实际的进度跟踪
            'current_stage': 'idle',
            'estimated_time_remaining': 0
        }

    def cleanup_temp_files(self):
        """清理临时文件"""
        try:
            temp_dir = Path("temp")
            if temp_dir.exists():
                import shutil
                shutil.rmtree(temp_dir)
                self.logger.info("临时文件清理完成")
        except Exception as e:
            self.logger.error(f"清理临时文件失败: {e}")


# ==================== 配置验证和工具函数 ====================

def validate_swanksalon_config(config: Dict[str, Any]) -> bool:
    """
    验证SWANKSALON配置

    Args:
        config: 配置字典

    Returns:
        配置是否有效
    """
    required_keys = ['template', 'music', 'videos', 'output']

    for key in required_keys:
        if key not in config:
            print(f"❌ 缺少必需的配置项: {key}")
            return False

    # 验证音乐配置
    music_config = config['music']
    if 'path' not in music_config:
        print("❌ 音乐配置缺少路径")
        return False

    if not os.path.exists(music_config['path']):
        print(f"❌ 音乐文件不存在: {music_config['path']}")
        return False

    # 验证视频配置
    videos = config['videos']
    if not videos:
        print("❌ 没有配置视频文件")
        return False

    for i, video in enumerate(videos):
        if 'path' not in video:
            print(f"❌ 视频 {i+1} 缺少路径")
            return False

        if not os.path.exists(video['path']):
            print(f"❌ 视频文件不存在: {video['path']}")
            return False

    print("✅ 配置验证通过")
    return True


def create_default_swanksalon_config() -> Dict[str, Any]:
    """
    创建默认的SWANKSALON配置

    Returns:
        默认配置字典
    """
    return {
        "project": "SwankSalon_Project",
        "template": {
            "id": "SALON_2024",
            "name": "理发店时尚模板",
            "duration": 30.0,
            "theme_colors": ["#FF3366", "#33CCFF"],
            "transitions": [
                {"type": "scissor_light", "position": 5.0},
                {"type": "hair_particle", "position": 15.0},
                {"type": "color_dissolve", "position": 25.0}
            ],
            "beauty_params": {
                "skin_smooth": 0.7,
                "face_slim": 0.5,
                "hair_boost": ["red", "blue", "purple"],
                "min_face_confidence": 0.8
            }
        },
        "music": {
            "path": "",  # 用户需要设置
            "bpm": 120,
            "volume": 0.6,
            "beat_points": []  # 自动检测
        },
        "videos": [],  # 用户需要添加
        "output": {
            "resolution": "1080p",
            "frame_rate": 30,
            "directory": "output_videos",
            "watermark": {
                "enabled": False,
                "path": "",
                "position": "bottom-right",
                "opacity": 0.7
            }
        }
    }
